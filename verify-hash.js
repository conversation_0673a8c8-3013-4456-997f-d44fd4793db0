const crypto = require('crypto');

// 计算"admin123"的SHA-256哈希值
const sha256Hash = crypto.createHash('sha256').update('admin123').digest('hex');
console.log('SHA-256 Hash of "admin123":', sha256Hash);

// 计算"admin123"的SHA-1哈希值
const sha1Hash = crypto.createHash('sha1').update('admin123').digest('hex');
console.log('SHA-1 Hash of "admin123":', sha1Hash);

// 验证seed-config.js中的哈希值
const configHash = '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9';
console.log('Hash in seed-config.js:', configHash);
console.log('Does SHA-256 hash match config hash?', sha256Hash === configHash);

// 验证旧的哈希值
const oldConfigHash = 'f865b53623b121fd34ee5426c792e5c33af8c227';
console.log('Old hash in seed-config.js:', oldConfigHash);
console.log('Does SHA-1 hash match old config hash?', sha1Hash === oldConfigHash);
