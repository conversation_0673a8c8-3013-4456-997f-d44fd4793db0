#!/usr/bin/env node

/**
 * 语法错误修复脚本
 * 专门处理字段映射、注释格式、控制器语法等问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复语法错误...\n');

// 语法修复规则
const syntaxFixRules = [
  // 修复字段映射问题
  {
    name: '修复Schema字段映射',
    pattern: /.*\.service\.ts$/,
    fixes: [
      { from: /orderBy: { createdAt: "desc" }/g, to: 'orderBy: { createTime: "desc" }' },
      { from: /orderBy: { createdAt: 'desc' }/g, to: "orderBy: { createTime: 'desc' }" },
      { from: /orderBy: { updatedAt: "desc" }/g, to: 'orderBy: { updateTime: "desc" }' },
      { from: /orderBy: { updatedAt: 'desc' }/g, to: "orderBy: { updateTime: 'desc' }" },
      { from: /"desc"/g, to: "'desc'" },
      { from: /"asc"/g, to: "'asc'" }
    ]
  },

  // 修复注释导致的语法错误
  {
    name: '修复注释语法错误',
    pattern: /.*\.(controller|service)\.ts$/,
    fixes: [
      { from: /\/\/ tenantId, \/\/ not in schema/g, to: '// tenantId // not in schema' },
      { from: /user\.tenantId, \/\/ tenantId from user/g, to: 'user.tenantId // tenantId from user' },
      { from: /Promise<any \/\/ WebsiteTemplateEntity not exist>/g, to: 'Promise<any>' },
      { from: /Promise<any \/\/ WebsiteFormEntity not exist>/g, to: 'Promise<any>' },
      { from: /Promise<any \/\/ WebsiteSeoEntity not exist>/g, to: 'Promise<any>' },
      { from: /return \/\/ this\./g, to: 'throw new Error("Method not implemented"); // return this.' }
    ]
  },

  // 修复控制器方法签名
  {
    name: '修复控制器方法语法',
    pattern: /.*\.controller\.ts$/,
    fixes: [
      { from: /@ApiCompleteResponse\(any \/\/ WebsiteTemplateEntity not exist,/g, to: '@ApiCompleteResponse(Object,' },
      { from: /@ApiCompleteResponse\(any \/\/ WebsiteFormEntity not exist,/g, to: '@ApiCompleteResponse(Object,' },
      { from: /@ApiCompleteResponse\(any \/\/ WebsiteSeoEntity not exist,/g, to: '@ApiCompleteResponse(Object,' },
    ]
  },

  // 修复类型错误
  {
    name: '修复类型定义错误',
    pattern: /.*\.(service|controller)\.ts$/,
    fixes: [
      { from: /tags: string\[\],/g, to: 'tags: string[]' },
      { from: /: Promise<string\[\]>/g, to: ': Promise<string[]>' }
    ]
  }
];

// 特殊文件修复规则
const specialFileRules = [
  // website-template.entity.ts 重复metadata字段
  {
    name: '修复WebsiteTemplate Entity重复字段',
    pattern: /website-template\.entity\.ts$/,
    content: `import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsObject, IsDate } from 'class-validator';

export class WebsiteTemplateEntity {
  @ApiProperty({ description: '模板ID' })
  id: number;

  @ApiProperty({ description: '模板名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '模板状态', default: 'active' })
  @IsString()
  status: string;

  @ApiProperty({ description: '元数据', type: 'object', required: false })
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '租户ID', required: false })
  @IsOptional()
  tenantId?: number;

  @ApiProperty({ description: '创建时间' })
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;
}`
  }
];

// 处理特殊文件
function processSpecialFiles() {
  for (const rule of specialFileRules) {
    const files = findFiles(rule.pattern);
    for (const filePath of files) {
      try {
        fs.writeFileSync(filePath, rule.content, 'utf8');
        console.log(`✅ 特殊修复: ${path.relative(process.cwd(), filePath)}`);
      } catch (error) {
        console.error(`❌ 特殊修复失败: ${filePath} - ${error.message}`);
      }
    }
  }
}

// 查找匹配的文件
function findFiles(pattern) {
  const files = [];

  function searchDir(dirPath) {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !['node_modules', '.git', 'dist'].includes(item)) {
        searchDir(fullPath);
      } else if (stat.isFile() && pattern.test(fullPath)) {
        files.push(fullPath);
      }
    }
  }

  searchDir(path.join(process.cwd(), 'src'));
  return files;
}

// 处理文件
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    for (const rule of syntaxFixRules) {
      if (rule.pattern.test(filePath)) {
        for (const fix of rule.fixes) {
          const newContent = content.replace(fix.from, fix.to);
          if (newContent !== content) {
            content = newContent;
            hasChanges = true;
          }
        }
      }
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 语法修复: ${path.relative(process.cwd(), filePath)}`);
    }
  } catch (error) {
    console.error(`❌ 处理失败: ${filePath} - ${error.message}`);
  }
}

// 递归处理目录
function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);

  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !['node_modules', '.git', 'dist'].includes(item)) {
      processDirectory(fullPath);
    } else if (stat.isFile() && path.extname(item) === '.ts') {
      processFile(fullPath);
    }
  }
}

// 执行修复
console.log('📂 处理特殊文件...');
processSpecialFiles();

console.log('📂 处理语法错误...');
processDirectory(path.join(process.cwd(), 'src'));

console.log('\n🎯 语法错误修复完成！');
