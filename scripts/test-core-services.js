#!/usr/bin/env node

/**
 * 核心服务测试脚本
 *
 * 验证Phase 1 Sprint 1.3的核心服务重构是否正常工作：
 * - 数据库工厂
 * - 服务层架构
 * - API路由结构
 * - 中间件增强
 * - 缓存策略
 */

console.log('🚀 开始测试核心服务重构...\n');

// 模拟测试数据
const testResults = [];

function addTestResult(testName, status, details = '') {
  testResults.push({ testName, status, details });
  const icon = status === 'PASS' ? '✅' : status === 'WARN' ? '⚠️' : '❌';
  console.log(`${icon} ${testName}${details ? `: ${details}` : ''}`);
}

// 1. 测试数据库工厂架构
console.log('1️⃣ 测试数据库工厂架构');
try {
  // 检查数据库客户端文件
  const fs = require('fs');
  const path = require('path');

  const publicClientPath = path.join(__dirname, '../src/core/database/clients/public.client.ts');
  const tenantClientPath = path.join(__dirname, '../src/core/database/clients/tenant.client.ts');
  const factoryPath = path.join(__dirname, '../src/core/database/database.factory.ts');

  if (fs.existsSync(publicClientPath)) {
    addTestResult('PublicDatabaseClient', 'PASS', '文件存在');
  } else {
    addTestResult('PublicDatabaseClient', 'FAIL', '文件不存在');
  }

  if (fs.existsSync(tenantClientPath)) {
    addTestResult('TenantDatabaseClient', 'PASS', '文件存在');
  } else {
    addTestResult('TenantDatabaseClient', 'FAIL', '文件不存在');
  }

  if (fs.existsSync(factoryPath)) {
    addTestResult('DatabaseFactory', 'PASS', '文件存在');
  } else {
    addTestResult('DatabaseFactory', 'FAIL', '文件不存在');
  }
} catch (error) {
  addTestResult('数据库工厂检查', 'FAIL', error.message);
}

// 2. 测试服务层架构
console.log('\n2️⃣ 测试服务层架构');
try {
  const fs = require('fs');
  const path = require('path');

  const platformServicePath = path.join(
    __dirname,
    '../src/modules/platform/base/platform.service.ts',
  );
  const businessServicePath = path.join(
    __dirname,
    '../src/modules/business/base/business.service.ts',
  );
  const tenantManagementPath = path.join(
    __dirname,
    '../src/modules/platform/tenant-management/tenant-management.service.ts',
  );
  const userManagementPath = path.join(
    __dirname,
    '../src/modules/business/user-management/user-management.service.ts',
  );

  if (fs.existsSync(platformServicePath)) {
    addTestResult('BasePlatformService', 'PASS', '基础平台服务类');
  } else {
    addTestResult('BasePlatformService', 'FAIL', '文件不存在');
  }

  if (fs.existsSync(businessServicePath)) {
    addTestResult('BaseBusinessService', 'PASS', '基础业务服务类');
  } else {
    addTestResult('BaseBusinessService', 'FAIL', '文件不存在');
  }

  if (fs.existsSync(tenantManagementPath)) {
    addTestResult('TenantManagementService', 'PASS', '租户管理服务');
  } else {
    addTestResult('TenantManagementService', 'FAIL', '文件不存在');
  }

  if (fs.existsSync(userManagementPath)) {
    addTestResult('UserManagementService', 'PASS', '用户管理服务');
  } else {
    addTestResult('UserManagementService', 'FAIL', '文件不存在');
  }
} catch (error) {
  addTestResult('服务层架构检查', 'FAIL', error.message);
}

// 3. 测试API结构
console.log('\n3️⃣ 测试API结构');
try {
  const fs = require('fs');
  const path = require('path');

  const platformControllerPath = path.join(
    __dirname,
    '../src/modules/platform/tenant-management/tenant-management.controller.ts',
  );
  const businessControllerPath = path.join(
    __dirname,
    '../src/modules/business/user-management/user-management.controller.ts',
  );

  if (fs.existsSync(platformControllerPath)) {
    const controllerContent = fs.readFileSync(platformControllerPath, 'utf8');
    if (controllerContent.includes("@Controller('platform/tenants')")) {
      addTestResult('平台管理API', 'PASS', '/platform/tenants 路由');
    } else {
      addTestResult('平台管理API', 'WARN', '路由配置可能不正确');
    }
  } else {
    addTestResult('平台管理API', 'FAIL', '控制器文件不存在');
  }

  if (fs.existsSync(businessControllerPath)) {
    const controllerContent = fs.readFileSync(businessControllerPath, 'utf8');
    if (controllerContent.includes("@Controller('api/users')")) {
      addTestResult('业务功能API', 'PASS', '/api/users 路由');
    } else {
      addTestResult('业务功能API', 'WARN', '路由配置可能不正确');
    }
  } else {
    addTestResult('业务功能API', 'FAIL', '控制器文件不存在');
  }
} catch (error) {
  addTestResult('API结构检查', 'FAIL', error.message);
}

// 4. 测试中间件和装饰器
console.log('\n4️⃣ 测试中间件和装饰器');
try {
  const fs = require('fs');
  const path = require('path');

  const middlewarePath = path.join(
    __dirname,
    '../src/core/middleware/tenant-resolution.middleware.ts',
  );
  const decoratorPath = path.join(__dirname, '../src/core/decorators/tenant-context.decorator.ts');

  if (fs.existsSync(middlewarePath)) {
    addTestResult('TenantResolutionMiddleware', 'PASS', '租户识别中间件');
  } else {
    addTestResult('TenantResolutionMiddleware', 'FAIL', '文件不存在');
  }

  if (fs.existsSync(decoratorPath)) {
    addTestResult('TenantContext装饰器', 'PASS', '租户上下文装饰器');
  } else {
    addTestResult('TenantContext装饰器', 'FAIL', '文件不存在');
  }
} catch (error) {
  addTestResult('中间件检查', 'FAIL', error.message);
}

// 5. 测试缓存策略
console.log('\n5️⃣ 测试缓存策略');
try {
  const fs = require('fs');
  const path = require('path');

  const cachePath = path.join(__dirname, '../src/core/cache/tenant-config.cache.ts');

  if (fs.existsSync(cachePath)) {
    addTestResult('TenantConfigCache', 'PASS', '租户配置缓存');
  } else {
    addTestResult('TenantConfigCache', 'FAIL', '文件不存在');
  }
} catch (error) {
  addTestResult('缓存策略检查', 'FAIL', error.message);
}

// 6. 测试工具类和常量
console.log('\n6️⃣ 测试工具类和常量');
try {
  const fs = require('fs');
  const path = require('path');

  const dataMapperPath = path.join(__dirname, '../src/core/common/utils/data-mapper.util.ts');
  const modelConstantPath = path.join(__dirname, '../src/core/common/constants/model.constant.ts');
  const baseDtoPath = path.join(__dirname, '../src/core/common/base/base-dto.ts');

  if (fs.existsSync(dataMapperPath)) {
    addTestResult('DataMapperUtil', 'PASS', '数据映射工具');
  } else {
    addTestResult('DataMapperUtil', 'FAIL', '文件不存在');
  }

  if (fs.existsSync(modelConstantPath)) {
    addTestResult('ModelConstant', 'PASS', '模型常量定义');
  } else {
    addTestResult('ModelConstant', 'FAIL', '文件不存在');
  }

  if (fs.existsSync(baseDtoPath)) {
    addTestResult('BaseDTO', 'PASS', '基础DTO类');
  } else {
    addTestResult('BaseDTO', 'FAIL', '文件不存在');
  }
} catch (error) {
  addTestResult('工具类检查', 'FAIL', error.message);
}

// 7. 验证架构原则
console.log('\n7️⃣ 验证架构原则');

// 统计测试结果
const passCount = testResults.filter(r => r.status === 'PASS').length;
const warnCount = testResults.filter(r => r.status === 'WARN').length;
const failCount = testResults.filter(r => r.status === 'FAIL').length;
const totalCount = testResults.length;

if (passCount >= totalCount * 0.8) {
  addTestResult('架构完整性', 'PASS', `${passCount}/${totalCount} 项通过`);
} else if (passCount >= totalCount * 0.6) {
  addTestResult('架构完整性', 'WARN', `${passCount}/${totalCount} 项通过，需要完善`);
} else {
  addTestResult('架构完整性', 'FAIL', `${passCount}/${totalCount} 项通过，架构不完整`);
}

// 检查关键原则
const hasClearSeparation =
  passCount > 0 &&
  testResults.some(r => r.testName.includes('Platform') && r.status === 'PASS') &&
  testResults.some(r => r.testName.includes('Business') && r.status === 'PASS');

if (hasClearSeparation) {
  addTestResult('职责分离原则', 'PASS', '平台/业务服务清晰分离');
} else {
  addTestResult('职责分离原则', 'FAIL', '服务分离不够清晰');
}

// 输出测试总结
console.log('\n' + '='.repeat(50));
console.log('🎯 测试结果总结');
console.log('='.repeat(50));
console.log(`✅ 通过: ${passCount} 项`);
console.log(`⚠️  警告: ${warnCount} 项`);
console.log(`❌ 失败: ${failCount} 项`);
console.log(`📊 总计: ${totalCount} 项`);

const successRate = Math.round((passCount / totalCount) * 100);
console.log(`🎯 成功率: ${successRate}%`);

console.log('\n📋 架构评估:');
if (successRate >= 90) {
  console.log('🏆 优秀：架构重构完成度很高，符合设计要求');
} else if (successRate >= 80) {
  console.log('✅ 良好：架构重构基本完成，少量细节需要完善');
} else if (successRate >= 70) {
  console.log('⚠️  一般：架构重构进度正常，需要继续完善核心功能');
} else {
  console.log('❌ 需要改进：架构重构进度不足，需要重点关注失败项');
}

console.log('\n🚀 下一步行动:');
if (failCount > 0) {
  console.log('1. 优先完成失败项的开发');
}
if (warnCount > 0) {
  console.log('2. 检查并优化警告项');
}
console.log('3. 进行集成测试和性能测试');
console.log('4. 更新文档和API规范');

console.log('\n✨ Phase 1 Sprint 1.3 核心服务重构测试完成！');
