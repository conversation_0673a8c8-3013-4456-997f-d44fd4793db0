#!/usr/bin/env node

/**
 * 服务方法批量修复脚本 V2
 * 解决缺失方法、依赖注入、参数类型问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始批量修复服务方法...\n');

// 服务方法补全模板
const serviceMethodTemplates = {
  // MediaAssetService 缺失方法
  'src/modules/media-asset/media-asset.service.ts': {
    methods: [
      `
  /**
   * 获取媒体统计信息
   */
  async getMediaStats(tenantId: number) {
    throw new Error('Method not implemented - getMediaStats');
  }

  /**
   * 获取媒体分类列表
   */
  async getCategories(tenantId: number): Promise<string[]> {
    throw new Error('Method not implemented - getCategories');
  }

  /**
   * 获取媒体标签列表
   */
  async getTags(tenantId: number): Promise<string[]> {
    throw new Error('Method not implemented - getTags');
  }

  /**
   * 根据分类获取媒体
   */
  async getByCategory(tenantId: number, category: string) {
    throw new Error('Method not implemented - getByCategory');
  }

  /**
   * 根据标签获取媒体
   */
  async getByTags(tenantId: number, tags: string[]) {
    throw new Error('Method not implemented - getByTags');
  }`
    ]
  },

  // ComponentLibraryService 缺失方法
  'src/modules/component-library/component-library.service.ts': {
    methods: [
      `
  /**
   * 获取组件分类
   */
  async getCategories(tenantId: number): Promise<string[]> {
    throw new Error('Method not implemented - getCategories');
  }

  /**
   * 根据分类获取组件
   */
  async getComponentsByCategory(tenantId: number, category: string) {
    throw new Error('Method not implemented - getComponentsByCategory');
  }

  /**
   * 查找单个组件
   */
  async findOne(tenantId: number, id: number, userId: number) {
    throw new Error('Method not implemented - findOne');
  }

  /**
   * 更新组件
   */
  async update(tenantId: number, id: number, updateDto: any, userId: number) {
    throw new Error('Method not implemented - update');
  }

  /**
   * 删除组件
   */
  async remove(tenantId: number, id: number, userId: number) {
    throw new Error('Method not implemented - remove');
  }

  /**
   * 复制组件
   */
  async duplicate(tenantId: number, id: number, newName: string, userId: number) {
    throw new Error('Method not implemented - duplicate');
  }

  /**
   * 切换激活状态
   */
  async toggleActive(tenantId: number, id: number, userId: number) {
    throw new Error('Method not implemented - toggleActive');
  }`
    ]
  }
};

// 依赖注入修复规则
const dependencyFixRules = [
  // 修复PublicPrismaService引用
  {
    name: '修复PublicPrismaService引用',
    pattern: /.*\.(service|strategy)\.ts$/,
    fixes: [
      { from: /import.*PublicPrismaService.*\n/g, to: '// import PublicPrismaService - using DatabaseFactory\n' },
      { from: /PublicPrismaService/g, to: 'DatabaseFactory' },
      { from: /private readonly prisma: DatabaseFactory/g, to: 'private readonly databaseFactory: DatabaseFactory' },
      { from: /constructor\(\s*private readonly prisma: DatabaseFactory/g, to: 'constructor(private readonly databaseFactory: DatabaseFactory' },
      { from: /this\.prisma\./g, to: 'this.databaseFactory.' }
    ]
  },

  // 修复TENANT_PRISMA_SERVICE引用
  {
    name: '修复TENANT_PRISMA_SERVICE引用',
    pattern: /.*\.(service|strategy)\.ts$/,
    fixes: [
      { from: /TENANT_PRISMA_SERVICE/g, to: "'DATABASE_FACTORY'" },
      { from: /@Inject\('DATABASE_FACTORY'\)/g, to: '' },
      { from: /private readonly tenantPrisma: any/g, to: 'private readonly databaseFactory: DatabaseFactory' }
    ]
  },

  // 修复参数类型问题
  {
    name: '修复参数类型问题',
    pattern: /.*\.controller\.ts$/,
    fixes: [
      { from: /@Param\('id', ParseIntPipe\) id: number.*categoryId: number/g, to: "@Param('id', ParseIntPipe) id: number, @Param('categoryId') categoryId: string" },
      { from: /@Param\('websiteId', ParseIntPipe\) websiteId: number/g, to: "@Param('websiteId') websiteId: string" },
      { from: /tenantId: number.*categoryId: number/g, to: 'tenantId: number, categoryId: string' }
    ]
  },

  // 修复语法标识符错误
  {
    name: '修复语法标识符错误',
    pattern: /.*\.service\.ts$/,
    fixes: [
      { from: /}\s*\)\s*;\s*}\s*\)/gm, to: '})' },
      { from: /}\s*\)\s*;\s*$/gm, to: '})' },
      { from: /\s*\)\s*;\s*}\s*$/gm, to: ' })' }
    ]
  }
];

// 应用服务方法补全
function applyServiceMethodTemplates() {
  let count = 0;
  for (const [filePath, template] of Object.entries(serviceMethodTemplates)) {
    try {
      if (fs.existsSync(filePath)) {
        let content = fs.readFileSync(filePath, 'utf8');

        // 在最后一个 } 之前插入方法
        const lastBraceIndex = content.lastIndexOf('}');
        if (lastBraceIndex > 0) {
          const beforeBrace = content.substring(0, lastBraceIndex);
          const afterBrace = content.substring(lastBraceIndex);

          content = beforeBrace + template.methods[0] + '\n' + afterBrace;

          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`✅ 补全服务方法: ${path.relative(process.cwd(), filePath)}`);
          count++;
        }
      }
    } catch (error) {
      console.error(`❌ 补全失败: ${filePath} - ${error.message}`);
    }
  }
  return count;
}

// 应用依赖修复规则
function applyDependencyFixes() {
  let count = 0;

  function processFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;

      for (const rule of dependencyFixRules) {
        if (rule.pattern.test(filePath)) {
          for (const fix of rule.fixes) {
            const newContent = content.replace(fix.from, fix.to);
            if (newContent !== content) {
              content = newContent;
              hasChanges = true;
            }
          }
        }
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ 依赖修复: ${path.relative(process.cwd(), filePath)}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`❌ 依赖修复失败: ${filePath} - ${error.message}`);
      return false;
    }
  }

  function processDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !['node_modules', '.git', 'dist'].includes(item)) {
        count += processDirectory(fullPath);
      } else if (stat.isFile() && path.extname(item) === '.ts') {
        if (processFile(fullPath)) {
          count++;
        }
      }
    }

    return count;
  }

  return processDirectory(path.join(process.cwd(), 'src'));
}

// 修复特定文件的语法错误
function fixSpecificFiles() {
  const fixes = [
    // 修复 logging.service.ts 语法错误
    {
      file: 'src/core/logging/logging.service.ts',
      fix: (content) => {
        // 修复第154行语法错误
        return content.replace(/}\s*\)\s*;\s*}\s*\)/gm, '})');
      }
    },

    // 修复 department.controller.ts 参数类型
    {
      file: 'src/modules/department/department.controller.ts',
      fix: (content) => {
        return content.replace(
          /@Param\('categoryId', ParseIntPipe\) categoryId: number/g,
          "@Param('categoryId') categoryId: string"
        );
      }
    }
  ];

  let count = 0;
  for (const { file, fix } of fixes) {
    try {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        const newContent = fix(content);

        if (newContent !== content) {
          fs.writeFileSync(file, newContent, 'utf8');
          console.log(`✅ 特定修复: ${path.relative(process.cwd(), file)}`);
          count++;
        }
      }
    } catch (error) {
      console.error(`❌ 特定修复失败: ${file} - ${error.message}`);
    }
  }

  return count;
}

// 执行修复
console.log('📂 补全服务方法...');
const methodCount = applyServiceMethodTemplates();

console.log('📂 修复依赖注入...');
const dependencyCount = applyDependencyFixes();

console.log('📂 修复特定文件...');
const specificCount = fixSpecificFiles();

console.log(`\n✅ 批量修复完成！`);
console.log(`🔧 服务方法补全: ${methodCount} 个文件`);
console.log(`📋 依赖注入修复: ${dependencyCount} 个文件`);
console.log(`🎯 特定文件修复: ${specificCount} 个文件`);
console.log('�� 下一步: 编译检查错误减少情况');
