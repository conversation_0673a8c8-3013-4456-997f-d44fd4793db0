#!/usr/bin/env node

/**
 * 批量修复Prisma服务引用脚本
 *
 * 将旧的PublicPrismaService和TenantPrismaService引用
 * 替换为新的DatabaseFactory架构
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始批量修复Prisma服务引用...\n');

// 定义替换规则
const replacements = [
  // PublicPrismaService 引用替换
  {
    name: 'PublicPrismaService Import',
    from: /import\s*{\s*PublicPrismaService\s*}\s*from\s*['"][^'"]*public-prisma\.service['"];?\s*/g,
    to: "import { DatabaseFactory } from '@/core/database/database.factory';\n",
  },

  // TenantPrismaService 引用替换
  {
    name: 'TenantPrismaService Import',
    from: /import\s*{\s*TenantPrismaService\s*}\s*from\s*['"][^'"]*tenant-prisma\.service['"];?\s*/g,
    to: "import { DatabaseFactory } from '@/core/database/database.factory';\n",
  },

  // TENANT_PRISMA_SERVICE token 引用替换
  {
    name: 'TENANT_PRISMA_SERVICE Import',
    from: /import\s*{\s*TENANT_PRISMA_SERVICE\s*}\s*from\s*['"][^'"]*tenant-prisma\.service['"];?\s*/g,
    to: "import { DatabaseFactory } from '@/core/database/database.factory';\n",
  },

  // 组合引用替换
  {
    name: 'Combined Prisma Service Import',
    from: /import\s*{\s*(TenantPrismaService|TENANT_PRISMA_SERVICE)\s*,?\s*}\s*from\s*['"][^'"]*tenant-prisma\.service['"];?\s*/g,
    to: "import { DatabaseFactory } from '@/core/database/database.factory';\n",
  },
];

// 需要处理的文件扩展名
const fileExtensions = ['.ts', '.js'];

// 需要跳过的目录
const skipDirs = ['node_modules', '.git', 'dist', 'coverage', 'docs'];

// 统计信息
let totalFiles = 0;
let modifiedFiles = 0;
const modifiedFilesList = [];

/**
 * 递归搜索并处理文件
 */
function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);

  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      // 跳过指定目录
      if (skipDirs.includes(item)) {
        continue;
      }
      processDirectory(fullPath);
    } else if (stat.isFile()) {
      // 处理符合条件的文件
      const ext = path.extname(item);
      if (fileExtensions.includes(ext)) {
        processFile(fullPath);
      }
    }
  }
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  totalFiles++;

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let hasChanges = false;

    // 应用所有替换规则
    for (const replacement of replacements) {
      const newContent = content.replace(replacement.from, replacement.to);
      if (newContent !== content) {
        console.log(`  ✅ ${replacement.name}: ${path.relative(process.cwd(), filePath)}`);
        content = newContent;
        hasChanges = true;
      }
    }

    // 如果有变更，写回文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      modifiedFiles++;
      modifiedFilesList.push(path.relative(process.cwd(), filePath));
    }
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath}`, error.message);
  }
}

/**
 * 主函数
 */
function main() {
  const startTime = Date.now();

  // 从src目录开始处理
  const srcPath = path.join(process.cwd(), 'src');
  if (fs.existsSync(srcPath)) {
    console.log('📂 处理 src/ 目录...');
    processDirectory(srcPath);
  }

  // 处理根目录下的特定文件
  const rootFiles = ['prisma/seed.ts', 'prisma/seeds/component-library.seed.ts'];
  for (const file of rootFiles) {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      console.log(`📂 处理 ${file}...`);
      processFile(filePath);
    }
  }

  const endTime = Date.now();

  // 输出统计信息
  console.log('\n' + '='.repeat(60));
  console.log('📊 修复完成统计');
  console.log('='.repeat(60));
  console.log(`⏱️  处理时间: ${endTime - startTime}ms`);
  console.log(`📁 扫描文件: ${totalFiles} 个`);
  console.log(`✏️  修改文件: ${modifiedFiles} 个`);

  if (modifiedFiles > 0) {
    console.log('\n📝 修改的文件列表:');
    modifiedFilesList.forEach(file => {
      console.log(`   - ${file}`);
    });

    console.log('\n🎯 下一步操作:');
    console.log('1. 运行 npm run build 检查编译错误');
    console.log('2. 运行 npm run lint 检查代码规范');
    console.log('3. 手动检查复杂的引用替换');
  } else {
    console.log('\n✨ 没有需要修改的文件');
  }

  console.log('\n🚀 Prisma服务引用修复完成！');
}

// 执行主函数
main();
