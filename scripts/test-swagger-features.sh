#!/bin/bash

# Swagger功能测试脚本
echo "=== Swagger功能测试 ==="
echo ""

# 检查服务是否运行
echo "1. 检查服务状态..."
if curl -s http://localhost:3000/api/docs > /dev/null; then
    echo "✅ 服务正在运行"
else
    echo "❌ 服务未运行，请先启动服务: npm run start:dev"
    exit 1
fi

echo ""

# 测试Swagger UI
echo "2. 测试Swagger UI..."
curl -s -I http://localhost:3000/api/docs | head -1
echo "💡 访问链接: http://localhost:3000/api/docs"

echo ""

# 测试Markdown导出
echo "3. 测试Markdown导出..."
if curl -s http://localhost:3000/api/system/swagger/markdown -o swagger-docs.md; then
    echo "✅ Markdown文档导出成功: swagger-docs.md"
    echo "📄 文档大小: $(wc -l swagger-docs.md | awk '{print $1}') 行"
else
    echo "❌ Markdown导出失败"
fi

echo ""

# 测试JSON导出
echo "4. 测试JSON导出..."
if curl -s http://localhost:3000/api/system/swagger/json -o swagger-docs.json; then
    echo "✅ JSON文档导出成功: swagger-docs.json"
    echo "📄 文档大小: $(wc -l swagger-docs.json | awk '{print $1}') 行"
else
    echo "❌ JSON导出失败"
fi

echo ""

# 检查统一返回格式
echo "5. 检查统一返回格式..."
echo "在Swagger UI中，所有API响应都应该显示以下格式："
echo ""
echo "{
  \"code\": 0,
  \"message\": \"操作成功\",
  \"data\": {
    // 具体响应数据
  }
}"

echo ""
echo "=== 测试完成 ==="
echo ""
echo "📝 功能清单:"
echo "✅ Swagger UI增强 - 显示统一返回格式"
echo "✅ Markdown文档导出 - /api/system/swagger/markdown"
echo "✅ JSON文档导出 - /api/system/swagger/json"
echo "✅ API响应装饰器 - 简化Swagger注解"
echo ""
echo "🔗 相关链接:"
echo "- Swagger UI: http://localhost:3000/api/docs"
echo "- API文档(MD): http://localhost:3000/api/system/swagger/markdown"
echo "- API文档(JSON): http://localhost:3000/api/system/swagger/json"
