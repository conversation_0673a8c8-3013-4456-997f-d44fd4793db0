#!/usr/bin/env node

/**
 * 导入修复脚本 V2
 * 修复DatabaseFactory导入和依赖注入问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复导入和依赖注入...\n');

// 导入修复规则
const importFixRules = [
  // 确保有DatabaseFactory导入
  {
    name: '添加DatabaseFactory导入',
    pattern: /.*\.service\.ts$/,
    fix: (content) => {
      // 如果使用了DatabaseFactory但没有导入
      if (content.includes('DatabaseFactory') && !content.includes("import { DatabaseFactory }")) {
        // 在第一个import之后添加
        const firstImportIndex = content.indexOf('import');
        if (firstImportIndex >= 0) {
          const lines = content.split('\n');
          let insertIndex = 0;

          // 找到最后一个import语句
          for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes('import')) {
              insertIndex = i + 1;
            } else if (lines[i].trim() === '' && insertIndex > 0) {
              break;
            }
          }

          lines.splice(insertIndex, 0, "import { DatabaseFactory } from '@/core/database/database.factory';");
          return lines.join('\n');
        }
      }
      return content;
    }
  }
];

// 处理单个文件
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // 应用导入修复
    for (const rule of importFixRules) {
      if (rule.pattern.test(filePath)) {
        const newContent = rule.fix(content);
        if (newContent !== content) {
          content = newContent;
          hasChanges = true;
        }
      }
    }

    // 额外的清理工作
    const originalContent = content;

    // 移除重复的DatabaseFactory导入
    content = content.replace(/import { DatabaseFactory }[^\n]*\n/g, (match, offset) => {
      const before = content.substring(0, offset);
      if (before.includes("import { DatabaseFactory }")) {
        return ''; // 移除重复的导入
      }
      return match;
    });

    // 修复注入装饰器问题
    content = content.replace(/@Inject\('DATABASE_FACTORY'\)\s*/g, '');

    // 修复构造函数参数
    content = content.replace(
      /constructor\(\s*private readonly databaseFactory: DatabaseFactory\s*,?\s*\)/g,
      'constructor(private readonly databaseFactory: DatabaseFactory)'
    );

    if (content !== originalContent) {
      hasChanges = true;
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复导入: ${path.relative(process.cwd(), filePath)}`);
      return true;
    }
    return false;
  } catch (error) {
    console.error(`❌ 修复失败: ${filePath} - ${error.message}`);
    return false;
  }
}

// 递归处理目录
function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);
  let count = 0;

  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !['node_modules', '.git', 'dist'].includes(item)) {
      count += processDirectory(fullPath);
    } else if (stat.isFile() && path.extname(item) === '.ts') {
      if (processFile(fullPath)) {
        count++;
      }
    }
  }

  return count;
}

// 修复特定的语法错误文件
function fixSpecificSyntaxErrors() {
  const specificFixes = [
    // 修复 logging.service.ts
    {
      file: 'src/core/logging/logging.service.ts',
      fix: (content) => {
        return content
          .replace(/}\s*\)\s*;\s*}\s*\)/gm, '})') // 修复多余的括号
          .replace(/\s*\)\s*;\s*$/gm, ')'); // 修复行尾多余的分号和括号
      }
    },

    // 修复 media-asset.controller.ts 参数问题
    {
      file: 'src/modules/media-asset/media-asset.controller.ts',
      fix: (content) => {
        return content.replace(/websiteId/g, 'id'); // 如果websiteId变量不存在，替换为id
      }
    }
  ];

  let count = 0;
  for (const { file, fix } of specificFixes) {
    try {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        const newContent = fix(content);

        if (newContent !== content) {
          fs.writeFileSync(file, newContent, 'utf8');
          console.log(`✅ 特定语法修复: ${path.relative(process.cwd(), file)}`);
          count++;
        }
      }
    } catch (error) {
      console.error(`❌ 特定修复失败: ${file} - ${error.message}`);
    }
  }

  return count;
}

// 执行修复
console.log('📂 修复导入和依赖注入...');
const importCount = processDirectory(path.join(process.cwd(), 'src'));

console.log('📂 修复特定语法错误...');
const syntaxCount = fixSpecificSyntaxErrors();

console.log(`\n✅ 导入修复完成！`);
console.log(`📋 导入修复: ${importCount} 个文件`);
console.log(`🔧 语法修复: ${syntaxCount} 个文件`);
console.log('🎯 下一步: 运行编译检查');
