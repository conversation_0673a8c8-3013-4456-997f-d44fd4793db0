#!/usr/bin/env node

/**
 * 方法名修复脚本
 * 修复DatabaseFactory方法调用错误
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复方法名...\n');

// 方法名修复规则
const methodNameFixes = [
  {
    from: /this\.databaseFactory\.getSystemClient\(\)/g,
    to: 'this.databaseFactory.getPublicClient()'
  },
  {
    from: /databaseFactory\.getSystemClient\(\)/g,
    to: 'databaseFactory.getPublicClient()'
  },
  {
    from: /const db = this\.databaseFactory\.getPublicClient\(\);/g,
    to: 'const db = this.databaseFactory.getPublicClient();'
  }
];

// 处理文件
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    for (const fix of methodNameFixes) {
      const newContent = content.replace(fix.from, fix.to);
      if (newContent !== content) {
        content = newContent;
        hasChanges = true;
      }
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 方法名修复: ${path.relative(process.cwd(), filePath)}`);
      return true;
    }
    return false;
  } catch (error) {
    console.error(`❌ 方法名修复失败: ${filePath} - ${error.message}`);
    return false;
  }
}

// 递归处理目录
function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);
  let count = 0;

  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !['node_modules', '.git', 'dist'].includes(item)) {
      count += processDirectory(fullPath);
    } else if (stat.isFile() && path.extname(item) === '.ts') {
      if (processFile(fullPath)) {
        count++;
      }
    }
  }

  return count;
}

// 执行修复
console.log('📂 修复方法名...');
const count = processDirectory(path.join(process.cwd(), 'src'));

console.log(`\n✅ 方法名修复完成！修改了 ${count} 个文件`);
console.log('🚀 下一步: 检查编译效果');
