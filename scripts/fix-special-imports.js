#!/usr/bin/env node

/**
 * 修复特殊Prisma服务引用脚本
 *
 * 处理复杂的引用模式和非标准路径
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复特殊Prisma服务引用...\n');

// 需要手动处理的文件和对应的修复策略
const specialFiles = [
  {
    file: 'src/modules/website/website.service.ts',
    action: 'replace_with_base_service',
  },
  {
    file: 'src/modules/component-library/component-library.service.ts',
    action: 'replace_with_base_service',
  },
  {
    file: 'src/modules/media-asset/media-asset.service.ts',
    action: 'replace_with_base_service',
  },
  {
    file: 'src/modules/sitemap/sitemap.service.ts',
    action: 'replace_with_base_service',
  },
  {
    file: 'src/modules/website-seo/website-seo.service.ts',
    action: 'replace_with_base_service',
  },
  {
    file: 'src/modules/website-template/website-template.service.ts',
    action: 'replace_with_base_service',
  },
  {
    file: 'src/modules/website-page/website-page.service.ts',
    action: 'replace_with_base_service',
  },
  {
    file: 'src/modules/website-form/website-form.service.ts',
    action: 'replace_with_base_service',
  },
];

let totalFixed = 0;

/**
 * 修复@core/路径的引用
 */
function fixCorePathImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // 修复@core/路径
    const corePathPattern = /from\s*['"]@core\/database\/prisma\/[^'"]*['"];?/g;
    if (corePathPattern.test(content)) {
      content = content.replace(corePathPattern, "from '@/core/database/database.factory';");
      hasChanges = true;
    }

    // 修复相对路径
    const relativePathPattern = /from\s*['"][^'"]*core\/database\/prisma\/[^'"]*['"];?/g;
    if (relativePathPattern.test(content)) {
      content = content.replace(relativePathPattern, "from '@/core/database/database.factory';");
      hasChanges = true;
    }

    // 修复import中的TenantPrismaService和TENANT_PRISMA_SERVICE
    const complexImportPattern =
      /import\s*{\s*[^}]*(?:TenantPrismaService|TENANT_PRISMA_SERVICE)[^}]*}\s*from\s*['"][^'"]*tenant-prisma\.service['"];?/g;
    if (complexImportPattern.test(content)) {
      content = content.replace(
        complexImportPattern,
        "import { DatabaseFactory } from '@/core/database/database.factory';",
      );
      hasChanges = true;
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`  ✅ 修复路径引用: ${filePath}`);
      totalFixed++;
    }
  } catch (error) {
    console.error(`❌ 修复失败: ${filePath}`, error.message);
  }
}

/**
 * 将服务重构为继承BaseBusinessService
 */
function convertToBaseService(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    // 1. 添加BaseBusinessService import
    if (!content.includes('BaseBusinessService')) {
      const importMatch = content.match(/import[^;]+;/);
      if (importMatch) {
        const importSection = content.substring(
          0,
          content.indexOf(importMatch[0]) + importMatch[0].length,
        );
        const restContent = content.substring(
          content.indexOf(importMatch[0]) + importMatch[0].length,
        );

        content =
          importSection +
          "\nimport { BaseBusinessService } from '@/modules/business/base/business.service';" +
          restContent;
      }
    }

    // 2. 移除Prisma相关imports
    content = content.replace(
      /import\s*{\s*[^}]*(?:TenantPrismaService|TENANT_PRISMA_SERVICE)[^}]*}\s*from\s*['"][^'"]*;?\s*/g,
      '',
    );

    // 3. 更新class继承
    const classMatch = content.match(/export\s+class\s+(\w+)\s*(?:extends\s+\w+)?\s*{/);
    if (classMatch) {
      const className = classMatch[1];
      content = content.replace(
        classMatch[0],
        `export class ${className} extends BaseBusinessService {`,
      );
    }

    // 4. 移除constructor中的Prisma注入，替换为super()调用
    const constructorPattern =
      /constructor\s*\([^)]*@Inject\(TENANT_PRISMA_SERVICE\)[^)]*\)\s*{[^}]*}/;
    if (constructorPattern.test(content)) {
      content = content.replace(
        constructorPattern,
        'constructor(databaseFactory: DatabaseFactory) { super(databaseFactory); }',
      );
    }

    // 5. 替换this.prisma调用为this.tenantDb
    content = content.replace(/this\.prisma\./g, 'this.tenantDb.');

    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`  ✅ 转换为BaseBusinessService: ${filePath}`);
      totalFixed++;
    }
  } catch (error) {
    console.error(`❌ 转换失败: ${filePath}`, error.message);
  }
}

/**
 * 主函数
 */
function main() {
  const startTime = Date.now();

  // 批量修复@core/路径引用
  console.log('📂 修复@core/路径引用...');
  const corePathFiles = [
    'src/modules/website/website.service.ts',
    'src/modules/component-library/component-library.service.ts',
    'src/modules/media-asset/media-asset.service.ts',
    'src/modules/sitemap/sitemap.service.ts',
    'src/modules/website-seo/website-seo.service.ts',
    'src/modules/website-template/website-template.service.ts',
    'src/modules/website-page/website-page.service.ts',
    'src/modules/website-form/website-form.service.ts',
  ];

  corePathFiles.forEach(file => {
    const fullPath = path.join(process.cwd(), file);
    if (fs.existsSync(fullPath)) {
      fixCorePathImports(fullPath);
    }
  });

  console.log('\n📂 转换为BaseBusinessService架构...');
  specialFiles.forEach(item => {
    const fullPath = path.join(process.cwd(), item.file);
    if (fs.existsSync(fullPath)) {
      if (item.action === 'replace_with_base_service') {
        convertToBaseService(fullPath);
      }
    }
  });

  const endTime = Date.now();

  console.log('\n' + '='.repeat(60));
  console.log('📊 特殊引用修复完成');
  console.log('='.repeat(60));
  console.log(`⏱️  处理时间: ${endTime - startTime}ms`);
  console.log(`✏️  修复文件: ${totalFixed} 个`);

  console.log('\n🎯 下一步: 手动处理策略类和代理类的复杂引用');
  console.log('   这些文件需要特殊处理，因为它们使用了动态解析模式');
}

// 执行主函数
main();
