#!/usr/bin/env node

/**
 * 全面语法修复脚本
 * 处理所有控制器、服务的语法错误
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始全面语法修复...\n');

// 全面语法修复规则
const syntaxRules = [
  // 修复双引号为单引号
  {
    name: '修复引号风格',
    pattern: /.*\.(service|controller)\.ts$/,
    fixes: [
      { from: /throw new Error\("Method not implemented"\)/g, to: "throw new Error('Method not implemented')" },
      { from: /"desc"/g, to: "'desc'" },
      { from: /"asc"/g, to: "'asc'" }
    ]
  },

  // 修复API装饰器类型问题
  {
    name: '修复API装饰器类型',
    pattern: /.*\.controller\.ts$/,
    fixes: [
      { from: /@ApiCompleteResponse\(\[String\],/g, to: '@ApiCompleteResponse(String,' },
      { from: /@ApiCompleteResponse\(any \/\/ .* not exist,/g, to: '@ApiCompleteResponse(Object,' },
      { from: /@ApiCompletePaginationResponse\(any \/\/ .* not exist,/g, to: '@ApiCompletePaginationResponse(Object,' }
    ]
  },

  // 修复方法注释语法
  {
    name: '修复方法注释语法',
    pattern: /.*\.(service|controller)\.ts$/,
    fixes: [
      { from: /await \/\/ this\./g, to: '// await this.' },
      { from: /return \/\/ this\./g, to: '// return this.' },
      { from: /Promise<any \/\/ .* not exist>/g, to: 'Promise<any>' }
    ]
  },

  // 修复注释格式错误
  {
    name: '修复注释格式',
    pattern: /.*\.(service|controller)\.ts$/,
    fixes: [
      { from: /\/\/ tenantId, \/\/ not in schema/g, to: '// tenantId // not in schema' },
      { from: /user\.tenantId, \/\/ tenantId from user/g, to: 'user.tenantId // tenantId from user' }
    ]
  }
];

// 处理文件
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    for (const rule of syntaxRules) {
      if (rule.pattern.test(filePath)) {
        for (const fix of rule.fixes) {
          const newContent = content.replace(fix.from, fix.to);
          if (newContent !== content) {
            content = newContent;
            hasChanges = true;
          }
        }
      }
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复: ${path.relative(process.cwd(), filePath)}`);
      return true;
    }
    return false;
  } catch (error) {
    console.error(`❌ 错误: ${filePath} - ${error.message}`);
    return false;
  }
}

// 递归处理目录
function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);
  let modifiedCount = 0;

  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !['node_modules', '.git', 'dist'].includes(item)) {
      modifiedCount += processDirectory(fullPath);
    } else if (stat.isFile() && path.extname(item) === '.ts') {
      if (processFile(fullPath)) {
        modifiedCount++;
      }
    }
  }

  return modifiedCount;
}

// 执行修复
console.log('📂 开始处理所有文件...');
const modified = processDirectory(path.join(process.cwd(), 'src'));

console.log(`\n✅ 语法修复完成！修改了 ${modified} 个文件`);
console.log('�� 下一步: 运行编译检查剩余错误');
