/**
 * 导出功能代码数据为种子文件
 * 
 * 使用方法：
 * node scripts/export-feature-codes.js
 */

const { PrismaClient } = require('../node_modules/@prisma-public/prisma/client');
const fs = require('fs');
const path = require('path');

// 创建 Prisma 客户端实例
const prisma = new PrismaClient();

/**
 * 导出功能代码数据
 */
async function exportFeatureCodes() {
  try {
    console.log('开始导出功能代码数据...');

    // 从数据库获取所有功能代码
    const featureCodes = await prisma.featureCode.findMany({
      orderBy: {
        module: 'asc',
        sortOrder: 'asc'
      }
    });

    console.log(`找到 ${featureCodes.length} 个功能代码`);

    // 按模块分组
    const moduleGroups = {};
    featureCodes.forEach(code => {
      if (!moduleGroups[code.module]) {
        moduleGroups[code.module] = [];
      }
      moduleGroups[code.module].push(code);
    });

    // 生成种子文件内容
    let fileContent = `/**
 * 创建默认功能代码
 * @param {import('@prisma/client').PrismaClient} prisma Prisma客户端
 */
async function seedFeatureCodes(prisma) {
  console.log('开始创建功能代码...');

`;

    // 为每个模块生成代码
    for (const [module, codes] of Object.entries(moduleGroups)) {
      fileContent += `  // ${module}功能\n`;
      
      for (const code of codes) {
        fileContent += `  await createFeatureCode(prisma, {
    code: '${code.code}',
    name: '${code.name}',
    description: '${code.description}',
    module: '${code.module}',
    isActive: ${code.isActive},
    sortOrder: ${code.sortOrder},
    metadata: ${JSON.stringify(code.metadata, null, 2).replace(/\n/g, '\n    ')}
  });\n\n`;
      }
    }

    fileContent += `  console.log('功能代码创建完成');
}

/**
 * 创建或更新功能代码
 * @param {import('@prisma/client').PrismaClient} prisma Prisma客户端
 * @param {Object} data 功能代码数据
 */
async function createFeatureCode(prisma, data) {
  const { code, name, description, module, isActive, sortOrder, metadata } = data;
  
  await prisma.featureCode.upsert({
    where: { code },
    update: {
      name,
      description,
      module,
      isActive,
      sortOrder,
      metadata
    },
    create: {
      code,
      name,
      description,
      module,
      isActive,
      sortOrder,
      metadata
    }
  });
  
  console.log(\`功能代码 \${code} 创建/更新成功\`);
}

module.exports = { seedFeatureCodes };`;

    // 保存到文件
    const outputPath = path.join(__dirname, '../prisma/seeds/feature-codes.js');
    fs.writeFileSync(outputPath, fileContent);

    console.log(`功能代码数据已导出到: ${outputPath}`);

    // 创建备份文件
    const backupDir = path.join(__dirname, '../backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(backupDir, `feature-codes-${timestamp}.json`);
    fs.writeFileSync(backupPath, JSON.stringify(featureCodes, null, 2));

    console.log(`功能代码数据备份已保存到: ${backupPath}`);
  } catch (error) {
    console.error('导出功能代码数据失败:', error);
  } finally {
    // 关闭数据库连接
    await prisma.$disconnect();
  }
}

// 执行导出
exportFeatureCodes().catch(error => {
  console.error('导出失败:', error);
  process.exit(1);
});
