#!/usr/bin/env node

/**
 * 修复seed文件中的Schema问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复seed文件的Schema问题...\n');

function fixSeedFile() {
  const seedPath = path.join(process.cwd(), 'prisma/seed.ts');

  if (!fs.existsSync(seedPath)) {
    console.error('❌ seed.ts文件不存在');
    return;
  }

  let content = fs.readFileSync(seedPath, 'utf8');
  let hasChanges = false;

  // 1. 移除subscriptionPlan表引用（不存在）
  if (content.includes('prisma.subscriptionPlan')) {
    console.log('  ✅ 注释掉subscriptionPlan相关代码（表不存在）');

    // 注释掉整个subscriptionPlan相关的代码块
    content = content.replace(
      /for \(const plan of subscriptionPlans\) \{[\s\S]*?await prisma\.subscriptionPlan\.upsert\([\s\S]*?\}\);[\s\S]*?\}/,
      '// subscriptionPlan table does not exist in current schema\n  // for (const plan of subscriptionPlans) {\n  //   await prisma.subscriptionPlan.upsert(...);\n  // }'
    );
    hasChanges = true;
  }

  // 2. 移除tenant表引用（不存在）
  if (content.includes('prisma.tenant')) {
    console.log('  ✅ 注释掉tenant相关代码（表不存在）');

    content = content.replace(
      /const systemTenant = await prisma\.tenant\.upsert\([\s\S]*?\}\);/,
      '// tenant table does not exist in current schema\n  // const systemTenant = await prisma.tenant.upsert(...);'
    );
    hasChanges = true;
  }

  // 3. 修复role表字段（remark → description）
  if (content.includes('remark: role.description')) {
    console.log('  ✅ 修复Role表字段：remark → description');
    content = content.replace(/remark: role\.description/g, 'description: role.description');
    content = content.replace(/remark: role\.description/g, 'description: role.description');
    hasChanges = true;
  }

  // 4. 修复User表字段映射
  if (content.includes('emailAddress:')) {
    console.log('  ✅ 修复User表字段：emailAddress → email');
    content = content.replace(/emailAddress:/g, 'email:');
    hasChanges = true;
  }

  // 5. 修复时间字段
  if (content.includes('updatedAt:')) {
    console.log('  ✅ 修复时间字段：updatedAt → updateTime');
    content = content.replace(/updatedAt:/g, 'updateTime:');
    hasChanges = true;
  }

  // 6. 修复UserRole表的tenantId字段（不存在）
  if (content.includes('tenantId: 0,') && content.includes('userRole')) {
    console.log('  ✅ 移除UserRole表中的tenantId字段（不存在）');
    content = content.replace(/tenantId: 0,\s*\n\s*\}/g, '\n      }');
    hasChanges = true;
  }

  // 7. 修复unique约束名
  if (content.includes('username_tenantId')) {
    console.log('  ✅ 修复unique约束名');
    content = content.replace(/username_tenantId/g, 'user_tenant_username_key');
    hasChanges = true;
  }

  if (content.includes('userId_roleId')) {
    console.log('  ✅ 修复UserRole unique约束名');
    content = content.replace(/userId_roleId/g, 'userId_roleId'); // 这个可能需要检查实际的约束名
    hasChanges = true;
  }

  // 8. 修复tenantId_code约束
  if (content.includes('tenantId_code')) {
    console.log('  ✅ 修复Role unique约束名');
    content = content.replace(/tenantId_code/g, 'tenantId_code'); // 需要检查实际约束名
    hasChanges = true;
  }

  if (hasChanges) {
    fs.writeFileSync(seedPath, content, 'utf8');
    console.log('\n✅ seed.ts文件修复完成');
  } else {
    console.log('\n✨ seed.ts文件无需修改');
  }
}

// 执行修复
fixSeedFile();
