#!/usr/bin/env node

/**
 * 租户注册API测试脚本
 * 用于测试全自动化租户注册流程
 */

const axios = require('axios');

// 配置
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000/api';
const TEST_DATA = {
  tenantCode: `test-tenant-${Date.now()}`,
  tenantName: '测试公司',
  customDomain: null, // 不使用自定义域名
  companyInfo: {
    companyName: '测试科技有限公司',
    companySize: '10-50人',
    industry: '软件开发',
    website: 'https://test-company.com',
  },
  adminUser: {
    username: 'admin',
    password: 'Admin123456',
    email: `admin-${Date.now()}@test.com`,
    realName: '管理员',
    phoneNumber: '13800138000',
  },
  subscriptionPlan: 'free',
  agreeToTerms: true,
};

async function testTenantRegistration() {
  console.log('🚀 开始测试租户注册流程...\n');

  try {
    // 1. 检查租户代码可用性
    console.log('1️⃣ 检查租户代码可用性...');
    const availabilityResponse = await axios.get(
      `${API_BASE_URL}/public/tenant/check-availability`,
      {
        params: {
          code: TEST_DATA.tenantCode,
          domain: TEST_DATA.customDomain,
        },
      },
    );

    console.log('✅ 可用性检查结果:', availabilityResponse.data);

    if (!availabilityResponse.data.codeAvailable) {
      console.log('❌ 租户代码不可用，使用建议的代码');
      TEST_DATA.tenantCode = availabilityResponse.data.suggestions[0];
    }

    // 2. 获取订阅计划
    console.log('\n2️⃣ 获取可用订阅计划...');
    const plansResponse = await axios.get(`${API_BASE_URL}/public/tenant/subscription-plans`);

    console.log(
      '✅ 可用计划:',
      plansResponse.data.plans.map(p => ({
        code: p.code,
        name: p.name,
        price: p.price,
      })),
    );

    // 3. 注册租户
    console.log('\n3️⃣ 开始租户注册...');
    const registrationResponse = await axios.post(
      `${API_BASE_URL}/public/tenant/register`,
      TEST_DATA,
    );

    console.log('✅ 注册成功:', {
      tenantId: registrationResponse.data.tenantId,
      tenantCode: registrationResponse.data.tenantCode,
      accessUrl: registrationResponse.data.accessUrl,
      nextSteps: registrationResponse.data.nextSteps,
    });

    // 4. 检查注册状态
    console.log('\n4️⃣ 检查注册状态...');
    const statusResponse = await axios.get(`${API_BASE_URL}/public/tenant/registration-status`, {
      params: { code: registrationResponse.data.tenantCode },
    });

    console.log('✅ 注册状态:', statusResponse.data);

    // 5. 验证租户（模拟邮箱验证）
    console.log('\n5️⃣ 模拟邮箱验证...');
    const verifyResponse = await axios.post(`${API_BASE_URL}/public/tenant/verify`, {
      tenantCode: registrationResponse.data.tenantCode,
      verificationCode: 'mock-verification-code',
    });

    console.log('✅ 验证结果:', verifyResponse.data);

    // 6. 测试管理员登录
    console.log('\n6️⃣ 测试管理员登录...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: TEST_DATA.adminUser.username,
      password: TEST_DATA.adminUser.password,
      tenantCode: registrationResponse.data.tenantCode,
    });

    console.log('✅ 登录成功:', {
      token: loginResponse.data.token ? '已获取' : '未获取',
      userInfo: loginResponse.data.userInfo,
    });

    console.log('\n🎉 租户注册流程测试完成！');
    console.log(`📱 访问地址: ${registrationResponse.data.accessUrl}`);
    console.log(`🔑 管理员令牌: ${registrationResponse.data.adminToken}`);
  } catch (error) {
    console.error('❌ 测试失败:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
    });
  }
}

// 并发测试多个租户注册
async function testConcurrentRegistrations() {
  console.log('\n🔄 开始并发注册测试...');

  const promises = [];
  const testCount = 3;

  for (let i = 0; i < testCount; i++) {
    const testData = {
      ...TEST_DATA,
      tenantCode: `concurrent-test-${Date.now()}-${i}`,
      adminUser: {
        ...TEST_DATA.adminUser,
        username: `admin${i}`,
        email: `admin${i}-${Date.now()}@test.com`,
      },
    };

    promises.push(
      axios
        .post(`${API_BASE_URL}/public/tenant/register`, testData)
        .then(response => ({
          success: true,
          tenantCode: response.data.tenantCode,
          index: i,
        }))
        .catch(error => ({
          success: false,
          error: error.message,
          index: i,
        })),
    );
  }

  const results = await Promise.all(promises);

  console.log('🔄 并发测试结果:');
  results.forEach(result => {
    if (result.success) {
      console.log(`  ✅ 租户 ${result.index}: ${result.tenantCode} - 成功`);
    } else {
      console.log(`  ❌ 租户 ${result.index}: ${result.error} - 失败`);
    }
  });

  const successCount = results.filter(r => r.success).length;
  console.log(
    `📊 成功率: ${successCount}/${testCount} (${((successCount / testCount) * 100).toFixed(1)}%)`,
  );
}

// 测试错误处理
async function testErrorHandling() {
  console.log('\n🚨 开始错误处理测试...');

  const errorTests = [
    {
      name: '重复租户代码',
      data: { ...TEST_DATA, tenantCode: 'duplicate-tenant' },
    },
    {
      name: '无效邮箱格式',
      data: {
        ...TEST_DATA,
        tenantCode: `error-test-${Date.now()}`,
        adminUser: { ...TEST_DATA.adminUser, email: 'invalid-email' },
      },
    },
    {
      name: '缺少必填字段',
      data: {
        tenantCode: `missing-field-${Date.now()}`,
        // 缺少其他必填字段
      },
    },
  ];

  for (const test of errorTests) {
    try {
      await axios.post(`${API_BASE_URL}/public/tenant/register`, test.data);
      console.log(`  ❌ ${test.name}: 应该失败但成功了`);
    } catch (error) {
      console.log(`  ✅ ${test.name}: 正确处理错误 - ${error.response?.status}`);
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const testType = args[0] || 'basic';

  switch (testType) {
    case 'basic':
      await testTenantRegistration();
      break;
    case 'concurrent':
      await testConcurrentRegistrations();
      break;
    case 'error':
      await testErrorHandling();
      break;
    case 'all':
      await testTenantRegistration();
      await testConcurrentRegistrations();
      await testErrorHandling();
      break;
    default:
      console.log('使用方法: node test-tenant-registration.js [basic|concurrent|error|all]');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testTenantRegistration,
  testConcurrentRegistrations,
  testErrorHandling,
};
