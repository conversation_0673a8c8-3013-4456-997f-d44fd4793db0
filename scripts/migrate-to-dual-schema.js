#!/usr/bin/env node

/**
 * FlexiHub Dual-Schema架构迁移脚本
 *
 * 功能：将现有单一Schema架构迁移到Dual-Schema架构
 *
 * 迁移内容：
 * - 平台基础设施数据 → Public Schema
 * - 业务数据 → Tenant Schema（保持原位置）
 */

const { PrismaClient } = require('@prisma/client');

// 尝试加载Public客户端，如果不存在则跳过
let PublicPrismaClient;
try {
  PublicPrismaClient = require('@prisma-public/prisma/client').PrismaClient;
} catch (error) {
  console.log('⚠️ Public Schema客户端未找到，请先运行: npm run db:generate:public');
  console.log('错误详情:', error.message);
  process.exit(1);
}

const prisma = new PrismaClient();
const publicPrisma = new PublicPrismaClient();

// 迁移状态跟踪
const migrationStatus = {
  tenants: 0,
  subscriptionPlans: 0,
  tenantSubscriptions: 0,
  tenantFeatures: 0,
  tenantConfigs: 0,
  systemConfigs: 0,
  auditLogs: 0,
  errors: [],
};

async function main() {
  console.log('🚀 开始FlexiHub Dual-Schema架构迁移...\n');

  try {
    // 检查环境和连接
    await checkEnvironment();

    // 执行迁移
    await migrateToPublicSchema();

    // 验证迁移结果
    await verifyMigration();

    // 输出迁移报告
    printMigrationReport();

    console.log('\n✅ Dual-Schema架构迁移完成！');
  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    console.error('\n建议操作：');
    console.error('1. 检查数据库连接');
    console.error('2. 确保Public Schema已创建：npm run db:push:public');
    console.error('3. 恢复数据库备份（如有）');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    await publicPrisma.$disconnect();
  }
}

async function checkEnvironment() {
  console.log('🔍 检查环境配置...');

  // 检查数据库连接
  try {
    await prisma.$connect();
    console.log('✅ 源数据库连接正常');
  } catch (error) {
    throw new Error(`源数据库连接失败: ${error.message}`);
  }

  try {
    await publicPrisma.$connect();
    console.log('✅ Public数据库连接正常');
  } catch (error) {
    throw new Error(`Public数据库连接失败: ${error.message}`);
  }

  // 检查源数据完整性
  const tenantCount = await prisma.tenant.count();
  const userCount = await prisma.user.count();

  console.log(`📊 源数据概览:`);
  console.log(`   - 租户: ${tenantCount}个`);
  console.log(`   - 用户: ${userCount}个`);

  if (tenantCount === 0) {
    console.log('⚠️ 未发现租户数据，将执行空迁移');
  }

  console.log('✅ 环境检查完成\n');
}

async function migrateToPublicSchema() {
  console.log('📦 开始数据迁移...\n');

  // 1. 迁移租户和数据源
  await migrateTenants();

  // 2. 迁移订阅计划
  await migrateSubscriptionPlans();

  // 3. 迁移租户订阅
  await migrateTenantSubscriptions();

  // 4. 迁移租户功能
  await migrateTenantFeatures();

  // 5. 迁移租户配置
  await migrateTenantConfigs();

  // 6. 迁移系统配置
  await migrateSystemConfigs();

  // 7. 迁移审计日志
  await migrateAuditLogs();

  console.log('✅ 数据迁移完成\n');
}

async function migrateTenants() {
  console.log('1️⃣ 迁移租户数据...');

  const tenants = await prisma.tenant.findMany({
    include: { datasource: true },
  });

  for (const tenant of tenants) {
    try {
      await publicPrisma.tenant.create({
        data: {
          id: tenant.id,
          code: tenant.code,
          name: tenant.name,
          website: tenant.website,
          domain: tenant.domain,
          status: tenant.status,
          createTime: tenant.createTime,
          updateTime: tenant.updateTime,
          metadata: tenant.metadata || {},
          registrationSource: tenant.registrationSource || 'manual',
          datasource: tenant.datasource
            ? {
                create: {
                  isShared: true, // 默认设置为共享模式
                  host: tenant.datasource.host,
                  port: tenant.datasource.port,
                  username: tenant.datasource.username,
                  password: tenant.datasource.password,
                  database: tenant.datasource.database,
                  ssl: tenant.datasource.ssl,
                  createTime: tenant.datasource.createTime,
                  updateTime: tenant.datasource.updateTime,
                },
              }
            : undefined,
        },
      });

      migrationStatus.tenants++;
      console.log(`   📝 成功迁移租户: ${tenant.code}`);
    } catch (error) {
      console.error(`   ❌ 租户 ${tenant.code} 迁移失败:`, error.message);
      migrationStatus.errors.push(`租户 ${tenant.code}: ${error.message}`);
    }
  }

  console.log(`   ✅ 已迁移 ${migrationStatus.tenants}/${tenants.length} 个租户`);
}

async function migrateSubscriptionPlans() {
  console.log('2️⃣ 迁移订阅计划...');

  const plans = await prisma.subscriptionPlan.findMany();

  for (const plan of plans) {
    try {
      await publicPrisma.subscriptionPlan.create({
        data: {
          id: plan.id,
          code: plan.code,
          name: plan.name,
          description: plan.description,
          price: plan.price,
          currency: plan.currency,
          billingCycle: plan.billingCycle,
          deploymentMode: ['shared'], // 默认支持共享模式
          features: plan.features,
          limits: plan.limits,
          databases: {}, // 默认空配置
          isActive: plan.isActive,
          sortOrder: plan.sortOrder,
          metadata: plan.metadata || {},
          createdAt: plan.createdAt,
          updatedAt: plan.updatedAt,
        },
      });

      migrationStatus.subscriptionPlans++;
      console.log(`   📝 成功迁移订阅计划: ${plan.code}`);
    } catch (error) {
      console.error(`   ❌ 订阅计划 ${plan.code} 迁移失败:`, error.message);
      migrationStatus.errors.push(`订阅计划 ${plan.code}: ${error.message}`);
    }
  }

  console.log(`   ✅ 已迁移 ${migrationStatus.subscriptionPlans}/${plans.length} 个订阅计划`);
}

async function migrateTenantSubscriptions() {
  console.log('3️⃣ 迁移租户订阅...');

  const subscriptions = await prisma.tenantSubscription.findMany();

  for (const subscription of subscriptions) {
    try {
      await publicPrisma.tenantSubscription.create({
        data: {
          id: subscription.id,
          tenantId: subscription.tenantId,
          planId: subscription.planId,
          duration: subscription.duration,
          billingCycle: subscription.billingCycle,
          startDate: subscription.startDate,
          endDate: subscription.endDate,
          status: subscription.status,
          autoRenew: subscription.autoRenew,
          paymentInfo: subscription.paymentInfo,
          metadata: subscription.metadata,
          createdAt: subscription.createdAt,
          updatedAt: subscription.updatedAt,
        },
      });

      migrationStatus.tenantSubscriptions++;
    } catch (error) {
      migrationStatus.errors.push(`租户订阅 ${subscription.id}: ${error.message}`);
    }
  }

  console.log(
    `   ✅ 已迁移 ${migrationStatus.tenantSubscriptions}/${subscriptions.length} 个租户订阅`,
  );
}

async function migrateTenantFeatures() {
  console.log('4️⃣ 迁移租户功能...');

  const features = await prisma.tenantFeature.findMany();

  for (const feature of features) {
    try {
      await publicPrisma.tenantFeature.create({
        data: {
          id: feature.id,
          tenantId: feature.tenantId,
          featureCode: feature.featureCode,
          enabled: feature.enabled,
          quota: feature.quota,
          usedQuota: feature.usedQuota,
          resetAt: feature.resetAt,
          metadata: feature.metadata || {},
          createdAt: feature.createdAt,
          updatedAt: feature.updatedAt,
        },
      });

      migrationStatus.tenantFeatures++;
    } catch (error) {
      migrationStatus.errors.push(`租户功能 ${feature.id}: ${error.message}`);
    }
  }

  console.log(`   ✅ 已迁移 ${migrationStatus.tenantFeatures}/${features.length} 个租户功能`);
}

async function migrateTenantConfigs() {
  console.log('5️⃣ 迁移租户配置...');

  const configs = await prisma.tenantConfig.findMany();

  for (const config of configs) {
    try {
      await publicPrisma.tenantConfig.create({
        data: {
          id: config.id,
          tenantId: config.tenantId,
          category: config.category,
          key: config.key,
          value: config.value,
          dataType: config.dataType,
          encrypted: config.encrypted,
          description: config.description,
          metadata: config.metadata || {},
          createdAt: config.createdAt,
          updatedAt: config.updatedAt,
        },
      });

      migrationStatus.tenantConfigs++;
    } catch (error) {
      migrationStatus.errors.push(`租户配置 ${config.id}: ${error.message}`);
    }
  }

  console.log(`   ✅ 已迁移 ${migrationStatus.tenantConfigs}/${configs.length} 个租户配置`);
}

async function migrateSystemConfigs() {
  console.log('6️⃣ 迁移系统配置...');

  const configs = await prisma.systemConfig.findMany();

  for (const config of configs) {
    try {
      await publicPrisma.systemConfig.create({
        data: {
          id: config.id,
          category: config.category,
          key: config.key,
          value: config.value,
          dataType: config.dataType,
          description: config.description,
          isPublic: config.isPublic,
          metadata: config.metadata || {},
          createdAt: config.createdAt,
          updatedAt: config.updatedAt,
        },
      });

      migrationStatus.systemConfigs++;
    } catch (error) {
      migrationStatus.errors.push(`系统配置 ${config.id}: ${error.message}`);
    }
  }

  console.log(`   ✅ 已迁移 ${migrationStatus.systemConfigs}/${configs.length} 个系统配置`);
}

async function migrateAuditLogs() {
  console.log('7️⃣ 迁移审计日志...');

  // 只迁移最近30天的审计日志，避免数据量过大
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const logs = await prisma.auditLog.findMany({
    where: {
      createdAt: {
        gte: thirtyDaysAgo,
      },
    },
    take: 10000, // 限制数量
  });

  for (const log of logs) {
    try {
      await publicPrisma.auditLog.create({
        data: {
          id: log.id,
          tenantId: log.tenantId,
          userId: log.userId,
          action: log.action,
          resource: log.resource,
          resourceId: log.resourceId,
          ipAddress: log.ipAddress,
          userAgent: log.userAgent,
          details: log.details || {},
          createdAt: log.createdAt,
        },
      });

      migrationStatus.auditLogs++;
    } catch (error) {
      migrationStatus.errors.push(`审计日志 ${log.id}: ${error.message}`);
    }
  }

  console.log(`   ✅ 已迁移 ${migrationStatus.auditLogs}/${logs.length} 条审计日志（最近30天）`);
}

async function verifyMigration() {
  console.log('🔍 验证迁移结果...');

  // 验证租户数据
  const originalTenantCount = await prisma.tenant.count();
  const migratedTenantCount = await publicPrisma.tenant.count();

  if (originalTenantCount !== migratedTenantCount) {
    throw new Error(`租户数据迁移不完整: 原始${originalTenantCount} vs 迁移${migratedTenantCount}`);
  }

  // 验证订阅计划
  const originalPlanCount = await prisma.subscriptionPlan.count();
  const migratedPlanCount = await publicPrisma.subscriptionPlan.count();

  if (originalPlanCount !== migratedPlanCount) {
    throw new Error(`订阅计划迁移不完整: 原始${originalPlanCount} vs 迁移${migratedPlanCount}`);
  }

  // 验证关键业务数据是否保留在Tenant Schema
  const userCount = await prisma.user.count();
  const websiteCount = await prisma.website.count();

  console.log('📊 迁移验证结果:');
  console.log(`   - 租户: ${originalTenantCount} → ${migratedTenantCount} ✅`);
  console.log(`   - 订阅计划: ${originalPlanCount} → ${migratedPlanCount} ✅`);
  console.log(`   - 用户数据: ${userCount}个 (保留在Tenant Schema) ✅`);
  console.log(`   - 网站数据: ${websiteCount}个 (保留在Tenant Schema) ✅`);
}

function printMigrationReport() {
  console.log('\n📋 迁移报告:');
  console.log('=====================================');
  console.log(`✅ 租户: ${migrationStatus.tenants}`);
  console.log(`✅ 订阅计划: ${migrationStatus.subscriptionPlans}`);
  console.log(`✅ 租户订阅: ${migrationStatus.tenantSubscriptions}`);
  console.log(`✅ 租户功能: ${migrationStatus.tenantFeatures}`);
  console.log(`✅ 租户配置: ${migrationStatus.tenantConfigs}`);
  console.log(`✅ 系统配置: ${migrationStatus.systemConfigs}`);
  console.log(`✅ 审计日志: ${migrationStatus.auditLogs}`);

  if (migrationStatus.errors.length > 0) {
    console.log(`\n⚠️ 迁移错误 (${migrationStatus.errors.length}个):`);
    migrationStatus.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  }

  console.log('=====================================');
}

// 处理脚本中断
process.on('SIGINT', async () => {
  console.log('\n⚠️ 迁移被中断，正在清理连接...');
  await prisma.$disconnect();
  await publicPrisma.$disconnect();
  process.exit(1);
});

// 执行迁移
main().catch(console.error);
