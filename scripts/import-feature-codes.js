/**
 * 从备份文件导入功能代码数据
 * 
 * 使用方法：
 * node scripts/import-feature-codes.js [备份文件路径]
 * 
 * 示例：
 * node scripts/import-feature-codes.js ./backups/feature-codes-2025-05-16T12-00-00-000Z.json
 */

const { PrismaClient } = require('../node_modules/@prisma-public/prisma/client');
const fs = require('fs');
const path = require('path');

// 创建 Prisma 客户端实例
const prisma = new PrismaClient();

/**
 * 导入功能代码数据
 * @param {string} filePath 备份文件路径
 */
async function importFeatureCodes(filePath) {
  try {
    console.log(`开始从 ${filePath} 导入功能代码数据...`);

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.error(`文件不存在: ${filePath}`);
      return;
    }

    // 读取备份文件
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const featureCodes = JSON.parse(fileContent);

    console.log(`从备份文件中读取到 ${featureCodes.length} 个功能代码`);

    // 导入功能代码
    for (const code of featureCodes) {
      await prisma.featureCode.upsert({
        where: { code: code.code },
        update: {
          name: code.name,
          description: code.description,
          module: code.module,
          isActive: code.isActive,
          sortOrder: code.sortOrder,
          metadata: code.metadata
        },
        create: {
          code: code.code,
          name: code.name,
          description: code.description,
          module: code.module,
          isActive: code.isActive,
          sortOrder: code.sortOrder,
          metadata: code.metadata
        }
      });
      console.log(`功能代码 ${code.code} 导入成功`);
    }

    console.log('功能代码数据导入完成');
  } catch (error) {
    console.error('导入功能代码数据失败:', error);
  } finally {
    // 关闭数据库连接
    await prisma.$disconnect();
  }
}

// 获取命令行参数
const args = process.argv.slice(2);
const filePath = args[0];

if (!filePath) {
  console.error('请提供备份文件路径');
  process.exit(1);
}

// 执行导入
importFeatureCodes(filePath).catch(error => {
  console.error('导入失败:', error);
  process.exit(1);
});
