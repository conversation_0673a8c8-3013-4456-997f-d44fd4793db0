#!/usr/bin/env node

/**
 * Entity与Schema字段映射修复脚本
 *
 * 修复Entity定义与实际Schema不匹配的问题
 * 遵循第一性原理：Entity应该完全反映实际数据库Schema
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复Entity与Schema字段映射...\n');

// Entity字段修复规则
const entityFixRules = [
  // ===== WebsitePage Entity修复 =====
  {
    name: 'WebsitePage: 移除tenantId字段（Schema中不存在）',
    pattern: /src\/modules\/website-page\/entities\/website-page\.entity\.ts/,
    fixes: [
      {
        from: /(@ApiProperty\({ description: '租户ID' }\)\s*tenantId: number;)/g,
        to: '// tenantId field removed - not in schema'
      },
      {
        from: /metadata: { sortOrder: number;/g,
        to: ''
      },
      {
        from: /metadata: { isHomePage: boolean;/g,
        to: ''
      },
      {
        from: /@ApiProperty\({ description: '排序权重', default: 0 }\)\s*@IsInt\(\)/g,
        to: ''
      },
      {
        from: /@ApiProperty\({ description: '是否为首页', default: false }\)\s*@IsBoolean\(\)/g,
        to: ''
      }
    ]
  },

  // ===== Website Entity修复 =====
  {
    name: 'Website: 修复字段映射',
    pattern: /src\/modules\/website\/entities\/website\.entity\.ts/,
    fixes: [
      {
        from: /createTime/g,
        to: 'createdAt'
      },
      {
        from: /updateTime/g,
        to: 'updatedAt'
      }
    ]
  },

  // ===== User Entity修复 =====
  {
    name: 'User: 修复字段映射',
    pattern: /src\/modules\/user\/entities\/user\.entity\.ts/,
    fixes: [
      {
        from: /createTime/g,
        to: 'createTime'
      },
      {
        from: /updateTime/g,
        to: 'updateTime'
      },
      {
        from: /emailAddress/g,
        to: 'email'
      }
    ]
  },

  // ===== 移除不存在的Entity文件引用 =====
  {
    name: '移除WebsiteForm Entity引用（表不存在）',
    pattern: /.*\/entities\/website-form\.entity\.ts/,
    action: 'comment_imports'
  },
  {
    name: '移除WebsiteSeo Entity引用（表不存在）',
    pattern: /.*\/entities\/website-seo\.entity\.ts/,
    action: 'comment_imports'
  },
  {
    name: '移除WebsiteTemplate Entity引用（表不存在）',
    pattern: /.*\/entities\/website-template\.entity\.ts/,
    action: 'comment_imports'
  },
];

// 统计信息
let totalFiles = 0;
let modifiedFiles = 0;
const modifiedFilesList = [];

/**
 * 递归搜索并处理文件
 */
function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);

  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      if (['node_modules', '.git', 'dist', 'coverage', 'docs'].includes(item)) {
        continue;
      }
      processDirectory(fullPath);
    } else if (stat.isFile()) {
      const ext = path.extname(item);
      if (['.ts', '.js'].includes(ext)) {
        processFile(fullPath);
      }
    }
  }
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  totalFiles++;

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let hasChanges = false;

    // 应用Entity修复规则
    for (const rule of entityFixRules) {
      if (rule.pattern.test(filePath)) {
        console.log(`  🔧 ${rule.name}: ${path.relative(process.cwd(), filePath)}`);

        if (rule.action === 'comment_imports') {
          // 注释掉不存在Entity的import语句
          const importPattern = new RegExp(`import.*from.*${path.basename(filePath, '.ts')}.*`, 'g');
          content = content.replace(importPattern, '// $&');
          hasChanges = true;
        } else if (rule.fixes) {
          // 应用字段修复
          for (const fix of rule.fixes) {
            const newContent = content.replace(fix.from, fix.to);
            if (newContent !== content) {
              content = newContent;
              hasChanges = true;
            }
          }
        }
      }
    }

    // 通用修复：移除不存在表的import
    const beforeImportFix = content;
    content = content.replace(/import.*WebsiteFormEntity.*\n/g, '// import WebsiteFormEntity - table does not exist\n');
    content = content.replace(/import.*WebsiteSeoEntity.*\n/g, '// import WebsiteSeoEntity - table does not exist\n');
    content = content.replace(/import.*WebsiteTemplateEntity.*\n/g, '// import WebsiteTemplateEntity - table does not exist\n');

    if (content !== beforeImportFix) {
      hasChanges = true;
      console.log(`  ✅ 移除不存在表的import: ${path.relative(process.cwd(), filePath)}`);
    }

    // 如果有变更，写回文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      modifiedFiles++;
      modifiedFilesList.push(path.relative(process.cwd(), filePath));
    }
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath}`, error.message);
  }
}

/**
 * 主函数
 */
function main() {
  const startTime = Date.now();

  // 从src目录开始处理
  const srcPath = path.join(process.cwd(), 'src');
  if (fs.existsSync(srcPath)) {
    console.log('📂 处理 src/ 目录...');
    processDirectory(srcPath);
  }

  const endTime = Date.now();

  // 输出统计信息
  console.log('\n' + '='.repeat(60));
  console.log('📊 Entity-Schema修复完成统计');
  console.log('='.repeat(60));
  console.log(`⏱️  处理时间: ${endTime - startTime}ms`);
  console.log(`📁 扫描文件: ${totalFiles} 个`);
  console.log(`✏️  修改文件: ${modifiedFiles} 个`);

  if (modifiedFiles > 0) {
    console.log('\n📝 修改的文件列表:');
    modifiedFilesList.forEach(file => {
      console.log(`   - ${file}`);
    });

    console.log('\n🎯 下一步操作:');
    console.log('1. 运行 npm run build 检查编译错误');
    console.log('2. 修复剩余的服务方法问题');
    console.log('3. 处理类型定义不匹配');
  } else {
    console.log('\n✨ 没有需要修改的文件');
  }

  console.log('\n🚀 Entity-Schema修复完成！');
}

// 执行主函数
main();
