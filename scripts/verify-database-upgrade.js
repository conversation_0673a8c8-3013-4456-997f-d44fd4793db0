const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyDatabaseUpgrade() {
  console.log('🔍 开始验证数据库升级...\n');

  const results = {
    tables: {},
    data: {},
    errors: [],
  };

  try {
    // 1. 验证新表是否存在
    console.log('📋 检查新增表结构...');

    const tables = [
      'subscription_plans',
      'tenant_subscriptions',
      'tenant_features',
      'tenant_config',
      'websites',
      'website_pages',
      'audit_logs',
      'system_config',
      'notifications',
      'usage_stats',
      'payments',
    ];

    for (const table of tables) {
      try {
        const count = await prisma.$queryRaw`
          SELECT COUNT(*) as count
          FROM information_schema.tables
          WHERE table_name = ${table}
        `;
        results.tables[table] = count[0].count > 0;
        console.log(`  ✅ ${table}: ${results.tables[table] ? '存在' : '不存在'}`);
      } catch (error) {
        results.tables[table] = false;
        results.errors.push(`表 ${table} 检查失败: ${error.message}`);
        console.log(`  ❌ ${table}: 检查失败`);
      }
    }

    // 2. 验证订阅计划数据
    console.log('\n📦 检查订阅计划数据...');
    try {
      const planCount = await prisma.subscriptionPlan.count();
      results.data.subscriptionPlans = planCount;
      console.log(`  ✅ 订阅计划数量: ${planCount}`);

      if (planCount >= 4) {
        const plans = await prisma.subscriptionPlan.findMany({
          select: { code: true, name: true, price: true },
        });
        console.log('  📋 订阅计划列表:');
        plans.forEach(plan => {
          console.log(`    - ${plan.code}: ${plan.name} (¥${plan.price})`);
        });
      }
    } catch (error) {
      results.errors.push(`订阅计划检查失败: ${error.message}`);
      console.log('  ❌ 订阅计划检查失败');
    }

    // 3. 验证租户订阅分配
    console.log('\n🏢 检查租户订阅分配...');
    try {
      const tenantCount = await prisma.tenant.count();
      const subscriptionCount = await prisma.tenantSubscription.count();

      results.data.tenants = tenantCount;
      results.data.subscriptions = subscriptionCount;

      console.log(`  ✅ 租户总数: ${tenantCount}`);
      console.log(`  ✅ 订阅记录: ${subscriptionCount}`);

      // 检查是否有租户没有订阅
      const tenantsWithoutSubscription = await prisma.tenant.findMany({
        where: {
          subscriptions: { none: {} },
        },
        select: { id: true, name: true },
      });

      if (tenantsWithoutSubscription.length === 0) {
        console.log('  ✅ 所有租户都已分配订阅');
      } else {
        console.log(`  ⚠️ ${tenantsWithoutSubscription.length} 个租户未分配订阅:`);
        tenantsWithoutSubscription.forEach(tenant => {
          console.log(`    - ID: ${tenant.id}, 名称: ${tenant.name}`);
        });
        results.errors.push(`${tenantsWithoutSubscription.length} 个租户未分配订阅`);
      }
    } catch (error) {
      results.errors.push(`租户订阅检查失败: ${error.message}`);
      console.log('  ❌ 租户订阅检查失败');
    }

    // 4. 验证系统官网
    console.log('\n🌐 检查系统官网设置...');
    try {
      const systemTenant = await prisma.tenant.findUnique({
        where: { id: 0 },
        include: { websites: true },
      });

      if (systemTenant) {
        console.log(`  ✅ 系统租户: ${systemTenant.name}`);
        console.log(`  ✅ 系统网站数量: ${systemTenant.websites.length}`);
        results.data.systemTenant = true;
      } else {
        console.log('  ❌ 系统租户不存在');
        results.data.systemTenant = false;
        results.errors.push('系统租户不存在');
      }
    } catch (error) {
      results.errors.push(`系统官网检查失败: ${error.message}`);
      console.log('  ❌ 系统官网检查失败');
    }

    // 5. 验证系统管理员
    console.log('\n👤 检查系统管理员...');
    try {
      const systemAdmin = await prisma.user.findFirst({
        where: {
          tenantId: 0,
          username: 'admin',
          userType: 'SYSTEM',
        },
        include: {
          roles: {
            include: {
              role: true,
            },
          },
        },
      });

      if (systemAdmin) {
        console.log(`  ✅ 系统管理员: ${systemAdmin.realName} (${systemAdmin.username})`);
        console.log(`  ✅ 管理员角色数量: ${systemAdmin.roles.length}`);
        results.data.systemAdmin = true;
      } else {
        console.log('  ❌ 系统管理员不存在');
        results.data.systemAdmin = false;
        results.errors.push('系统管理员不存在');
      }
    } catch (error) {
      results.errors.push(`系统管理员检查失败: ${error.message}`);
      console.log('  ❌ 系统管理员检查失败');
    }

    // 6. 验证系统配置
    console.log('\n⚙️ 检查系统配置...');
    try {
      const configCount = await prisma.systemConfig.count();
      results.data.systemConfigs = configCount;
      console.log(`  ✅ 系统配置数量: ${configCount}`);

      if (configCount > 0) {
        const configs = await prisma.systemConfig.findMany({
          select: { category: true, key: true, value: true },
        });

        const groupedConfigs = configs.reduce((acc, config) => {
          if (!acc[config.category]) acc[config.category] = [];
          acc[config.category].push(`${config.key}=${config.value}`);
          return acc;
        }, {});

        console.log('  📋 配置分类:');
        Object.entries(groupedConfigs).forEach(([category, configs]) => {
          console.log(`    - ${category}: ${configs.length} 项配置`);
        });
      }
    } catch (error) {
      results.errors.push(`系统配置检查失败: ${error.message}`);
      console.log('  ❌ 系统配置检查失败');
    }

    // 7. 验证数据库索引
    console.log('\n📊 检查数据库索引...');
    try {
      const indexes = await prisma.$queryRaw`
        SELECT
          t.relname as table_name,
          i.relname as index_name,
          pg_get_indexdef(i.oid) as index_definition
        FROM pg_class t, pg_class i, pg_index ix, pg_attribute a
        WHERE t.oid = ix.indrelid
          AND i.oid = ix.indexrelid
          AND a.attrelid = t.oid
          AND a.attnum = ANY(ix.indkey)
          AND t.relkind = 'r'
          AND t.relname IN ('subscription_plans', 'tenant_subscriptions', 'tenant_features', 'tenant_config')
        GROUP BY t.relname, i.relname, i.oid
        ORDER BY t.relname, i.relname;
      `;

      console.log(`  ✅ 新增表索引数量: ${indexes.length}`);
      results.data.indexes = indexes.length;
    } catch (error) {
      results.errors.push(`索引检查失败: ${error.message}`);
      console.log('  ❌ 索引检查失败');
    }

    // 总结报告
    console.log('\n📊 验证结果汇总');
    console.log('='.repeat(50));

    const allTablesExist = Object.values(results.tables).every(exists => exists);
    const hasSubscriptionPlans = results.data.subscriptionPlans >= 4;
    const hasSystemTenant = results.data.systemTenant === true;
    const hasSystemAdmin = results.data.systemAdmin === true;
    const hasSystemConfigs = results.data.systemConfigs > 0;

    console.log(`表结构: ${allTablesExist ? '✅ 通过' : '❌ 失败'}`);
    console.log(`订阅计划: ${hasSubscriptionPlans ? '✅ 通过' : '❌ 失败'}`);
    console.log(`系统租户: ${hasSystemTenant ? '✅ 通过' : '❌ 失败'}`);
    console.log(`系统管理员: ${hasSystemAdmin ? '✅ 通过' : '❌ 失败'}`);
    console.log(`系统配置: ${hasSystemConfigs ? '✅ 通过' : '❌ 失败'}`);

    if (results.errors.length > 0) {
      console.log('\n❌ 发现的问题:');
      results.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    const overallSuccess =
      allTablesExist &&
      hasSubscriptionPlans &&
      hasSystemTenant &&
      hasSystemAdmin &&
      hasSystemConfigs &&
      results.errors.length === 0;

    if (overallSuccess) {
      console.log('\n🎉 数据库升级验证通过！系统已准备就绪。');
      process.exit(0);
    } else {
      console.log('\n⚠️ 数据库升级验证失败，请检查上述问题。');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ 验证过程发生错误:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 执行验证
verifyDatabaseUpgrade().catch(console.error);
