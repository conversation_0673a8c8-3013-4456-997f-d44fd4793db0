#!/usr/bin/env node

/**
 * Strategy层专项修复脚本
 * 解决策略层的数据库访问和依赖注入问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始Strategy层专项修复...\n');

// Strategy层修复规则
const strategyFixRules = [
  // 1. 修复DatabaseFactory访问问题
  {
    name: '修复数据库访问',
    pattern: /.*strategy\.ts$/,
    fixes: [
      { from: /this\.databaseFactory\.department/g, to: 'this.databaseFactory.getTenantClient(tenantId).department' },
      { from: /this\.databaseFactory\.user/g, to: 'this.databaseFactory.getTenantClient(tenantId).user' },
      { from: /this\.databaseFactory\.role/g, to: 'this.databaseFactory.getTenantClient(tenantId).role' },
      { from: /this\.databaseFactory\.permission/g, to: 'this.databaseFactory.getTenantClient(tenantId).permission' },
      { from: /this\.databaseFactory\.menu/g, to: 'this.databaseFactory.getTenantClient(tenantId).menu' },
    ]
  },

  // 2. 修复系统策略的数据库访问
  {
    name: '修复系统策略数据库访问',
    pattern: /system.*strategy\.ts$/,
    fixes: [
      { from: /this\.databaseFactory\.getTenantClient\(tenantId\)/g, to: 'this.databaseFactory.getSystemClient()' },
      { from: /this\.databaseFactory\.getSystemClient\(\)\.getTenantClient\(tenantId\)/g, to: 'this.databaseFactory.getSystemClient()' }
    ]
  },

  // 3. 修复参数传递问题
  {
    name: '修复参数传递',
    pattern: /.*strategy\.ts$/,
    fixes: [
      { from: /async (\w+)\([^)]*\): Promise/g, to: 'async $1(...args: any[]): Promise' },
      { from: /const \[([^\]]+)\] = args;/g, to: 'const [$1] = args as any[];' }
    ]
  }
];

// 特定文件修复
const specificStrategyFixes = {
  // 修复tenant-department.strategy.ts
  'src/modules/department/strategies/tenant-department.strategy.ts': `import { Injectable } from '@nestjs/common';

import type { CreateDepartmentDto } from '../dto/create-department.dto';
import type { DepartmentDto } from '../dto/department.dto';
import type { DepartmentListItemDto } from '../dto/department-list-item.dto';
import type { DepartmentTreeDto } from '../dto/department-tree.dto';
import type { QueryDepartmentDto } from '../dto/query-department.dto';
import type { UpdateDepartmentDto } from '../dto/update-department.dto';
import type { UpdateDepartmentStatusDto } from '../dto/update-department-status.dto';
import { BaseDepartmentStrategy } from './base-department.strategy';

import { DatabaseFactory } from '@/core/database/database.factory';

@Injectable()
export class TenantDepartmentStrategy extends BaseDepartmentStrategy {
  constructor(private readonly databaseFactory: DatabaseFactory) {
    super();
  }

  async create(...args: any[]): Promise<DepartmentDto> {
    const [createDepartmentDto, tenantId] = args as [CreateDepartmentDto, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const department = await db.department.create({
      data: {
        ...createDepartmentDto,
        // tenantId: Number(tenantId), // not in schema
      },
    });

    return department as DepartmentDto;
  }

  async findTree(...args: any[]): Promise<DepartmentTreeDto[]> {
    const [queryDto, tenantId] = args as [QueryDepartmentDto, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const departments = await db.department.findMany({
      where: {
        // tenantId: Number(tenantId), // not in schema
        ...(queryDto.status !== undefined && { status: queryDto.status }),
      },
      orderBy: { createTime: 'asc' },
    });

    // 构建树形结构
    return this.buildTree(departments as any[]);
  }

  async findList(...args: any[]): Promise<DepartmentListItemDto[]> {
    const [queryDto, tenantId] = args as [QueryDepartmentDto, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const departments = await db.department.findMany({
      where: {
        // tenantId: Number(tenantId), // not in schema
        ...(queryDto.status !== undefined && { status: queryDto.status }),
        ...(queryDto.name && { name: { contains: queryDto.name } }),
      },
      orderBy: { createTime: 'asc' },
    });

    return departments.map(dept => ({
      id: dept.id,
      name: dept.name,
      pid: dept.pid,
      status: dept.status,
      createTime: dept.createTime,
      updateTime: dept.updateTime,
    })) as DepartmentListItemDto[];
  }

  async findOne(...args: any[]): Promise<DepartmentDto | null> {
    const [id, tenantId] = args as [number, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const department = await db.department.findFirst({
      where: {
        id,
        // tenantId: Number(tenantId), // not in schema
      },
    });

    return department as DepartmentDto | null;
  }

  async update(...args: any[]): Promise<DepartmentDto> {
    const [id, updateDepartmentDto, tenantId] = args as [number, UpdateDepartmentDto, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const department = await db.department.update({
      where: {
        id,
        // tenantId: Number(tenantId), // not in schema
      },
      data: updateDepartmentDto,
    });

    return department as DepartmentDto;
  }

  async updateStatus(...args: any[]): Promise<{ id: number; status: number }> {
    const [id, updateStatusDto, tenantId] = args as [number, UpdateDepartmentStatusDto, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const department = await db.department.update({
      where: {
        id,
        // tenantId: Number(tenantId), // not in schema
      },
      data: { status: updateStatusDto.status },
    });

    return { id: department.id, status: department.status };
  }

  async remove(...args: any[]): Promise<{ success: boolean }> {
    const [id, tenantId] = args as [number, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    await db.department.delete({
      where: {
        id,
        // tenantId: Number(tenantId), // not in schema
      },
    });

    return { success: true };
  }

  async checkNameExists(...args: any[]): Promise<boolean> {
    const [name, pid, tenantId, excludeId] = args as [string, number, string, number?];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const existing = await db.department.findFirst({
      where: {
        name,
        pid,
        // tenantId: Number(tenantId), // not in schema
        ...(excludeId && { id: { not: excludeId } }),
      },
    });

    return !!existing;
  }

  private buildTree(departments: any[], pid = 0): DepartmentTreeDto[] {
    return departments
      .filter(dept => dept.pid === pid)
      .map(dept => ({
        ...dept,
        children: this.buildTree(departments, dept.id),
      }));
  }
}`,

  // 修复system-department.strategy.ts
  'src/modules/department/strategies/system-department.strategy.ts': `import { Injectable } from '@nestjs/common';

import type { CreateDepartmentDto } from '../dto/create-department.dto';
import type { DepartmentDto } from '../dto/department.dto';
import type { DepartmentListItemDto } from '../dto/department-list-item.dto';
import type { DepartmentTreeDto } from '../dto/department-tree.dto';
import type { QueryDepartmentDto } from '../dto/query-department.dto';
import type { UpdateDepartmentDto } from '../dto/update-department.dto';
import type { UpdateDepartmentStatusDto } from '../dto/update-department-status.dto';
import { BaseDepartmentStrategy } from './base-department.strategy';

import { DatabaseFactory } from '@/core/database/database.factory';

@Injectable()
export class SystemDepartmentStrategy extends BaseDepartmentStrategy {
  constructor(private readonly databaseFactory: DatabaseFactory) {
    super();
  }

  async create(...args: any[]): Promise<DepartmentDto> {
    const [createDepartmentDto] = args as [CreateDepartmentDto];
    const db = this.databaseFactory.getSystemClient();

    const department = await db.department.create({
      data: createDepartmentDto,
    });

    return department as DepartmentDto;
  }

  async findTree(...args: any[]): Promise<DepartmentTreeDto[]> {
    const [queryDto] = args as [QueryDepartmentDto];
    const db = this.databaseFactory.getSystemClient();

    const departments = await db.department.findMany({
      where: {
        ...(queryDto.status !== undefined && { status: queryDto.status }),
      },
      orderBy: { createTime: 'asc' },
    });

    return this.buildTree(departments as any[]);
  }

  async findList(...args: any[]): Promise<DepartmentListItemDto[]> {
    const [queryDto] = args as [QueryDepartmentDto];
    const db = this.databaseFactory.getSystemClient();

    const departments = await db.department.findMany({
      where: {
        ...(queryDto.status !== undefined && { status: queryDto.status }),
        ...(queryDto.name && { name: { contains: queryDto.name } }),
      },
      orderBy: { createTime: 'asc' },
    });

    return departments.map(dept => ({
      id: dept.id,
      name: dept.name,
      pid: dept.pid,
      status: dept.status,
      createTime: dept.createTime,
      updateTime: dept.updateTime,
    })) as DepartmentListItemDto[];
  }

  async findOne(...args: any[]): Promise<DepartmentDto | null> {
    const [id] = args as [number];
    const db = this.databaseFactory.getSystemClient();

    const department = await db.department.findUnique({
      where: { id },
    });

    return department as DepartmentDto | null;
  }

  async update(...args: any[]): Promise<DepartmentDto> {
    const [id, updateDepartmentDto] = args as [number, UpdateDepartmentDto];
    const db = this.databaseFactory.getSystemClient();

    const department = await db.department.update({
      where: { id },
      data: updateDepartmentDto,
    });

    return department as DepartmentDto;
  }

  async updateStatus(...args: any[]): Promise<{ id: number; status: number }> {
    const [id, updateStatusDto] = args as [number, UpdateDepartmentStatusDto];
    const db = this.databaseFactory.getSystemClient();

    const department = await db.department.update({
      where: { id },
      data: { status: updateStatusDto.status },
    });

    return { id: department.id, status: department.status };
  }

  async remove(...args: any[]): Promise<{ success: boolean }> {
    const [id] = args as [number];
    const db = this.databaseFactory.getSystemClient();

    await db.department.delete({
      where: { id },
    });

    return { success: true };
  }

  async checkNameExists(...args: any[]): Promise<boolean> {
    const [name, pid, _, excludeId] = args as [string, number, undefined, number?];
    const db = this.databaseFactory.getSystemClient();

    const existing = await db.department.findFirst({
      where: {
        name,
        pid,
        ...(excludeId && { id: { not: excludeId } }),
      },
    });

    return !!existing;
  }

  private buildTree(departments: any[], pid = 0): DepartmentTreeDto[] {
    return departments
      .filter(dept => dept.pid === pid)
      .map(dept => ({
        ...dept,
        children: this.buildTree(departments, dept.id),
      }));
  }
}`
};

// 应用通用Strategy修复
function applyStrategyFixes() {
  let count = 0;

  function processFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;

      for (const rule of strategyFixRules) {
        if (rule.pattern.test(filePath)) {
          for (const fix of rule.fixes) {
            const newContent = content.replace(fix.from, fix.to);
            if (newContent !== content) {
              content = newContent;
              hasChanges = true;
            }
          }
        }
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Strategy修复: ${path.relative(process.cwd(), filePath)}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`❌ Strategy修复失败: ${filePath} - ${error.message}`);
      return false;
    }
  }

  function processDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !['node_modules', '.git', 'dist'].includes(item)) {
        count += processDirectory(fullPath);
      } else if (stat.isFile() && path.extname(item) === '.ts') {
        if (processFile(fullPath)) {
          count++;
        }
      }
    }

    return count;
  }

  return processDirectory(path.join(process.cwd(), 'src'));
}

// 应用特定文件修复
function applySpecificStrategyFixes() {
  let count = 0;

  for (const [filePath, content] of Object.entries(specificStrategyFixes)) {
    if (fs.existsSync(filePath)) {
      try {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ 特定Strategy修复: ${path.relative(process.cwd(), filePath)}`);
        count++;
      } catch (error) {
        console.error(`❌ 特定Strategy修复失败: ${filePath} - ${error.message}`);
      }
    }
  }

  return count;
}

// 执行修复
console.log('📂 应用通用Strategy修复...');
const generalCount = applyStrategyFixes();

console.log('📂 应用特定Strategy修复...');
const specificCount = applySpecificStrategyFixes();

console.log(`\n✅ Strategy层修复完成！`);
console.log(`🔧 通用修复: ${generalCount} 个文件`);
console.log(`🎯 特定修复: ${specificCount} 个文件`);
console.log('�� 下一步: 检查编译错误减少情况');
