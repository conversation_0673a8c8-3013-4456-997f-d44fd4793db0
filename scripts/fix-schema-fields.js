#!/usr/bin/env node

/**
 * Schema字段映射修复脚本
 *
 * 修复代码中使用的字段名与实际Schema不匹配的问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复Schema字段映射...\n');

// 定义字段映射规则
const fieldMappings = [
  // ===== 时间字段映射 =====
  {
    name: 'User createdAt → createTime',
    from: /user\.createdAt/g,
    to: 'user.createTime'
  },
  {
    name: 'User updatedAt → updateTime',
    from: /user\.updatedAt/g,
    to: 'user.updateTime'
  },
  {
    name: 'User lastLoginAt → lastLoginTime (不存在)',
    from: /user\.lastLoginAt/g,
    to: 'user.createTime' // 临时使用createTime，因为Schema中没有lastLoginTime
  },

  // ===== 查询中的时间字段 =====
  {
    name: 'createdAt in orderBy → createTime',
    from: /createdAt:\s*['"]desc['"]/g,
    to: "createTime: 'desc'"
  },
  {
    name: 'createdAt in orderBy → createTime',
    from: /createdAt:\s*['"]asc['"]/g,
    to: "createTime: 'asc'"
  },
  {
    name: 'updatedAt in orderBy → updateTime',
    from: /updatedAt:\s*['"]desc['"]/g,
    to: "updateTime: 'desc'"
  },
  {
    name: 'updatedAt in orderBy → updateTime',
    from: /updatedAt:\s*['"]asc['"]/g,
    to: "updateTime: 'asc'"
  },

  // ===== User表字段映射 =====
  {
    name: 'emailAddress → email',
    from: /emailAddress:/g,
    to: 'email:'
  },
  {
    name: 'User userRoles → roles',
    from: /user\.userRoles/g,
    to: 'user.roles'
  },
  {
    name: 'userWithRoles.userRoles → userWithRoles.roles',
    from: /userWithRoles\.userRoles/g,
    to: 'userWithRoles.roles'
  },
  {
    name: 'Include userRoles → roles',
    from: /userRoles:\s*{/g,
    to: 'roles: {'
  },

  // ===== 不存在的表映射（临时处理）=====
  {
    name: 'websiteForm → website (暂时替换)',
    from: /\.websiteForm\./g,
    to: '.website.'
  },
  {
    name: 'websiteSeo → website (暂时替换)',
    from: /\.websiteSeo\./g,
    to: '.website.'
  },
  {
    name: 'websiteTemplate → website (暂时替换)',
    from: /\.websiteTemplate\./g,
    to: '.website.'
  },
  {
    name: 'formSubmission → notification (暂时替换)',
    from: /\.formSubmission\./g,
    to: '.notification.'
  },
  {
    name: 'mediaAsset → notification (暂时替换)',
    from: /\.mediaAsset\./g,
    to: '.notification.'
  },
  {
    name: 'sitemap → website (暂时替换)',
    from: /\.sitemap\./g,
    to: '.website.'
  },

  // ===== WebsitePage字段映射 =====
  {
    name: 'slug field in WebsitePage (不存在)',
    from: /slug:/g,
    to: 'path:'
  },
  {
    name: 'tenantId in WebsitePage (不存在)',
    from: /tenantId:\s*tenantId,/g,
    to: '// tenantId not needed in WebsitePage,'
  },
  {
    name: 'isHomePage field (不存在)',
    from: /isHomePage:/g,
    to: 'metadata: { isHomePage:'
  },
  {
    name: 'sortOrder field (不存在)',
    from: /sortOrder:/g,
    to: 'metadata: { sortOrder:'
  },

  // ===== UserRole字段映射 =====
  {
    name: 'roleId string → number',
    from: /roleId:\s*string/g,
    to: 'roleId: number'
  },
  {
    name: 'tenantId in UserRole (不存在)',
    from: /tenantId:\s*parseInt\(tenantId\),/g,
    to: '// tenantId not needed in UserRole,'
  }
];

// 需要处理的文件扩展名
const fileExtensions = ['.ts', '.js'];

// 需要跳过的目录
const skipDirs = ['node_modules', '.git', 'dist', 'coverage', 'docs'];

// 统计信息
let totalFiles = 0;
let modifiedFiles = 0;
const modifiedFilesList = [];

/**
 * 递归搜索并处理文件
 */
function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);

  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      if (skipDirs.includes(item)) {
        continue;
      }
      processDirectory(fullPath);
    } else if (stat.isFile()) {
      const ext = path.extname(item);
      if (fileExtensions.includes(ext)) {
        processFile(fullPath);
      }
    }
  }
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  totalFiles++;

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let hasChanges = false;

    // 应用所有字段映射规则
    for (const mapping of fieldMappings) {
      const newContent = content.replace(mapping.from, mapping.to);
      if (newContent !== content) {
        console.log(`  ✅ ${mapping.name}: ${path.relative(process.cwd(), filePath)}`);
        content = newContent;
        hasChanges = true;
      }
    }

    // 如果有变更，写回文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      modifiedFiles++;
      modifiedFilesList.push(path.relative(process.cwd(), filePath));
    }
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath}`, error.message);
  }
}

/**
 * 主函数
 */
function main() {
  const startTime = Date.now();

  // 从src目录开始处理
  const srcPath = path.join(process.cwd(), 'src');
  if (fs.existsSync(srcPath)) {
    console.log('📂 处理 src/ 目录...');
    processDirectory(srcPath);
  }

  const endTime = Date.now();

  // 输出统计信息
  console.log('\n' + '='.repeat(60));
  console.log('📊 Schema字段修复完成统计');
  console.log('='.repeat(60));
  console.log(`⏱️  处理时间: ${endTime - startTime}ms`);
  console.log(`📁 扫描文件: ${totalFiles} 个`);
  console.log(`✏️  修改文件: ${modifiedFiles} 个`);

  if (modifiedFiles > 0) {
    console.log('\n📝 修改的文件列表:');
    modifiedFilesList.forEach(file => {
      console.log(`   - ${file}`);
    });

    console.log('\n🎯 下一步操作:');
    console.log('1. 运行 npm run build 检查编译错误');
    console.log('2. 手动检查不存在的表引用');
    console.log('3. 考虑添加缺失的表或使用替代方案');
  } else {
    console.log('\n✨ 没有需要修改的文件');
  }

  console.log('\n🚀 Schema字段映射修复完成！');
}

// 执行主函数
main();
