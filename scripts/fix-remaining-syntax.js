#!/usr/bin/env node

/**
 * 修复剩余语法错误脚本
 * 专门处理异常抛出、控制结构等剩余语法问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复剩余语法错误...\n');

// 剩余语法修复规则
const remainingSyntaxFixes = [
  // 修复异常抛出语句的多余花括号
  {
    from: /throw new (\w+Exception)\(([^}]+)\s*}\s*\)/g,
    to: 'throw new $1($2);'
  },

  // 修复字符串模板后的多余花括号
  {
    from: /`([^`]+)`\s*}\s*\)/g,
    to: '`$1`);'
  },

  // 修复 if 语句后缺少花括号
  {
    from: /if\s*\([^)]+\)\s*{\s*$/gm,
    to: function(match) {
      return match.replace(/{\s*$/, ' {');
    }
  },

  // 修复方法调用后的语法错误
  {
    from: /(\w+\.[a-zA-Z]+\([^)]*\))\s*;\s*}/g,
    to: '$1;'
  },

  // 修复对象属性后的语法错误
  {
    from: /(\w+:\s*[^,}]+)\s*}\s*\)/g,
    to: '$1'
  },

  // 修复函数参数中的多余花括号
  {
    from: /\(([^)]+)\s*}\s*\)/g,
    to: '($1)'
  },

  // 修复return语句后的多余符号
  {
    from: /return\s+([^;}]+);?\s*}\s*$/gm,
    to: 'return $1;'
  }
];

// 特定文件修复
const specificFileFixes = [
  {
    file: 'src/modules/website-template/website-template.service.ts',
    fixes: [
      { from: /throw new ConflictException\(`模板名称 "\$\{createDto\.name\}" 已存在`\s*\}\s*\)/g, to: 'throw new ConflictException(`模板名称 "${createDto.name}" 已存在`);' }
    ]
  }
];

// 应用剩余语法修复
function applyRemainingSyntaxFixes() {
  let count = 0;

  function processFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;

      for (const fix of remainingSyntaxFixes) {
        const newContent = content.replace(fix.from, fix.to);
        if (newContent !== content) {
          content = newContent;
          hasChanges = true;
        }
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ 剩余语法修复: ${path.relative(process.cwd(), filePath)}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`❌ 剩余语法修复失败: ${filePath} - ${error.message}`);
      return false;
    }
  }

  function processDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !['node_modules', '.git', 'dist'].includes(item)) {
        count += processDirectory(fullPath);
      } else if (stat.isFile() && path.extname(item) === '.ts') {
        if (processFile(fullPath)) {
          count++;
        }
      }
    }

    return count;
  }

  return processDirectory(path.join(process.cwd(), 'src'));
}

// 应用特定文件修复
function applySpecificFileFixes() {
  let count = 0;

  for (const { file, fixes } of specificFileFixes) {
    if (fs.existsSync(file)) {
      try {
        let content = fs.readFileSync(file, 'utf8');
        let hasChanges = false;

        for (const fix of fixes) {
          const newContent = content.replace(fix.from, fix.to);
          if (newContent !== content) {
            content = newContent;
            hasChanges = true;
          }
        }

        if (hasChanges) {
          fs.writeFileSync(file, content, 'utf8');
          console.log(`✅ 特定文件修复: ${path.relative(process.cwd(), file)}`);
          count++;
        }
      } catch (error) {
        console.error(`❌ 特定文件修复失败: ${file} - ${error.message}`);
      }
    }
  }

  return count;
}

// 手动修复严重的语法错误文件
function manualFixCriticalFiles() {
  const criticalFixes = [
    {
      file: 'src/modules/website-template/website-template.service.ts',
      action: () => {
        const filePath = 'src/modules/website-template/website-template.service.ts';
        if (!fs.existsSync(filePath)) return false;

        let content = fs.readFileSync(filePath, 'utf8');

        // 手动修复已知的错误模式
        content = content.replace(
          /throw new ConflictException\(`模板名称 "\$\{createDto\.name\}" 已存在`\s*\}\s*\)/g,
          'throw new ConflictException(`模板名称 "${createDto.name}" 已存在`);'
        );

        // 修复其他语法问题
        content = content.replace(/}\s*\)\s*$/gm, '}');
        content = content.replace(/;\s*}\s*$/gm, ';');

        fs.writeFileSync(filePath, content, 'utf8');
        return true;
      }
    }
  ];

  let count = 0;
  for (const { file, action } of criticalFixes) {
    try {
      if (action()) {
        console.log(`✅ 手动修复: ${path.relative(process.cwd(), file)}`);
        count++;
      }
    } catch (error) {
      console.error(`❌ 手动修复失败: ${file} - ${error.message}`);
    }
  }

  return count;
}

// 执行修复
console.log('📂 应用剩余语法修复...');
const remainingCount = applyRemainingSyntaxFixes();

console.log('📂 应用特定文件修复...');
const specificCount = applySpecificFileFixes();

console.log('📂 手动修复关键文件...');
const manualCount = manualFixCriticalFiles();

console.log(`\n✅ 剩余语法错误修复完成！`);
console.log(`🔧 剩余语法修复: ${remainingCount} 个文件`);
console.log(`🎯 特定文件修复: ${specificCount} 个文件`);
console.log(`⚙️ 手动修复: ${manualCount} 个文件`);
console.log('🚀 下一步: 测试编译效果');
