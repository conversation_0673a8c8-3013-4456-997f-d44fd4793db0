#!/usr/bin/env node

/**
 * 关键语法错误修复脚本
 * 修复阻止服务编译的基础语法问题
 */

const fs = require('fs');
const path = require('path');

console.log('🚨 开始修复关键语法错误...\n');

// 关键语法修复规则
const criticalSyntaxFixes = [
  // 修复多余的花括号 - 函数调用后的 })
  {
    from: /(\w+\([^)]*\))\s*}\s*\)/g,
    to: '$1)'
  },

  // 修复 return 语句后的多余花括号
  {
    from: /return\s+([^;]+)\s*}\s*\)/g,
    to: 'return $1;'
  },

  // 修复 this.xxx(...) }) 模式
  {
    from: /this\.(\w+\([^)]*\))\s*}\s*\)/g,
    to: 'this.$1;'
  },

  // 修复字符串后的多余花括号
  {
    from: /('[^']*')\s*}\s*\)/g,
    to: '$1);'
  },

  // 修复方法调用后的 })
  {
    from: /\.(\w+\([^)]*\))\s*}\s*\)/g,
    to: '.$1;'
  },

  // 修复数组后的多余花括号
  {
    from: /(\[[^\]]*\])\s*}\s*\)/g,
    to: '$1);'
  }
];

// 特定文件的精确修复
const specificFixes = [
  {
    file: 'src/app.service.ts',
    fixes: [
      { from: /return this\.health\.check\(\[\] \}\)/g, to: 'return this.health.check([]);' }
    ]
  },

  {
    file: 'src/core/auth/services/auth.service.ts',
    fixes: [
      { from: /this\.logger\.debug\('未检测到租户信息，将使用系统认证策略' \}\)/g, to: "this.logger.debug('未检测到租户信息，将使用系统认证策略');" },
      { from: /return strategy\.login\(loginDto \}\)/g, to: 'return strategy.login(loginDto);' },
      { from: /return strategy\.refreshToken\(user\.userId, user\.tenantId \}\)/g, to: 'return strategy.refreshToken(user.userId, user.tenantId);' },
      { from: /return strategy\.getUserInfo\(user\.userId, user\.tenantId \}\)/g, to: 'return strategy.getUserInfo(user.userId, user.tenantId);' },
      { from: /return strategy\.getPermissionCodes\(user\.userId, user\.tenantId \}\)/g, to: 'return strategy.getPermissionCodes(user.userId, user.tenantId);' }
    ]
  }
];

// 应用通用语法修复
function applyCriticalSyntaxFixes() {
  let count = 0;

  function processFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;

      for (const fix of criticalSyntaxFixes) {
        const newContent = content.replace(fix.from, fix.to);
        if (newContent !== content) {
          content = newContent;
          hasChanges = true;
        }
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ 语法修复: ${path.relative(process.cwd(), filePath)}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`❌ 语法修复失败: ${filePath} - ${error.message}`);
      return false;
    }
  }

  function processDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !['node_modules', '.git', 'dist'].includes(item)) {
        count += processDirectory(fullPath);
      } else if (stat.isFile() && path.extname(item) === '.ts') {
        if (processFile(fullPath)) {
          count++;
        }
      }
    }

    return count;
  }

  return processDirectory(path.join(process.cwd(), 'src'));
}

// 应用特定文件修复
function applySpecificFixes() {
  let count = 0;

  for (const { file, fixes } of specificFixes) {
    if (fs.existsSync(file)) {
      try {
        let content = fs.readFileSync(file, 'utf8');
        let hasChanges = false;

        for (const fix of fixes) {
          const newContent = content.replace(fix.from, fix.to);
          if (newContent !== content) {
            content = newContent;
            hasChanges = true;
          }
        }

        if (hasChanges) {
          fs.writeFileSync(file, content, 'utf8');
          console.log(`✅ 特定修复: ${path.relative(process.cwd(), file)}`);
          count++;
        }
      } catch (error) {
        console.error(`❌ 特定修复失败: ${file} - ${error.message}`);
      }
    }
  }

  return count;
}

// 修复明显的async关键字问题
function fixAsyncKeywordIssues() {
  let count = 0;

  // 检查问题文件
  const problematicFiles = [
    'src/modules/website-seo/website-seo.service.ts'
  ];

  for (const file of problematicFiles) {
    if (fs.existsSync(file)) {
      try {
        let content = fs.readFileSync(file, 'utf8');
        let hasChanges = false;

        // 修复缺失的分号和花括号不匹配
        content = content.replace(/return this\.mapToEntity\(seo \}\)/g, 'return this.mapToEntity(seo);');

        // 确保async方法格式正确
        content = content.replace(/^(\s*)async\s+(\w+)\s*\(/gm, '$1async $2(');

        if (content !== fs.readFileSync(file, 'utf8')) {
          fs.writeFileSync(file, content, 'utf8');
          console.log(`✅ Async修复: ${path.relative(process.cwd(), file)}`);
          count++;
          hasChanges = true;
        }
      } catch (error) {
        console.error(`❌ Async修复失败: ${file} - ${error.message}`);
      }
    }
  }

  return count;
}

// 执行修复
console.log('📂 应用通用语法修复...');
const generalCount = applyCriticalSyntaxFixes();

console.log('📂 应用特定文件修复...');
const specificCount = applySpecificFixes();

console.log('📂 修复async关键字问题...');
const asyncCount = fixAsyncKeywordIssues();

console.log(`\n✅ 关键语法错误修复完成！`);
console.log(`🔧 通用修复: ${generalCount} 个文件`);
console.log(`🎯 特定修复: ${specificCount} 个文件`);
console.log(`⚡ Async修复: ${asyncCount} 个文件`);
console.log('🚀 下一步: 测试编译效果');