#!/usr/bin/env node

/**
 * 最终快速修复脚本
 * 精确处理核心错误，不引入新问题
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始最终快速修复...\n');

// 精确修复规则 - 只修复明确的错误
const preciseFixRules = [
  // 1. 修复department.service.ts语法错误
  {
    file: 'src/modules/department/department.service.ts',
    fixes: [
      { from: /async\s+query/g, to: 'query' },
      { from: /\[\s*query\s*\]/g, to: '[queryDto]' },
      { from: /Cannot find name 'query'/g, to: 'queryDto' }
    ]
  },

  // 2. 修复logging.service.ts多余括号
  {
    file: 'src/core/logging/logging.service.ts',
    fixes: [
      { from: /}\s*\)\s*;\s*}\s*\)/g, to: '})' },
      { from: /;\s*}\s*\)\s*$/gm, to: '}' }
    ]
  },

  // 3. 修复media-asset.controller.ts未定义变量
  {
    file: 'src/modules/media-asset/media-asset.controller.ts',
    fixes: [
      { from: /websiteId(?![a-zA-Z0-9_])/g, to: 'id' }
    ]
  }
];

// 通用修复规则 - 只处理安全的替换
const safeFixRules = [
  // 移除多余的装饰器
  {
    pattern: /.*\.ts$/,
    fixes: [
      { from: /@Inject\('DATABASE_FACTORY'\)\s*/g, to: '' }
    ]
  },

  // 修复引号风格（只在error语句中）
  {
    pattern: /.*\.ts$/,
    fixes: [
      { from: /throw new Error\("([^"]+)"\)/g, to: "throw new Error('$1')" }
    ]
  }
];

// 应用精确修复
function applyPreciseFixes() {
  let count = 0;

  for (const rule of preciseFixRules) {
    if (fs.existsSync(rule.file)) {
      try {
        let content = fs.readFileSync(rule.file, 'utf8');
        let hasChanges = false;

        for (const fix of rule.fixes) {
          const newContent = content.replace(fix.from, fix.to);
          if (newContent !== content) {
            content = newContent;
            hasChanges = true;
          }
        }

        if (hasChanges) {
          fs.writeFileSync(rule.file, content, 'utf8');
          console.log(`✅ 精确修复: ${path.relative(process.cwd(), rule.file)}`);
          count++;
        }
      } catch (error) {
        console.error(`❌ 精确修复失败: ${rule.file} - ${error.message}`);
      }
    }
  }

  return count;
}

// 应用安全修复
function applySafeFixes() {
  let count = 0;

  function processFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;

      for (const rule of safeFixRules) {
        if (rule.pattern.test(filePath)) {
          for (const fix of rule.fixes) {
            const newContent = content.replace(fix.from, fix.to);
            if (newContent !== content) {
              content = newContent;
              hasChanges = true;
            }
          }
        }
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, content, 'utf8');
        return true;
      }
      return false;
    } catch (error) {
      console.error(`❌ 安全修复失败: ${filePath} - ${error.message}`);
      return false;
    }
  }

  function processDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !['node_modules', '.git', 'dist'].includes(item)) {
        count += processDirectory(fullPath);
      } else if (stat.isFile() && path.extname(item) === '.ts') {
        if (processFile(fullPath)) {
          count++;
        }
      }
    }

    return count;
  }

  return processDirectory(path.join(process.cwd(), 'src'));
}

// 回退损坏的更改
function rollbackDamagedFiles() {
  const problematicFiles = [
    'src/modules/department/department.service.ts'
  ];

  let count = 0;
  for (const file of problematicFiles) {
    if (fs.existsSync(file)) {
      try {
        let content = fs.readFileSync(file, 'utf8');
        let hasChanges = false;

        // 修复async关键字问题
        content = content.replace(/(\s+)async(\s+)(?!function|[\(\w])/g, '$1');

        // 修复query变量名问题
        content = content.replace(/\[query\]/g, '[queryDto]');
        content = content.replace(/query,/g, 'queryDto,');

        if (hasChanges) {
          fs.writeFileSync(file, content, 'utf8');
          console.log(`✅ 回退修复: ${path.relative(process.cwd(), file)}`);
          count++;
        }
      } catch (error) {
        console.error(`❌ 回退失败: ${file} - ${error.message}`);
      }
    }
  }

  return count;
}

// 执行修复
console.log('📂 执行精确修复...');
const preciseCount = applyPreciseFixes();

console.log('📂 执行安全修复...');
const safeCount = applySafeFixes();

console.log('📂 回退问题文件...');
const rollbackCount = rollbackDamagedFiles();

console.log(`\n✅ 最终修复完成！`);
console.log(`🎯 精确修复: ${preciseCount} 个文件`);
console.log(`🔧 安全修复: ${safeCount} 个文件`);
console.log(`🔙 回退修复: ${rollbackCount} 个文件`);
console.log('🚀 下一步: 编译检查效果');
