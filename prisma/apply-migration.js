const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// 读取环境变量
require('dotenv').config();

// 数据库连接配置
const dbConfig = {
  user: process.env.DATABASE_USER || 'postgres',
  host: process.env.DATABASE_HOST || 'localhost',
  database: process.env.DATABASE_NAME || 'database',
  password: process.env.DATABASE_PASSWORD || 'postgres',
  port: parseInt(process.env.DATABASE_PORT || '5432'),
};

// 迁移脚本路径
const migrationPath = path.join(__dirname, 'migrations', 'manual', 'role_id_to_uuid.sql');

async function applyMigration() {
  const client = new Client(dbConfig);
  
  try {
    // 连接数据库
    await client.connect();
    console.log('Connected to database');
    
    // 读取迁移脚本
    const sql = fs.readFileSync(migrationPath, 'utf8');
    console.log(`Read migration script from ${migrationPath}`);
    
    // 执行迁移脚本
    console.log('Applying migration...');
    await client.query(sql);
    
    console.log('Migration applied successfully');
  } catch (error) {
    console.error('Error applying migration:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await client.end();
    console.log('Database connection closed');
  }
}

applyMigration();
