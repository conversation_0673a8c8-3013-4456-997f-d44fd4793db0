/**
 * 创建默认租户功能
 * @param {import('@prisma/client').PrismaClient} prisma Prisma客户端
 * @param {number} tenantId 租户ID
 */
async function seedTenantFeatures(prisma, tenantId) {
  console.log(`开始创建租户 ${tenantId} 的功能权限...`);

  // 先删除该租户的所有功能权限
  await prisma.tenantFeature.deleteMany({
    where: {
      tenantId
    }
  });

  // 创建AI PPT功能
  await prisma.tenantFeature.create({
    data: {
      tenantId,
      featureCode: 'ai.ppt',
      enabled: true,
      quota: 50,
      usedQuota: 0,
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 一年后过期
      config: {
        allowedTemplates: ['basic', 'professional']
      }
    }
  });

  // 创建AI文档功能
  await prisma.tenantFeature.create({
    data: {
      tenantId,
      featureCode: 'ai.document',
      enabled: true,
      quota: 20,
      usedQuota: 0,
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 一年后过期
      config: {}
    }
  });

  // 创建邮件配置功能
  await prisma.tenantFeature.create({
    data: {
      tenantId,
      featureCode: 'tenant-config.email',
      enabled: true,
      config: {}
    }
  });

  // 创建短信配置功能
  await prisma.tenantFeature.create({
    data: {
      tenantId,
      featureCode: 'tenant-config.sms',
      enabled: true,
      config: {}
    }
  });

  console.log(`租户 ${tenantId} 的功能权限创建完成`);
}

/**
 * 创建功能使用记录
 * @param {import('@prisma/client').PrismaClient} prisma Prisma客户端
 * @param {number} tenantId 租户ID
 */
async function seedFeatureUsage(prisma, tenantId) {
  console.log(`开始创建租户 ${tenantId} 的功能使用记录...`);

  // 先删除该租户的所有功能使用记录
  await prisma.tenantFeatureUsage.deleteMany({
    where: {
      tenantId
    }
  });

  // 创建一些AI PPT功能使用记录
  const now = new Date();
  
  for (let i = 0; i < 5; i++) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    await prisma.tenantFeatureUsage.create({
      data: {
        tenantId,
        featureCode: 'ai.ppt',
        userId: 1, // 租户管理员ID
        metadata: {
          templateName: `示例PPT ${i + 1}`,
          slideCount: 10 + i,
          duration: (60 + i * 10) + '秒'
        },
        createdAt: date
      }
    });
  }

  // 创建一些AI文档功能使用记录
  for (let i = 0; i < 3; i++) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    await prisma.tenantFeatureUsage.create({
      data: {
        tenantId,
        featureCode: 'ai.document',
        userId: 1, // 租户管理员ID
        metadata: {
          documentName: `示例文档 ${i + 1}`,
          wordCount: 500 + i * 100,
          duration: (30 + i * 5) + '秒'
        },
        createdAt: date
      }
    });
  }

  console.log(`租户 ${tenantId} 的功能使用记录创建完成`);
}

module.exports = { seedTenantFeatures, seedFeatureUsage };
