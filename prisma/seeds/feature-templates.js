/**
 * 创建默认功能模板
 * @param {import('@prisma/client').PrismaClient} prisma Prisma客户端
 */
async function seedFeatureTemplates(prisma) {
  console.log('开始创建功能模板...');

  // 基础版模板
  await prisma.featureTemplate.upsert({
    where: { code: 'basic' },
    update: {},
    create: {
      code: 'basic',
      name: '基础版',
      features: {
        'ai.ppt': {
          enabled: true,
          quota: 10,
          config: {
            allowedTemplates: ['basic']
          }
        },
        'tenant-config.email': {
          enabled: true
        }
      },
      isActive: true
    }
  });

  // 专业版模板
  await prisma.featureTemplate.upsert({
    where: { code: 'pro' },
    update: {},
    create: {
      code: 'pro',
      name: '专业版',
      features: {
        'ai.ppt': {
          enabled: true,
          quota: 50,
          config: {
            allowedTemplates: ['basic', 'professional']
          }
        },
        'ai.document': {
          enabled: true,
          quota: 20
        },
        'tenant-config.email': {
          enabled: true
        },
        'tenant-config.sms': {
          enabled: true
        }
      },
      isActive: true
    }
  });

  // 企业版模板
  await prisma.featureTemplate.upsert({
    where: { code: 'enterprise' },
    update: {},
    create: {
      code: 'enterprise',
      name: '企业版',
      features: {
        'ai.ppt': {
          enabled: true,
          quota: null, // 无限制
          config: {
            allowedTemplates: ['basic', 'professional', 'advanced']
          }
        },
        'ai.document': {
          enabled: true,
          quota: null // 无限制
        },
        'tenant-config.email': {
          enabled: true
        },
        'tenant-config.sms': {
          enabled: true
        },
        'tenant-config.oss': {
          enabled: true
        },
        'tenant-config.payment': {
          enabled: true
        }
      },
      isActive: true
    }
  });

  console.log('功能模板创建完成');
}

module.exports = { seedFeatureTemplates };
