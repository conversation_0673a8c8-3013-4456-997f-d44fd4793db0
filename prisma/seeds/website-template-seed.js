const websiteTemplateSeeds = [
  {
    name: '企业官网模板',
    description: '适合中小企业的现代化官网模板，包含公司介绍、产品展示、联系方式等页面',
    category: 'business',
    industry: 'tech',
    thumbnail: '/templates/business-tech-1.jpg',
    preview: '/preview/business-tech-1',
    config: {
      layout: 'modern',
      colorScheme: 'blue',
      header: {
        type: 'fixed',
        showLogo: true,
        showNavigation: true,
        navigationItems: ['首页', '关于我们', '产品服务', '新闻动态', '联系我们'],
      },
      footer: {
        showCopyright: true,
        showSocialLinks: true,
        socialLinks: ['微信', '微博', '邮箱'],
      },
      pages: [
        {
          name: 'home',
          title: '首页',
          isDefault: true,
          components: ['hero', 'features', 'testimonials'],
        },
        { name: 'about', title: '关于我们', components: ['intro', 'team', 'history'] },
        { name: 'products', title: '产品服务', components: ['product-grid', 'features'] },
        { name: 'news', title: '新闻动态', components: ['news-list', 'sidebar'] },
        { name: 'contact', title: '联系我们', components: ['contact-form', 'map', 'info'] },
      ],
    },
    features: {
      responsive: true,
      seoFriendly: true,
      hasEcommerce: false,
      hasBlog: true,
      hasContactForm: true,
      complexity: 'medium',
      multiLanguage: false,
      customizable: true,
    },
    isPremium: false,
    isActive: true,
    sortOrder: 1,
    metadata: {
      author: 'FlexiHub Team',
      version: '1.0.0',
      license: 'MIT',
      tags: ['企业', '现代', '响应式', '蓝色主题'],
      estimatedBuildTime: '2-3小时',
    },
  },
  {
    name: '个人作品集模板',
    description: '展示个人作品和技能的精美模板，适合设计师、开发者、摄影师等创意人士',
    category: 'portfolio',
    industry: 'creative',
    thumbnail: '/templates/portfolio-creative-1.jpg',
    preview: '/preview/portfolio-creative-1',
    config: {
      layout: 'minimal',
      colorScheme: 'dark',
      header: {
        type: 'overlay',
        showLogo: true,
        showNavigation: true,
        navigationItems: ['作品', '关于', '服务', '联系'],
      },
      footer: {
        showCopyright: true,
        showSocialLinks: true,
        socialLinks: ['Instagram', 'Behance', 'LinkedIn'],
      },
      pages: [
        {
          name: 'home',
          title: '首页',
          isDefault: true,
          components: ['hero-slider', 'featured-works'],
        },
        { name: 'portfolio', title: '作品集', components: ['gallery-grid', 'filter'] },
        { name: 'about', title: '关于我', components: ['bio', 'skills', 'experience'] },
        { name: 'services', title: '服务', components: ['service-cards', 'pricing'] },
        { name: 'contact', title: '联系我', components: ['contact-form', 'social-links'] },
      ],
    },
    features: {
      responsive: true,
      seoFriendly: true,
      hasEcommerce: false,
      hasBlog: false,
      hasContactForm: true,
      complexity: 'high',
      multiLanguage: false,
      customizable: true,
      hasGallery: true,
    },
    isPremium: true,
    isActive: true,
    sortOrder: 2,
    metadata: {
      author: 'FlexiHub Team',
      version: '1.2.0',
      license: 'Premium',
      tags: ['作品集', '创意', '暗色主题', '画廊'],
      estimatedBuildTime: '4-6小时',
    },
  },
  {
    name: '电商购物模板',
    description: '功能完整的电商网站模板，包含商品展示、购物车、订单管理等功能',
    category: 'ecommerce',
    industry: 'retail',
    thumbnail: '/templates/ecommerce-retail-1.jpg',
    preview: '/preview/ecommerce-retail-1',
    config: {
      layout: 'standard',
      colorScheme: 'orange',
      header: {
        type: 'standard',
        showLogo: true,
        showNavigation: true,
        showSearch: true,
        showCart: true,
        navigationItems: ['首页', '商品分类', '品牌', '特价', '帮助中心'],
      },
      footer: {
        showCopyright: true,
        showSocialLinks: true,
        showPaymentMethods: true,
        links: ['客服中心', '退换货政策', '隐私政策', '配送信息'],
      },
      pages: [
        {
          name: 'home',
          title: '首页',
          isDefault: true,
          components: ['banner', 'categories', 'featured-products', 'promotions'],
        },
        {
          name: 'products',
          title: '商品列表',
          components: ['product-grid', 'filters', 'pagination'],
        },
        {
          name: 'product-detail',
          title: '商品详情',
          components: ['product-images', 'product-info', 'reviews', 'related-products'],
        },
        { name: 'cart', title: '购物车', components: ['cart-items', 'checkout-summary'] },
        { name: 'checkout', title: '结算', components: ['shipping-form', 'payment-methods'] },
        {
          name: 'account',
          title: '我的账户',
          components: ['user-info', 'order-history', 'wishlist'],
        },
      ],
    },
    features: {
      responsive: true,
      seoFriendly: true,
      hasEcommerce: true,
      hasBlog: false,
      hasContactForm: true,
      complexity: 'high',
      multiLanguage: true,
      customizable: true,
      hasPayment: true,
      hasInventory: true,
      hasReviews: true,
    },
    isPremium: true,
    isActive: true,
    sortOrder: 3,
    metadata: {
      author: 'FlexiHub Team',
      version: '2.0.0',
      license: 'Premium',
      tags: ['电商', '购物', '零售', '橙色主题', '支付'],
      estimatedBuildTime: '1-2周',
    },
  },
  {
    name: '博客写作模板',
    description: '简洁优雅的博客模板，专注于内容展示和阅读体验',
    category: 'blog',
    industry: 'media',
    thumbnail: '/templates/blog-media-1.jpg',
    preview: '/preview/blog-media-1',
    config: {
      layout: 'clean',
      colorScheme: 'green',
      header: {
        type: 'simple',
        showLogo: true,
        showNavigation: true,
        navigationItems: ['首页', '文章', '分类', '标签', '关于', '联系'],
      },
      footer: {
        showCopyright: true,
        showSocialLinks: true,
        showSubscribe: true,
        socialLinks: ['微信公众号', '微博', 'RSS'],
      },
      pages: [
        {
          name: 'home',
          title: '首页',
          isDefault: true,
          components: ['featured-posts', 'recent-posts', 'sidebar'],
        },
        {
          name: 'post',
          title: '文章详情',
          components: ['post-content', 'author-bio', 'comments', 'related-posts'],
        },
        { name: 'category', title: '分类页面', components: ['post-list', 'pagination', 'sidebar'] },
        { name: 'tag', title: '标签页面', components: ['post-list', 'tag-cloud'] },
        { name: 'about', title: '关于', components: ['author-info', 'social-links'] },
        { name: 'archive', title: '归档', components: ['post-archive', 'search'] },
      ],
    },
    features: {
      responsive: true,
      seoFriendly: true,
      hasEcommerce: false,
      hasBlog: true,
      hasContactForm: true,
      complexity: 'low',
      multiLanguage: false,
      customizable: true,
      hasComments: true,
      hasSearch: true,
      hasRSS: true,
    },
    isPremium: false,
    isActive: true,
    sortOrder: 4,
    metadata: {
      author: 'FlexiHub Team',
      version: '1.1.0',
      license: 'MIT',
      tags: ['博客', '写作', '绿色主题', '简洁'],
      estimatedBuildTime: '1-2小时',
    },
  },
  {
    name: '餐厅美食模板',
    description: '专为餐厅、咖啡馆设计的美食展示模板，突出菜品和用餐氛围',
    category: 'business',
    industry: 'food',
    thumbnail: '/templates/restaurant-food-1.jpg',
    preview: '/preview/restaurant-food-1',
    config: {
      layout: 'elegant',
      colorScheme: 'warm',
      header: {
        type: 'hero',
        showLogo: true,
        showNavigation: true,
        navigationItems: ['首页', '菜单', '预订', '关于我们', '联系我们'],
      },
      footer: {
        showCopyright: true,
        showSocialLinks: true,
        showLocation: true,
        showHours: true,
      },
      pages: [
        {
          name: 'home',
          title: '首页',
          isDefault: true,
          components: ['hero-video', 'featured-dishes', 'chef-intro', 'testimonials'],
        },
        { name: 'menu', title: '菜单', components: ['menu-categories', 'dish-gallery', 'prices'] },
        {
          name: 'reservation',
          title: '预订',
          components: ['booking-form', 'availability-calendar'],
        },
        {
          name: 'about',
          title: '关于我们',
          components: ['restaurant-story', 'chef-team', 'awards'],
        },
        { name: 'contact', title: '联系我们', components: ['contact-info', 'map', 'hours'] },
      ],
    },
    features: {
      responsive: true,
      seoFriendly: true,
      hasEcommerce: false,
      hasBlog: false,
      hasContactForm: true,
      complexity: 'medium',
      multiLanguage: false,
      customizable: true,
      hasReservation: true,
      hasMenu: true,
    },
    isPremium: true,
    isActive: true,
    sortOrder: 5,
    metadata: {
      author: 'FlexiHub Team',
      version: '1.0.0',
      license: 'Premium',
      tags: ['餐厅', '美食', '暖色调', '预订'],
      estimatedBuildTime: '3-4小时',
    },
  },
  {
    name: '落地页模板',
    description: '高转化率的产品落地页模板，专注于用户转化和产品推广',
    category: 'landing-page',
    industry: 'marketing',
    thumbnail: '/templates/landing-marketing-1.jpg',
    preview: '/preview/landing-marketing-1',
    config: {
      layout: 'conversion',
      colorScheme: 'purple',
      header: {
        type: 'minimal',
        showLogo: true,
        showNavigation: false,
        showCTA: true,
      },
      footer: {
        showCopyright: true,
        showPrivacy: true,
        minimal: true,
      },
      pages: [
        {
          name: 'landing',
          title: '产品落地页',
          isDefault: true,
          components: [
            'hero-cta',
            'features-grid',
            'testimonials',
            'pricing-table',
            'faq',
            'final-cta',
          ],
        },
      ],
    },
    features: {
      responsive: true,
      seoFriendly: true,
      hasEcommerce: false,
      hasBlog: false,
      hasContactForm: true,
      complexity: 'low',
      multiLanguage: false,
      customizable: true,
      highConversion: true,
      abTesting: true,
    },
    isPremium: false,
    isActive: true,
    sortOrder: 6,
    metadata: {
      author: 'FlexiHub Team',
      version: '1.0.0',
      license: 'MIT',
      tags: ['落地页', '转化', '紫色主题', '营销'],
      estimatedBuildTime: '1小时',
    },
  },
];

module.exports = { websiteTemplateSeeds };
