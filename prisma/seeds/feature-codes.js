/**
 * 创建默认功能代码
 * @param {import('@prisma/client').PrismaClient} prisma Prisma客户端
 */
async function seedFeatureCodes(prisma) {
  console.log('开始创建功能代码...');

  // AI模块功能
  await createFeatureCode(prisma, {
    code: 'ai.ppt',
    name: 'AI PPT生成',
    description: 'AI PPT生成功能',
    module: 'AI模块',
    isActive: true,
    sortOrder: 10,
    metadata: {
      icon: 'file-ppt',
      color: '#FF4D4F',
      requiredPermissions: ['ai.use']
    }
  });

  await createFeatureCode(prisma, {
    code: 'ai.document',
    name: 'AI文档生成',
    description: 'AI文档生成功能',
    module: 'AI模块',
    isActive: true,
    sortOrder: 20,
    metadata: {
      icon: 'file-text',
      color: '#1890FF',
      requiredPermissions: ['ai.use']
    }
  });

  await createFeatureCode(prisma, {
    code: 'ai.chat',
    name: 'AI聊天',
    description: 'AI聊天功能',
    module: 'AI模块',
    isActive: true,
    sortOrder: 30,
    metadata: {
      icon: 'message',
      color: '#52C41A',
      requiredPermissions: ['ai.use']
    }
  });

  await createFeatureCode(prisma, {
    code: 'ai.image',
    name: 'AI图像生成',
    description: 'AI图像生成功能',
    module: 'AI模块',
    isActive: true,
    sortOrder: 40,
    metadata: {
      icon: 'file-image',
      color: '#722ED1',
      requiredPermissions: ['ai.use']
    }
  });

  // 支付模块功能
  await createFeatureCode(prisma, {
    code: 'payment',
    name: '基础支付',
    description: '基础支付功能',
    module: '支付模块',
    isActive: true,
    sortOrder: 100,
    metadata: {
      icon: 'credit-card',
      color: '#FA8C16',
      requiredPermissions: ['payment.use']
    }
  });

  await createFeatureCode(prisma, {
    code: 'payment.refund',
    name: '退款功能',
    description: '支付退款功能',
    module: '支付模块',
    isActive: true,
    sortOrder: 110,
    metadata: {
      icon: 'rollback',
      color: '#FA8C16',
      requiredPermissions: ['payment.refund']
    }
  });

  await createFeatureCode(prisma, {
    code: 'payment.subscription',
    name: '订阅支付',
    description: '订阅支付功能',
    module: '支付模块',
    isActive: true,
    sortOrder: 120,
    metadata: {
      icon: 'calendar',
      color: '#FA8C16',
      requiredPermissions: ['payment.subscription']
    }
  });

  // 会员模块功能
  await createFeatureCode(prisma, {
    code: 'membership',
    name: '基础会员',
    description: '基础会员功能',
    module: '会员模块',
    isActive: true,
    sortOrder: 200,
    metadata: {
      icon: 'user',
      color: '#13C2C2',
      requiredPermissions: ['membership.use']
    }
  });

  await createFeatureCode(prisma, {
    code: 'membership.upgrade',
    name: '会员升级',
    description: '会员升级功能',
    module: '会员模块',
    isActive: true,
    sortOrder: 210,
    metadata: {
      icon: 'arrow-up',
      color: '#13C2C2',
      requiredPermissions: ['membership.upgrade']
    }
  });

  await createFeatureCode(prisma, {
    code: 'membership.points',
    name: '会员积分',
    description: '会员积分功能',
    module: '会员模块',
    isActive: true,
    sortOrder: 220,
    metadata: {
      icon: 'star',
      color: '#13C2C2',
      requiredPermissions: ['membership.points']
    }
  });

  // 租户配置模块功能
  await createFeatureCode(prisma, {
    code: 'tenant-config.email',
    name: '邮件配置',
    description: '租户邮件配置',
    module: '租户配置模块',
    isActive: true,
    sortOrder: 300,
    metadata: {
      icon: 'mail',
      color: '#EB2F96',
      requiredPermissions: ['tenant-config.manage']
    }
  });

  await createFeatureCode(prisma, {
    code: 'tenant-config.sms',
    name: '短信配置',
    description: '租户短信配置',
    module: '租户配置模块',
    isActive: true,
    sortOrder: 310,
    metadata: {
      icon: 'message',
      color: '#EB2F96',
      requiredPermissions: ['tenant-config.manage']
    }
  });

  await createFeatureCode(prisma, {
    code: 'tenant-config.oss',
    name: '对象存储配置',
    description: '租户对象存储配置',
    module: '租户配置模块',
    isActive: true,
    sortOrder: 320,
    metadata: {
      icon: 'cloud',
      color: '#EB2F96',
      requiredPermissions: ['tenant-config.manage']
    }
  });

  await createFeatureCode(prisma, {
    code: 'tenant-config.payment',
    name: '支付配置',
    description: '租户支付配置',
    module: '租户配置模块',
    isActive: true,
    sortOrder: 330,
    metadata: {
      icon: 'wallet',
      color: '#EB2F96',
      requiredPermissions: ['tenant-config.manage']
    }
  });

  console.log('功能代码创建完成');
}

/**
 * 创建或更新功能代码
 * @param {import('@prisma/client').PrismaClient} prisma Prisma客户端
 * @param {Object} data 功能代码数据
 */
async function createFeatureCode(prisma, data) {
  const { code, name, description, module, isActive, sortOrder, metadata } = data;
  
  await prisma.featureCode.upsert({
    where: { code },
    update: {
      name,
      description,
      module,
      isActive,
      sortOrder,
      metadata
    },
    create: {
      code,
      name,
      description,
      module,
      isActive,
      sortOrder,
      metadata
    }
  });
  
  console.log(`功能代码 ${code} 创建/更新成功`);
}

module.exports = { seedFeatureCodes };
