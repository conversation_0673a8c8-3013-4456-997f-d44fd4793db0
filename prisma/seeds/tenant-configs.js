const crypto = require('crypto');

/**
 * 创建默认租户配置
 * @param {import('@prisma/client').PrismaClient} prisma Prisma客户端
 * @param {number} tenantId 租户ID
 */
async function seedTenantConfigs(prisma, tenantId) {
  console.log(`开始创建租户 ${tenantId} 的配置...`);

  // 加密密钥和初始化向量
  const encryptionKey = process.env.ENCRYPTION_KEY || 'a'.repeat(32); // 32字节密钥
  const encryptionIv = process.env.ENCRYPTION_IV || 'b'.repeat(16); // 16字节IV

  // 邮件配置
  await createConfigCategory(prisma, tenantId, 'email', [
    { key: 'provider', value: 'smtp', encrypted: false },
    { key: 'host', value: 'smtp.example.com', encrypted: false },
    { key: 'port', value: '587', encrypted: false },
    { key: 'username', value: '<EMAIL>', encrypted: false },
    { key: 'password', value: 'password123', encrypted: true, encryptionKey, encryptionIv },
    { key: 'fromEmail', value: '<EMAIL>', encrypted: false },
    { key: 'fromName', value: '系统通知', encrypted: false }
  ]);

  // 短信配置
  await createConfigCategory(prisma, tenantId, 'sms', [
    { key: 'provider', value: 'aliyun', encrypted: false },
    { key: 'accessKeyId', value: 'your_access_key_id', encrypted: true, encryptionKey, encryptionIv },
    { key: 'accessKeySecret', value: 'your_access_key_secret', encrypted: true, encryptionKey, encryptionIv },
    { key: 'signName', value: '公司名称', encrypted: false },
    { key: 'templateCode', value: 'SMS_12345678', encrypted: false }
  ]);

  // 对象存储配置
  await createConfigCategory(prisma, tenantId, 'oss', [
    { key: 'provider', value: 'aliyun', encrypted: false },
    { key: 'region', value: 'oss-cn-hangzhou', encrypted: false },
    { key: 'bucket', value: 'your-bucket-name', encrypted: false },
    { key: 'accessKeyId', value: 'your_access_key_id', encrypted: true, encryptionKey, encryptionIv },
    { key: 'accessKeySecret', value: 'your_access_key_secret', encrypted: true, encryptionKey, encryptionIv },
    { key: 'cdnDomain', value: 'cdn.example.com', encrypted: false }
  ]);

  // 支付配置
  await createConfigCategory(prisma, tenantId, 'payment', [
    { key: 'alipay.enabled', value: 'true', encrypted: false },
    { key: 'alipay.appId', value: '2021000000000000', encrypted: false },
    { key: 'alipay.privateKey', value: 'your_private_key', encrypted: true, encryptionKey, encryptionIv },
    { key: 'alipay.publicKey', value: 'your_public_key', encrypted: true, encryptionKey, encryptionIv },
    { key: 'wechat.enabled', value: 'true', encrypted: false },
    { key: 'wechat.appId', value: 'wx1234567890', encrypted: false },
    { key: 'wechat.mchId', value: '1234567890', encrypted: false },
    { key: 'wechat.apiKey', value: 'your_api_key', encrypted: true, encryptionKey, encryptionIv }
  ]);

  console.log(`租户 ${tenantId} 的配置创建完成`);
}

/**
 * 创建配置类别
 * @param {import('@prisma/client').PrismaClient} prisma Prisma客户端
 * @param {number} tenantId 租户ID
 * @param {string} category 配置类别
 * @param {Array} configs 配置项数组
 */
async function createConfigCategory(prisma, tenantId, category, configs) {
  console.log(`创建 ${category} 配置...`);

  // 先删除该类别的所有配置
  await prisma.tenantConfig.deleteMany({
    where: {
      tenantId,
      category
    }
  });

  // 创建新配置
  for (const config of configs) {
    const { key, value, encrypted, encryptionKey, encryptionIv } = config;
    
    // 如果需要加密，则加密值
    let storedValue = value;
    if (encrypted) {
      storedValue = encryptValue(value, encryptionKey, encryptionIv);
    }

    await prisma.tenantConfig.create({
      data: {
        tenantId,
        category,
        key,
        value: storedValue,
        encrypted
      }
    });
  }

  console.log(`${category} 配置创建完成`);
}

/**
 * 加密值
 * @param {string} value 要加密的值
 * @param {string} key 加密密钥
 * @param {string} iv 初始化向量
 * @returns {string} 加密后的值
 */
function encryptValue(value, key, iv) {
  if (!value) return '';
  
  try {
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
    let encrypted = cipher.update(value, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  } catch (error) {
    console.error(`加密失败: ${error.message}`);
    return value;
  }
}

module.exports = { seedTenantConfigs };
