generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/@prisma-public/prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("PUBLIC_DATABASE_URL")
}

// 平台基础设施 - 租户管理
model Tenant {
  id                 Int      @id @default(autoincrement())
  code               String   @unique
  name               String
  website            String?
  domain             String?
  status             Int      @default(1)
  createTime         DateTime @default(now()) @map("create_time")
  updateTime         DateTime @updatedAt @map("update_time")
  metadata           Json?    @default("{}")
  registrationSource String?  @default("manual") @map("registration_source")

  // 关联
  datasource         TenantDatasource?
  subscriptions      TenantSubscription[]
  features          TenantFeature[]
  configs           TenantConfig[]

  @@map("tenant")
}

model TenantDatasource {
  id          Int     @id @default(autoincrement())
  tenantId    Int     @unique @map("tenant_id")

  // 支持共享和独立两种模式
  isShared    Boolean @default(true) @map("is_shared")

  // 独立数据库配置
  host        String?
  port        Int?
  username    String?
  password    String? // 加密存储
  database    String?
  ssl         Boolean @default(true)

  createTime  DateTime @default(now()) @map("create_time")
  updateTime  DateTime @updatedAt @map("update_time")

  tenant      Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("tenant_datasource")
}

// 平台订阅系统
model SubscriptionPlan {
  id            Int      @id @default(autoincrement())
  code          String   @unique
  name          String
  description   String?
  price         Decimal  @default(0) @db.Decimal(10,2)
  currency      String   @default("CNY")
  billingCycle  String   @default("monthly") @map("billing_cycle")

  // 支持的部署模式
  deploymentMode String[] @default(["shared"]) @map("deployment_mode") // shared, dedicated

  features      Json     @default("[]")
  limits        Json     @default("{}")
  databases     Json?    @default("{}") // 数据库资源配置
  isActive      Boolean  @default(true) @map("is_active")
  sortOrder     Int      @default(0) @map("sort_order")
  metadata      Json?    @default("{}")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  subscriptions TenantSubscription[]

  @@map("subscription_plans")
}

model TenantSubscription {
  id           Int      @id @default(autoincrement())
  tenantId     Int      @map("tenant_id")
  planId       Int      @map("plan_id")
  duration     Int      @default(1)
  billingCycle String   @default("monthly") @map("billing_cycle")
  startDate    DateTime @map("start_date")
  endDate      DateTime @map("end_date")
  status       String   @default("active")
  autoRenew    Boolean  @default(false) @map("auto_renew")
  paymentInfo  Json?    @default("{}") @map("payment_info")
  metadata     Json     @default("{}")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  tenant       Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  plan         SubscriptionPlan @relation(fields: [planId], references: [id])

  @@map("tenant_subscriptions")
}

// 租户功能权限
model TenantFeature {
  id          Int      @id @default(autoincrement())
  tenantId    Int      @map("tenant_id")
  featureCode String   @map("feature_code")
  enabled     Boolean  @default(true)
  quota       Int?
  usedQuota   Int      @default(0) @map("used_quota")
  resetAt     DateTime? @map("reset_at")
  metadata    Json?    @default("{}")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, featureCode])
  @@map("tenant_features")
}

// 租户配置
model TenantConfig {
  id        Int      @id @default(autoincrement())
  tenantId  Int      @map("tenant_id")
  category  String
  key       String
  value     String
  dataType  String   @default("string") @map("data_type")
  encrypted Boolean  @default(false)
  description String?
  metadata  Json?    @default("{}")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, category, key])
  @@map("tenant_config")
}

// 系统配置
model SystemConfig {
  id          Int      @id @default(autoincrement())
  category    String
  key         String
  value       String
  dataType    String   @default("string") @map("data_type")
  description String?
  isPublic    Boolean  @default(false) @map("is_public")
  metadata    Json?    @default("{}")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@unique([category, key])
  @@map("system_config")
}

// 平台级审计日志
model AuditLog {
  id         Int      @id @default(autoincrement())
  tenantId   Int?     @map("tenant_id")
  userId     Int?     @map("user_id")
  action     String
  resource   String
  resourceId String?  @map("resource_id")
  ipAddress  String?  @map("ip_address")
  userAgent  String?  @map("user_agent")
  details    Json?    @default("{}")
  createdAt  DateTime @default(now()) @map("created_at")

  @@index([tenantId])
  @@index([userId])
  @@index([action])
  @@index([createdAt])
  @@map("audit_logs")
}