// FlexiHub Tenant Schema - 业务数据层
// 支持共享数据库和独立数据库两种模式

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ================================
// 用户管理（支持共享和独立两种模式）
// ================================

model User {
  id          Int      @id @default(autoincrement())
  tenantId    Int?     @map("tenant_id") // 共享模式需要，独立模式可选
  username    String
  password    String
  email       String?
  realName    String?  @map("real_name")
  phoneNumber String?  @map("phone_number")
  avatar      String?
  status      Int      @default(1)
  userType    String   @default("TENANT") @map("user_type") // SYSTEM, TENANT
  metadata    Json?    @default("{}")
  createTime  DateTime @default(now()) @map("create_time")
  updateTime  DateTime @updatedAt @map("update_time")

  roles       UserRole[]

  // 共享模式的约束
  @@unique([tenantId, username], name: "user_tenant_username_key")
  @@unique([tenantId, email], name: "user_tenant_email_key")

  // 独立模式的约束（当tenantId为null时）
  @@unique([username], name: "user_username_key")
  @@unique([email], name: "user_email_key")

  @@map("user")
}

model Role {
  id          Int      @id @default(autoincrement())
  tenantId    Int?     @map("tenant_id")
  code        String
  name        String
  description String?
  permissions Json     @default("[]")
  isSystem    Boolean  @default(false) @map("is_system")
  status      Int      @default(1)
  createTime  DateTime @default(now()) @map("create_time")
  updateTime  DateTime @updatedAt @map("update_time")

  userRoles   UserRole[]

  @@unique([tenantId, code])
  @@map("role")
}

model UserRole {
  id         Int      @id @default(autoincrement())
  userId     Int      @map("user_id")
  roleId     Int      @map("role_id")
  createTime DateTime @default(now()) @map("create_time")

  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  role       Role     @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_role")
}

// ================================
// 网站管理
// ================================

model Website {
  id           Int      @id @default(autoincrement())
  tenantId     Int?     @map("tenant_id") // 共享模式需要，独立模式可选
  name         String
  domain       String?
  websiteType  String   @default("tenant") @map("website_type")
  status       String   @default("draft")
  config       Json     @default("{}")
  content      Json     @default("{}")
  seoConfig    Json?    @default("{}") @map("seo_config")
  analytics    Json?    @default("{}")
  metadata     Json?    @default("{}")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  publishedAt  DateTime? @map("published_at")

  pages        WebsitePage[]

  @@map("websites")
}

model WebsitePage {
  id         Int      @id @default(autoincrement())
  websiteId  Int      @map("website_id")
  path       String
  title      String
  content    Json     @default("{}")
  seoMeta    Json?    @default("{}") @map("seo_meta")
  status     String   @default("draft")
  metadata   Json?    @default("{}")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  website    Website  @relation(fields: [websiteId], references: [id], onDelete: Cascade)

  @@unique([websiteId, path])
  @@map("website_pages")
}

// ================================
// 通知系统
// ================================

model Notification {
  id        Int      @id @default(autoincrement())
  tenantId  Int?     @map("tenant_id")
  userId    Int?     @map("user_id")
  type      String
  title     String
  content   String
  isRead    Boolean  @default(false) @map("is_read")
  metadata  Json?    @default("{}")
  createdAt DateTime @default(now()) @map("created_at")
  readAt    DateTime? @map("read_at")

  @@index([tenantId, userId])
  @@index([createdAt])
  @@map("notifications")
}

// ================================
// 使用统计
// ================================

model UsageStats {
  id         Int      @id @default(autoincrement())
  tenantId   Int?     @map("tenant_id") // 独立模式下可能不需要
  date       DateTime @db.Date
  category   String
  value      Int      @default(0)
  unit       String   @default("count")
  metadata   Json?    @default("{}")
  createdAt  DateTime @default(now()) @map("created_at")

  @@unique([tenantId, date, category])
  @@index([tenantId, date])
  @@map("usage_stats")
}

// ================================
// 支付记录
// ================================

model Payment {
  id               Int      @id @default(autoincrement())
  tenantId         Int?     @map("tenant_id")
  subscriptionId   Int?     @map("subscription_id")
  amount           Decimal  @db.Decimal(10,2)
  currency         String   @default("CNY")
  paymentMethod    String   @map("payment_method")
  paymentProvider  String   @map("payment_provider")
  transactionId    String?  @map("transaction_id")
  status           String   @default("pending")
  paymentData      Json?    @default("{}") @map("payment_data")
  metadata         Json?    @default("{}")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")
  paidAt          DateTime? @map("paid_at")

  @@index([tenantId])
  @@index([status])
  @@map("payments")
}
