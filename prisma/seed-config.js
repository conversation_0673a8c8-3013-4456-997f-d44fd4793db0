/**
 * 种子数据配置
 * 用于存储种子数据中的配置信息，避免硬编码
 */
module.exports = {
  // 默认密码在seed.js中动态生成

  // 租户配置
  tenant: {
    code: 'tenant1',
    name: '测试租户',
    website: 'https://example.com',
    status: 1,
  },

  // 系统管理员配置
  systemAdmin: {
    username: 'admin',
    realName: '系统管理员',
    email: '<EMAIL>',
    status: 1,
  },

  // 租户管理员配置
  tenantAdmin: {
    username: 'tenant_admin',
    realName: '租户管理员',
    email: '<EMAIL>',
    status: 1,
  },

  // 系统角色配置
  systemRoles: [
    {
      name: '超级管理员',
      code: 'SUPER_ADMIN',
      remark: '系统超级管理员，拥有所有权限',
      status: 1,
    },
    {
      name: '测试管理员',
      code: 'TEST_ADMIN', // 修改为不同的角色编码
      remark: '测试管理员，拥有部分权限',
      status: 1,
    }
  ],

  // 租户角色配置
  tenantRoles: [
    {
      name: '租户管理员',
      code: 'TENANT_ADMIN',
      remark: '租户管理员，拥有租户内所有权限',
      status: 1,
    },
    {
      name: '租户测试角色',
      code: 'TENANT_USER', // 修改为不同的角色编码
      remark: '租户普通用户，拥有部分权限',
      status: 1,
    }
  ],

  // 系统权限配置
  permissions: [
    // 用户管理权限
    { name: '用户查询', code: 'system:user:list', description: '查询用户列表' },
    { name: '用户创建', code: 'system:user:create', description: '创建用户' },
    { name: '用户编辑', code: 'system:user:update', description: '编辑用户' },
    { name: '用户删除', code: 'system:user:delete', description: '删除用户' },

    // 角色管理权限
    { name: '角色查询', code: 'system:role:list', description: '查询角色列表' },
    { name: '角色创建', code: 'system:role:create', description: '创建角色' },
    { name: '角色编辑', code: 'system:role:update', description: '编辑角色' },
    { name: '角色删除', code: 'system:role:delete', description: '删除角色' },

    // 菜单管理权限
    { name: '菜单查询', code: 'system:menu:list', description: '查询菜单列表' },
    { name: '菜单创建', code: 'system:menu:create', description: '创建菜单' },
    { name: '菜单编辑', code: 'system:menu:update', description: '编辑菜单' },
    { name: '菜单删除', code: 'system:menu:delete', description: '删除菜单' },

    // 部门管理权限
    { name: '部门查询', code: 'system:dept:list', description: '查询部门列表' },
    { name: '部门创建', code: 'system:dept:create', description: '创建部门' },
    { name: '部门编辑', code: 'system:dept:update', description: '编辑部门' },
    { name: '部门删除', code: 'system:dept:delete', description: '删除部门' },

    // 租户管理权限
    { name: '租户查询', code: 'system:tenant:list', description: '查询租户列表' },
    { name: '租户创建', code: 'system:tenant:create', description: '创建租户' },
    { name: '租户编辑', code: 'system:tenant:update', description: '编辑租户' },
    { name: '租户删除', code: 'system:tenant:delete', description: '删除租户' },

    // 功能模板管理权限
    { name: '功能模板查询', code: 'system:feature-template:list', description: '查询功能模板列表' },
    { name: '功能模板创建', code: 'system:feature-template:create', description: '创建功能模板' },
    { name: '功能模板编辑', code: 'system:feature-template:update', description: '编辑功能模板' },
    { name: '功能模板删除', code: 'system:feature-template:delete', description: '删除功能模板' },

    // 功能代码管理权限
    { name: '功能代码查询', code: 'system:feature-code:list', description: '查询功能代码列表' },
    { name: '功能代码创建', code: 'system:feature-code:create', description: '创建功能代码' },
    { name: '功能代码编辑', code: 'system:feature-code:update', description: '编辑功能代码' },
    { name: '功能代码删除', code: 'system:feature-code:delete', description: '删除功能代码' },

    // 租户功能权限
    { name: '功能看板查询', code: 'tenant:features:list', description: '查询功能看板' },
    { name: '功能看板管理', code: 'tenant:features:manage', description: '管理功能看板' },

    // 租户配置权限
    { name: '配置中心查询', code: 'tenant:configs:list', description: '查询配置中心' },
    { name: '配置中心管理', code: 'tenant:configs:manage', description: '管理配置中心' },

    // 租户用户管理权限
    { name: '租户用户查询', code: 'tenant:user:list', description: '查询租户用户列表' },
    { name: '租户用户创建', code: 'tenant:user:create', description: '创建租户用户' },
    { name: '租户用户编辑', code: 'tenant:user:update', description: '编辑租户用户' },
    { name: '租户用户删除', code: 'tenant:user:delete', description: '删除租户用户' },

    // 租户角色管理权限
    { name: '租户角色查询', code: 'tenant:role:list', description: '查询租户角色列表' },
    { name: '租户角色创建', code: 'tenant:role:create', description: '创建租户角色' },
    { name: '租户角色编辑', code: 'tenant:role:update', description: '编辑租户角色' },
    { name: '租户角色删除', code: 'tenant:role:delete', description: '删除租户角色' },
  ],

  // 系统菜单配置
  systemMenus: {
    // 仪表盘
    dashboard: {
      name: 'Dashboard',
      path: '/dashboard',
      component: 'BasicLayout',
      type: 'menu',
      icon: 'lucide:layout-dashboard',
      orderNo: -1,
      status: 1,
      meta: {
        title: '仪表盘',
        icon: 'lucide:layout-dashboard',
        order: -1,
      },
    },
    // 仪表盘子菜单 - 分析页
    analytics: {
      name: 'Analytics',
      path: '/dashboard/analytics',
      component: '/dashboard/analytics/index',
      type: 'menu',
      icon: 'lucide:area-chart',
      orderNo: 1,
      status: 1,
      meta: {
        title: '分析页',
        icon: 'lucide:area-chart',
        affixTab: true,
      },
    },
    // 仪表盘子菜单 - 工作台
    workspace: {
      name: 'Workspace',
      path: '/dashboard/workspace',
      component: '/dashboard/workspace/index',
      type: 'menu',
      icon: 'carbon:workspace',
      orderNo: 2,
      status: 1,
      meta: {
        title: '工作台',
        icon: 'carbon:workspace',
      },
    },
    // 系统管理
    system: {
      name: 'System',
      path: '/system',
      component: 'BasicLayout',
      type: 'menu',
      icon: 'ion:settings-outline',
      orderNo: 9997,
      status: 1,
      meta: {
        title: '系统管理',
        icon: 'ion:settings-outline',
        order: 9997,
      },
    },
    // 系统管理子菜单 - 角色管理
    role: {
      name: 'SystemRole',
      path: '/system/role',
      component: '/system/role/list',
      type: 'menu',
      icon: 'mdi:account-group',
      permission: 'system:role:list',
      orderNo: 1,
      status: 1,
      meta: {
        title: '角色管理',
        icon: 'mdi:account-group',
      },
    },
    // 系统管理子菜单 - 菜单管理
    menu: {
      name: 'SystemMenu',
      path: '/system/menu',
      component: '/system/menu/list',
      type: 'menu',
      icon: 'mdi:menu',
      permission: 'system:menu:list',
      orderNo: 2,
      status: 1,
      meta: {
        title: '菜单管理',
        icon: 'mdi:menu',
      },
    },
    // 系统管理子菜单 - 部门管理
    dept: {
      name: 'SystemDept',
      path: '/system/dept',
      component: '/system/dept/list',
      type: 'menu',
      icon: 'charm:organisation',
      permission: 'system:dept:list',
      orderNo: 3,
      status: 1,
      meta: {
        title: '部门管理',
        icon: 'charm:organisation',
      },
    },
    // 系统管理子菜单 - 租户管理
    tenant: {
      name: 'SystemTenant',
      path: '/system/tenant',
      component: '/system/tenant/index',
      redirect: '/system/tenant/list',
      type: 'menu',
      icon: 'mdi:office-building',
      permission: 'system:tenant:list',
      orderNo: 4,
      status: 1,
      meta: {
        title: '租户管理',
        icon: 'mdi:office-building',
      },
    },
    // 系统管理子菜单 - 租户列表
    tenantList: {
      name: 'SystemTenantList',
      path: '/system/tenant/list',
      component: '/system/tenant/list',
      type: 'menu',
      permission: 'system:tenant:list',
      orderNo: 4.1,
      status: 1,
      meta: {
        title: '租户管理',
        hideInMenu: true,
      },
    },
    // 系统管理子菜单 - 租户功能管理
    tenantFeature: {
      name: 'SystemTenantFeature',
      path: '/system/tenant/features',
      component: '/system/tenant-feature/list',
      type: 'menu',
      icon: 'mdi:feature-search',
      permission: 'system:tenant:feature:list',
      orderNo: 4.2,
      status: 1,
      meta: {
        title: '功能管理',
        icon: 'mdi:feature-search',
        hideInMenu: true,
      },
    },
    // 系统管理子菜单 - 租户配置管理
    tenantConfig: {
      name: 'SystemTenantConfig',
      path: '/system/tenant/configs',
      component: '/system/tenant-config/list',
      type: 'menu',
      icon: 'mdi:cog',
      permission: 'system:tenant:config:list',
      orderNo: 4.3,
      status: 1,
      meta: {
        title: '配置管理',
        icon: 'mdi:cog',
        hideInMenu: true,
      },
    },
    // 系统管理子菜单 - 用户管理
    user: {
      name: 'SystemUser',
      path: '/system/user',
      component: '/system/user/list',
      type: 'menu',
      icon: 'mdi:account-multiple',
      permission: 'system:user:list',
      orderNo: 5,
      status: 1,
      meta: {
        title: '用户管理',
        icon: 'mdi:account-multiple',
      },
    },
    // 系统管理子菜单 - 功能模板管理
    featureTemplate: {
      name: 'SystemFeatureTemplate',
      path: '/system/feature-templates',
      component: '/system/feature-template/list',
      type: 'menu',
      icon: 'mdi:puzzle',
      permission: 'system:feature-template:list',
      orderNo: 6,
      status: 1,
      meta: {
        title: '功能模板管理',
        icon: 'mdi:puzzle',
      },
    },
    // 系统管理子菜单 - 功能代码管理
    featureCode: {
      name: 'SystemFeatureCode',
      path: '/system/feature-codes',
      component: '/system/feature-code/list',
      type: 'menu',
      icon: 'mdi:code-tags',
      permission: 'system:feature-code:list',
      orderNo: 7,
      status: 1,
      meta: {
        title: '功能代码管理',
        icon: 'mdi:code-tags',
      },
    },
    // 租户管理
    tenantManagement: {
      name: 'TenantManagement',
      path: '/tenant',
      component: 'BasicLayout',
      type: 'menu',
      icon: 'mdi:office-building-cog',
      orderNo: 9996,
      status: 1,
      meta: {
        title: '功能管理',
        icon: 'mdi:office-building-cog',
        order: 9996,
      },
    },
    // 租户管理子菜单 - 功能看板
    tenantFeatures: {
      name: 'TenantFeatures',
      path: '/tenant/features',
      component: '/tenant/features/dashboard',
      type: 'menu',
      icon: 'mdi:feature-search',
      permission: 'tenant:features:list',
      orderNo: 1,
      status: 1,
      meta: {
        title: '功能看板',
        icon: 'mdi:feature-search',
      },
    },
    // 租户管理子菜单 - 配置中心
    tenantConfigs: {
      name: 'TenantConfigs',
      path: '/tenant/configs',
      component: '/tenant/configs/index',
      type: 'menu',
      icon: 'mdi:cog-outline',
      permission: 'tenant:configs:list',
      orderNo: 2,
      status: 1,
      meta: {
        title: '配置中心',
        icon: 'mdi:cog-outline',
      },
    },
  },

  // 租户菜单标题映射 - 用于修改显示标题
  tenantMenuTitles: {
    '/tenant': '功能中心',
    '/system': '用户中心',
  },
};
