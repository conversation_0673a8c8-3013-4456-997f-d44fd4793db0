// FlexiHub 增强版 Prisma Schema
// 支持订阅计划、租户功能权限、配置管理等企业级功能

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ================================
// 核心租户模型 (保持向后兼容)
// ================================

model Tenant {
  id                 Int      @id @default(autoincrement())
  code               String   @unique
  name               String
  website            String?
  domain             String?
  status             Int      @default(1) // 1:启用 0:禁用
  createTime         DateTime @default(now()) @map("create_time")
  updateTime         DateTime @updatedAt @map("update_time")

  // 新增字段 (向后兼容)
  metadata           Json?    @default("{}")
  registrationSource String?  @default("manual") @map("registration_source") // manual, self-service

  // 数据源配置 (保留现有结构)
  datasource         TenantDatasource?

  // 新增关联
  subscriptions      TenantSubscription[]
  features          TenantFeature[]
  configs           TenantConfig[]
  users             User[]
  websites          Website[]

  @@map("tenant")
}

model TenantDatasource {
  id                Int     @id @default(autoincrement())
  tenantId          Int     @unique @map("tenant_id")
  host              String
  port              Int
  username          String
  password          String
  database          String
  ssl               Boolean @default(false)
  createTime        DateTime @default(now()) @map("create_time")
  updateTime        DateTime @updatedAt @map("update_time")

  tenant            Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("tenant_datasource")
}

// ================================
// 订阅计划管理
// ================================

model SubscriptionPlan {
  id            Int      @id @default(autoincrement())
  code          String   @unique
  name          String
  description   String?
  price         Decimal  @default(0) @db.Decimal(10,2)
  currency      String   @default("CNY")
  billingCycle  String   @default("monthly") @map("billing_cycle") // monthly, yearly, lifetime
  features      Json     @default("[]")  // 功能列表 JSON数组
  limits        Json     @default("{}")  // 限制配置 JSON对象
  isActive      Boolean  @default(true) @map("is_active")
  sortOrder     Int      @default(0) @map("sort_order")
  metadata      Json?    @default("{}")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  createdBy     Int?     @map("created_by")

  // 关联
  subscriptions TenantSubscription[]

  @@map("subscription_plans")
}

model TenantSubscription {
  id           Int      @id @default(autoincrement())
  tenantId     Int      @map("tenant_id")
  planId       Int      @map("plan_id")
  duration     Int      @default(1)           // 订阅时长
  billingCycle String   @default("monthly") @map("billing_cycle")
  startDate    DateTime @map("start_date")
  endDate      DateTime @map("end_date")
  status       String   @default("active")   // active, expired, cancelled, pending
  autoRenew    Boolean  @default(false) @map("auto_renew")
  paymentInfo  Json?    @default("{}") @map("payment_info")
  metadata     Json     @default("{}")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // 关联
  tenant       Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  plan         SubscriptionPlan @relation(fields: [planId], references: [id])

  @@map("tenant_subscriptions")
}

// ================================
// 租户功能权限管理
// ================================

model TenantFeature {
  id          Int      @id @default(autoincrement())
  tenantId    Int      @map("tenant_id")
  featureCode String   @map("feature_code")
  enabled     Boolean  @default(true)
  quota       Int?                        // 配额限制，null表示无限制
  usedQuota   Int      @default(0) @map("used_quota")
  resetAt     DateTime? @map("reset_at")  // 配额重置时间
  metadata    Json?    @default("{}")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, featureCode])
  @@map("tenant_features")
}

// ================================
// 租户配置管理
// ================================

model TenantConfig {
  id        Int      @id @default(autoincrement())
  tenantId  Int      @map("tenant_id")
  category  String                     // 配置分类: website, payment, email, etc.
  key       String
  value     String
  dataType  String   @default("string") @map("data_type") // string, number, boolean, json
  encrypted Boolean  @default(false)
  description String?
  metadata  Json?    @default("{}")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, category, key])
  @@map("tenant_config")
}

// ================================
// 用户管理 (保持现有结构)
// ================================

model User {
  id          Int      @id @default(autoincrement())
  tenantId    Int      @map("tenant_id")
  username    String
  password    String
  email       String?
  realName    String?  @map("real_name")
  phoneNumber String?  @map("phone_number")
  avatar      String?
  status      Int      @default(1)      // 1:启用 0:禁用
  userType    String   @default("TENANT") @map("user_type") // SYSTEM, TENANT
  metadata    Json?    @default("{}")
  createTime  DateTime @default(now()) @map("create_time")
  updateTime  DateTime @updatedAt @map("update_time")

  // 关联
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  roles       UserRole[]

  @@unique([tenantId, username])
  @@unique([tenantId, email])
  @@map("user")
}

model Role {
  id          Int      @id @default(autoincrement())
  tenantId    Int?     @map("tenant_id") // null表示系统级角色
  code        String
  name        String
  description String?
  permissions Json     @default("[]")   // 权限列表
  isSystem    Boolean  @default(false) @map("is_system")
  status      Int      @default(1)
  createTime  DateTime @default(now()) @map("create_time")
  updateTime  DateTime @updatedAt @map("update_time")

  // 关联
  userRoles   UserRole[]

  @@unique([tenantId, code])
  @@map("role")
}

model UserRole {
  id         Int      @id @default(autoincrement())
  userId     Int      @map("user_id")
  roleId     Int      @map("role_id")
  createTime DateTime @default(now()) @map("create_time")

  // 关联
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  role       Role     @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_role")
}

// ================================
// 网站管理 (支持官网建站化)
// ================================

model Website {
  id           Int      @id @default(autoincrement())
  tenantId     Int      @map("tenant_id")
  name         String
  domain       String?
  websiteType  String   @default("tenant") @map("website_type") // tenant, system, registration, landing
  status       String   @default("draft")  // draft, published, maintenance
  config       Json     @default("{}")
  content      Json     @default("{}")
  seoConfig    Json?    @default("{}") @map("seo_config")
  analytics    Json?    @default("{}")
  metadata     Json?    @default("{}")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  publishedAt  DateTime? @map("published_at")

  // 关联
  tenant       Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  pages        WebsitePage[]

  @@map("websites")
}

model WebsitePage {
  id         Int      @id @default(autoincrement())
  websiteId  Int      @map("website_id")
  path       String
  title      String
  content    Json     @default("{}")
  seoMeta    Json?    @default("{}") @map("seo_meta")
  status     String   @default("draft")
  metadata   Json?    @default("{}")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // 关联
  website    Website  @relation(fields: [websiteId], references: [id], onDelete: Cascade)

  @@unique([websiteId, path])
  @@map("website_pages")
}

// ================================
// 系统日志和审计
// ================================

model AuditLog {
  id         Int      @id @default(autoincrement())
  tenantId   Int?     @map("tenant_id")  // null表示系统级操作
  userId     Int?     @map("user_id")
  action     String                      // 操作类型
  resource   String                      // 资源类型
  resourceId String?  @map("resource_id") // 资源ID
  ipAddress  String?  @map("ip_address")
  userAgent  String?  @map("user_agent")
  details    Json?    @default("{}")
  createdAt  DateTime @default(now()) @map("created_at")

  @@index([tenantId])
  @@index([userId])
  @@index([action])
  @@index([createdAt])
  @@map("audit_logs")
}

// ================================
// 系统配置和通知
// ================================

model SystemConfig {
  id          Int      @id @default(autoincrement())
  category    String
  key         String
  value       String
  dataType    String   @default("string") @map("data_type")
  description String?
  isPublic    Boolean  @default(false) @map("is_public")  // 是否对租户可见
  metadata    Json?    @default("{}")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@unique([category, key])
  @@map("system_config")
}

model Notification {
  id        Int      @id @default(autoincrement())
  tenantId  Int?     @map("tenant_id")  // null表示系统通知
  userId    Int?     @map("user_id")    // null表示租户广播
  type      String                      // info, warning, error, success
  title     String
  content   String
  isRead    Boolean  @default(false) @map("is_read")
  metadata  Json?    @default("{}")
  createdAt DateTime @default(now()) @map("created_at")
  readAt    DateTime? @map("read_at")

  @@index([tenantId, userId])
  @@index([createdAt])
  @@map("notifications")
}

// ================================
// 使用统计和分析
// ================================

model UsageStats {
  id         Int      @id @default(autoincrement())
  tenantId   Int      @map("tenant_id")
  date       DateTime @db.Date
  category   String                     // api_calls, storage, bandwidth, users, websites
  value      Int      @default(0)
  unit       String   @default("count") // count, bytes, seconds
  metadata   Json?    @default("{}")
  createdAt  DateTime @default(now()) @map("created_at")

  @@unique([tenantId, date, category])
  @@index([tenantId, date])
  @@map("usage_stats")
}

// ================================
// 支付和账单 (预留)
// ================================

model Payment {
  id               Int      @id @default(autoincrement())
  tenantId         Int      @map("tenant_id")
  subscriptionId   Int?     @map("subscription_id")
  amount           Decimal  @db.Decimal(10,2)
  currency         String   @default("CNY")
  paymentMethod    String   @map("payment_method")
  paymentProvider  String   @map("payment_provider")
  transactionId    String?  @map("transaction_id")
  status           String   @default("pending") // pending, completed, failed, refunded
  paymentData      Json?    @default("{}") @map("payment_data")
  metadata         Json?    @default("{}")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")
  paidAt          DateTime? @map("paid_at")

  @@index([tenantId])
  @@index([status])
  @@map("payments")
}
