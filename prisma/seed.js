// 导入 Prisma 客户端
const { PrismaClient: PublicPrismaClient } = require('../node_modules/@prisma-public/prisma/client');
const { PrismaClient: TenantPrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const seedConfig = require('./seed-config');
const { seedFeatureTemplates } = require('./seeds/feature-templates');
const { seedFeatureCodes } = require('./seeds/feature-codes');
const { seedTenantConfigs } = require('./seeds/tenant-configs');
const { seedTenantFeatures, seedFeatureUsage } = require('./seeds/tenant-features');

// 创建 Prisma 客户端实例
const publicPrisma = new PublicPrismaClient();
const tenantPrisma = new TenantPrismaClient();

/**
 * 初始化数据库
 * 添加必要的初始数据
 */
async function main() {
  try {
    console.log('开始添加初始数据...');

    // 1. 创建数据源（先检查是否存在）
    console.log('检查并创建数据源...');

    // 检查数据源是否存在
    let datasource = await publicPrisma.datasource.findFirst({
      where: { name: '默认数据源' },
    });

    // 如果不存在则创建
    if (!datasource) {
      datasource = await publicPrisma.datasource.create({
        data: {
          name: '默认数据源',
          url: process.env.DATABASE_URL || '',
          metadata: {},
        },
      });
      console.log(`数据源创建成功: ${datasource.id}`);
    } else {
      console.log(`使用已存在的数据源: ${datasource.id}`);
    }

    // 2. 创建租户（先检查是否存在，如果存在则删除）
    console.log('检查并创建租户...');

    // 检查租户是否存在
    const existingTenant = await publicPrisma.tenant.findFirst({
      where: { code: seedConfig.tenant.code },
    });

    // 如果存在则删除
    if (existingTenant) {
      console.log(`发现已存在的租户: ${existingTenant.id}，准备删除...`);
      await publicPrisma.tenant.delete({
        where: { id: existingTenant.id },
      });
      console.log('已删除现有租户');
    }

    // 创建新租户
    const tenant = await publicPrisma.tenant.create({
      data: {
        code: seedConfig.tenant.code,
        name: seedConfig.tenant.name,
        website: seedConfig.tenant.website,
        status: seedConfig.tenant.status,
        datasourceId: datasource.id,
      },
    });
    console.log(`租户创建成功: ${tenant.id}`);

    // 3. 创建系统管理员（先检查是否存在）
    console.log('检查并创建系统管理员...');
    // 动态生成密码哈希（使用bcrypt）
    const defaultPassword = 'admin123'; // 默认密码
    const saltRounds = 10; // 盐轮数
    const adminPasswordHash = await bcrypt.hash(defaultPassword, saltRounds);
    console.log(`已为默认密码 "${defaultPassword}" 生成bcrypt哈希`);

    // 检查系统管理员是否存在
    let systemAdmin = await publicPrisma.systemUser.findFirst({
      where: { username: seedConfig.systemAdmin.username },
    });

    // 如果不存在则创建
    if (!systemAdmin) {
      systemAdmin = await publicPrisma.systemUser.create({
        data: {
          username: seedConfig.systemAdmin.username,
          password: adminPasswordHash,
          realName: seedConfig.systemAdmin.realName,
          email: seedConfig.systemAdmin.email,
          status: seedConfig.systemAdmin.status,
        },
      });
      console.log(`系统管理员创建成功: ${systemAdmin.id}`);
    } else {
      console.log(`使用已存在的系统管理员: ${systemAdmin.id}`);
    }

    // 4. 创建系统角色（先删除现有角色）
    console.log('删除现有系统角色...');
    await publicPrisma.systemRolePermission.deleteMany({});
    await publicPrisma.systemRoleMenu.deleteMany({});
    await publicPrisma.systemUserRole.deleteMany({});
    await publicPrisma.systemRole.deleteMany({});

    console.log('创建系统角色...');
    const systemRole = await publicPrisma.systemRole.create({
      data: seedConfig.systemRoles[0],
    });
    console.log(`系统角色创建成功: ${systemRole.id}`);

    // 创建另一个系统角色
    const systemRole2 = await publicPrisma.systemRole.create({
      data: seedConfig.systemRoles[1],
    });
    console.log(`测试系统角色创建成功: ${systemRole2.id}`);

    // 5. 分配角色给系统管理员
    console.log('分配角色给系统管理员...');
    await publicPrisma.systemUserRole.create({
      data: {
        userId: systemAdmin.id,
        roleId: systemRole.id,
      },
    });

    // 同时分配测试角色给系统管理员
    await publicPrisma.systemUserRole.create({
      data: {
        userId: systemAdmin.id,
        roleId: systemRole2.id,
      },
    });
    console.log('角色分配成功');

    // 6. 创建系统菜单（先删除现有菜单）
    console.log('删除现有系统菜单...');
    await publicPrisma.systemMenu.deleteMany({});

    console.log('创建系统菜单...');

    // 创建菜单并存储菜单ID
    const menuIds = {};

    // 创建仪表盘菜单
    const dashboardData = {
      ...seedConfig.systemMenus.dashboard,
      meta: JSON.stringify(seedConfig.systemMenus.dashboard.meta),
    };
    menuIds.dashboard = await publicPrisma.systemMenu.create({
      data: dashboardData,
    });
    console.log(`系统菜单创建成功: ${menuIds.dashboard.id} (${seedConfig.systemMenus.dashboard.name})`);

    // 创建仪表盘子菜单 - 分析页
    const analyticsData = {
      ...seedConfig.systemMenus.analytics,
      pid: menuIds.dashboard.id,
      meta: JSON.stringify(seedConfig.systemMenus.analytics.meta),
    };
    menuIds.analytics = await publicPrisma.systemMenu.create({
      data: analyticsData,
    });
    console.log(`系统菜单创建成功: ${menuIds.analytics.id} (${seedConfig.systemMenus.analytics.name})`);

    // 创建仪表盘子菜单 - 工作台
    const workspaceData = {
      ...seedConfig.systemMenus.workspace,
      pid: menuIds.dashboard.id,
      meta: JSON.stringify(seedConfig.systemMenus.workspace.meta),
    };
    menuIds.workspace = await publicPrisma.systemMenu.create({
      data: workspaceData,
    });
    console.log(`系统菜单创建成功: ${menuIds.workspace.id} (${seedConfig.systemMenus.workspace.name})`);

    // 创建系统管理菜单
    const systemData = {
      ...seedConfig.systemMenus.system,
      meta: JSON.stringify(seedConfig.systemMenus.system.meta),
    };
    menuIds.system = await publicPrisma.systemMenu.create({
      data: systemData,
    });
    console.log(`系统菜单创建成功: ${menuIds.system.id} (${seedConfig.systemMenus.system.name})`);

    // 创建系统管理子菜单 - 角色管理
    const roleData = {
      ...seedConfig.systemMenus.role,
      pid: menuIds.system.id,
      meta: JSON.stringify(seedConfig.systemMenus.role.meta),
    };
    menuIds.role = await publicPrisma.systemMenu.create({
      data: roleData,
    });
    console.log(`系统菜单创建成功: ${menuIds.role.id} (${seedConfig.systemMenus.role.name})`);

    // 创建系统管理子菜单 - 菜单管理
    const menuData = {
      ...seedConfig.systemMenus.menu,
      pid: menuIds.system.id,
      meta: JSON.stringify(seedConfig.systemMenus.menu.meta),
    };
    menuIds.menu = await publicPrisma.systemMenu.create({
      data: menuData,
    });
    console.log(`系统菜单创建成功: ${menuIds.menu.id} (${seedConfig.systemMenus.menu.name})`);

    // 创建系统管理子菜单 - 部门管理
    const deptData = {
      ...seedConfig.systemMenus.dept,
      pid: menuIds.system.id,
      meta: JSON.stringify(seedConfig.systemMenus.dept.meta),
    };
    menuIds.dept = await publicPrisma.systemMenu.create({
      data: deptData,
    });
    console.log(`系统菜单创建成功: ${menuIds.dept.id} (${seedConfig.systemMenus.dept.name})`);

    // 创建系统管理子菜单 - 租户管理
    const tenantData = {
      ...seedConfig.systemMenus.tenant,
      pid: menuIds.system.id,
      meta: JSON.stringify(seedConfig.systemMenus.tenant.meta),
    };
    menuIds.tenant = await publicPrisma.systemMenu.create({
      data: tenantData,
    });
    console.log(`系统菜单创建成功: ${menuIds.tenant.id} (${seedConfig.systemMenus.tenant.name})`);

    // 创建系统管理子菜单 - 租户列表
    const tenantListData = {
      ...seedConfig.systemMenus.tenantList,
      pid: menuIds.tenant.id,
      meta: JSON.stringify(seedConfig.systemMenus.tenantList.meta),
    };
    menuIds.tenantList = await publicPrisma.systemMenu.create({
      data: tenantListData,
    });
    console.log(`系统菜单创建成功: ${menuIds.tenantList.id} (${seedConfig.systemMenus.tenantList.name})`);

    // 创建系统管理子菜单 - 租户功能管理
    const tenantFeatureData = {
      ...seedConfig.systemMenus.tenantFeature,
      pid: menuIds.tenant.id,
      meta: JSON.stringify(seedConfig.systemMenus.tenantFeature.meta),
    };
    menuIds.tenantFeature = await publicPrisma.systemMenu.create({
      data: tenantFeatureData,
    });
    console.log(`系统菜单创建成功: ${menuIds.tenantFeature.id} (${seedConfig.systemMenus.tenantFeature.name})`);

    // 创建系统管理子菜单 - 租户配置管理
    const tenantConfigData = {
      ...seedConfig.systemMenus.tenantConfig,
      pid: menuIds.tenant.id,
      meta: JSON.stringify(seedConfig.systemMenus.tenantConfig.meta),
    };
    menuIds.tenantConfig = await publicPrisma.systemMenu.create({
      data: tenantConfigData,
    });
    console.log(`系统菜单创建成功: ${menuIds.tenantConfig.id} (${seedConfig.systemMenus.tenantConfig.name})`);

    // 创建系统管理子菜单 - 用户管理
    const userData = {
      ...seedConfig.systemMenus.user,
      pid: menuIds.system.id,
      meta: JSON.stringify(seedConfig.systemMenus.user.meta),
    };
    menuIds.user = await publicPrisma.systemMenu.create({
      data: userData,
    });
    console.log(`系统菜单创建成功: ${menuIds.user.id} (${seedConfig.systemMenus.user.name})`);

    // 创建系统管理子菜单 - 功能模板管理
    const featureTemplateData = {
      ...seedConfig.systemMenus.featureTemplate,
      pid: menuIds.system.id,
      meta: JSON.stringify(seedConfig.systemMenus.featureTemplate.meta),
    };
    menuIds.featureTemplate = await publicPrisma.systemMenu.create({
      data: featureTemplateData,
    });
    console.log(`系统菜单创建成功: ${menuIds.featureTemplate.id} (${seedConfig.systemMenus.featureTemplate.name})`);

    // 创建系统管理子菜单 - 功能代码管理
    const featureCodeData = {
      ...seedConfig.systemMenus.featureCode,
      pid: menuIds.system.id,
      meta: JSON.stringify(seedConfig.systemMenus.featureCode.meta),
    };
    menuIds.featureCode = await publicPrisma.systemMenu.create({
      data: featureCodeData,
    });
    console.log(`系统菜单创建成功: ${menuIds.featureCode.id} (${seedConfig.systemMenus.featureCode.name})`);

    // 创建租户管理菜单
    const tenantManagementData = {
      ...seedConfig.systemMenus.tenantManagement,
      meta: JSON.stringify(seedConfig.systemMenus.tenantManagement.meta),
    };
    menuIds.tenantManagement = await publicPrisma.systemMenu.create({
      data: tenantManagementData,
    });
    console.log(`系统菜单创建成功: ${menuIds.tenantManagement.id} (${seedConfig.systemMenus.tenantManagement.name})`);

    // 创建租户管理子菜单 - 功能看板
    const tenantFeaturesData = {
      ...seedConfig.systemMenus.tenantFeatures,
      pid: menuIds.tenantManagement.id,
      meta: JSON.stringify(seedConfig.systemMenus.tenantFeatures.meta),
    };
    menuIds.tenantFeatures = await publicPrisma.systemMenu.create({
      data: tenantFeaturesData,
    });
    console.log(`系统菜单创建成功: ${menuIds.tenantFeatures.id} (${seedConfig.systemMenus.tenantFeatures.name})`);

    // 创建租户管理子菜单 - 配置中心
    const tenantConfigsData = {
      ...seedConfig.systemMenus.tenantConfigs,
      pid: menuIds.tenantManagement.id,
      meta: JSON.stringify(seedConfig.systemMenus.tenantConfigs.meta),
    };
    menuIds.tenantConfigs = await publicPrisma.systemMenu.create({
      data: tenantConfigsData,
    });
    console.log(`系统菜单创建成功: ${menuIds.tenantConfigs.id} (${seedConfig.systemMenus.tenantConfigs.name})`);

    // 7. 分配菜单给系统角色
    console.log('分配菜单给系统角色...');

    // 超级管理员角色菜单 - 拥有所有菜单权限
    const superAdminMenus = Object.values(menuIds).map(menu => menu.id);

    // 测试管理员角色菜单 - 只有仪表盘和部分系统管理菜单
    const testAdminMenus = [
      menuIds.dashboard.id,
      menuIds.analytics.id,
      menuIds.workspace.id,
      menuIds.system.id,
      menuIds.user.id,
      menuIds.role.id,
    ];

    // 为超级管理员角色分配所有菜单
    console.log(`为超级管理员角色分配 ${superAdminMenus.length} 个菜单...`);
    for (const menuId of superAdminMenus) {
      await publicPrisma.systemRoleMenu.create({
        data: {
          roleId: systemRole.id,
          menuId,
        },
      });
    }

    // 为测试管理员角色分配部分菜单
    console.log(`为测试管理员角色分配 ${testAdminMenus.length} 个菜单...`);
    for (const menuId of testAdminMenus) {
      await publicPrisma.systemRoleMenu.create({
        data: {
          roleId: systemRole2.id,
          menuId,
        },
      });
    }
    console.log('菜单分配成功');

    // 8. 创建系统权限（先删除现有权限）
    console.log('删除现有系统权限...');
    await publicPrisma.systemPermission.deleteMany({});

    console.log('创建系统权限...');
    const permissions = seedConfig.permissions;
    const permissionMap = {}; // 用于存储权限ID，方便后续分配

    // 创建所有权限
    for (const permission of permissions) {
      const createdPermission = await publicPrisma.systemPermission.create({
        data: {
          name: permission.name,
          code: permission.code,
          description: permission.description,
          status: 1,
        },
      });
      console.log(`系统权限创建成功: ${createdPermission.id} (${permission.name})`);

      // 存储权限ID，以权限代码为键
      permissionMap[permission.code] = createdPermission.id;
    }

    // 定义超级管理员角色权限 - 拥有所有权限
    const superAdminPermissions = Object.values(permissionMap);

    // 定义测试管理员角色权限 - 只有查询权限和部分管理权限
    const testAdminPermissions = [];

    // 添加所有查询权限
    for (const [code, id] of Object.entries(permissionMap)) {
      if (code.includes(':list')) {
        testAdminPermissions.push(id);
      }
    }

    // 添加用户和角色的管理权限
    for (const [code, id] of Object.entries(permissionMap)) {
      if (code.startsWith('system:user:') || code.startsWith('system:role:')) {
        if (!testAdminPermissions.includes(id)) {
          testAdminPermissions.push(id);
        }
      }
    }

    // 为超级管理员角色分配所有权限
    console.log(`为超级管理员角色分配 ${superAdminPermissions.length} 个权限...`);
    for (const permissionId of superAdminPermissions) {
      await publicPrisma.systemRolePermission.create({
        data: {
          roleId: systemRole.id,
          permissionId,
        },
      });
    }

    // 为测试管理员角色分配部分权限
    console.log(`为测试管理员角色分配 ${testAdminPermissions.length} 个权限...`);
    for (const permissionId of testAdminPermissions) {
      await publicPrisma.systemRolePermission.create({
        data: {
          roleId: systemRole2.id,
          permissionId,
        },
      });
    }

    console.log('权限分配成功');

    // 8.5 创建功能代码
    console.log('创建功能代码...');
    await seedFeatureCodes(publicPrisma);
    console.log('功能代码创建成功');

    // 8.6 创建功能模板
    console.log('创建功能模板...');
    await seedFeatureTemplates(publicPrisma);
    console.log('功能模板创建成功');

    // 9. 创建租户管理员（先检查是否存在）
    console.log('检查并创建租户管理员...');

    // 检查租户管理员是否存在
    let tenantAdmin = await tenantPrisma.user.findFirst({
      where: {
        username: seedConfig.tenantAdmin.username,
        tenantId: tenant.id // 使用租户ID（数字）
      },
    });

    // 如果不存在则创建
    if (!tenantAdmin) {
      tenantAdmin = await tenantPrisma.user.create({
        data: {
          username: seedConfig.tenantAdmin.username,
          password: adminPasswordHash,
          realName: seedConfig.tenantAdmin.realName,
          emailAddress: seedConfig.tenantAdmin.email,
          status: seedConfig.tenantAdmin.status,
          tenantId: tenant.id, // 使用租户ID（数字）
        },
      });
      console.log(`租户管理员创建成功: ${tenantAdmin.id}`);
    } else {
      console.log(`使用已存在的租户管理员: ${tenantAdmin.id}`);
    }

    // 10. 创建租户角色（先删除现有角色）
    console.log('删除现有租户角色...');
    await tenantPrisma.rolePermission.deleteMany({
      where: { tenantId: tenant.id } // 使用租户ID（数字）
    });
    await tenantPrisma.roleMenu.deleteMany({
      where: { tenantId: tenant.id } // 使用租户ID（数字）
    });
    await tenantPrisma.userRole.deleteMany({
      where: { tenantId: tenant.id } // 使用租户ID（数字）
    });
    await tenantPrisma.role.deleteMany({
      where: { tenantId: tenant.id } // 使用租户ID（数字）
    });

    console.log('创建租户角色...');
    const tenantRole = await tenantPrisma.role.create({
      data: {
        ...seedConfig.tenantRoles[0],
        tenantId: tenant.id, // 使用租户ID（数字）
      },
    });
    console.log(`租户角色创建成功: ${tenantRole.id}`);

    // 创建另一个租户角色
    const tenantRole2 = await tenantPrisma.role.create({
      data: {
        ...seedConfig.tenantRoles[1],
        tenantId: tenant.id, // 使用租户ID（数字）
      },
    });
    console.log(`租户测试角色创建成功: ${tenantRole2.id}`);

    // 注意：租户菜单不再在初始化脚本中创建
    // 租户菜单将通过其他方式动态生成，例如：
    // 1. 在租户创建时，基于系统菜单自动生成租户菜单
    // 2. 通过管理界面，由系统管理员为租户分配菜单
    // 3. 在运行时，根据租户权限动态生成菜单

    // 11. 分配角色给租户管理员
    console.log('分配角色给租户管理员...');
    await tenantPrisma.userRole.create({
      data: {
        userId: tenantAdmin.id,
        roleId: tenantRole.id,
        tenantId: tenant.id, // 使用租户ID（数字）
      },
    });

    // 同时分配测试角色给租户管理员
    await tenantPrisma.userRole.create({
      data: {
        userId: tenantAdmin.id,
        roleId: tenantRole2.id,
        tenantId: tenant.id, // 使用租户ID（数字）
      },
    });
    console.log('角色分配成功');

    // 12. 创建租户配置
    console.log('创建租户配置...');
    await seedTenantConfigs(publicPrisma, tenant.id);
    console.log('租户配置创建成功');

    // 13. 创建租户功能权限
    console.log('创建租户功能权限...');
    await seedTenantFeatures(publicPrisma, tenant.id);
    console.log('租户功能权限创建成功');

    // 14. 创建租户功能使用记录
    console.log('创建租户功能使用记录...');
    await seedFeatureUsage(publicPrisma, tenant.id);
    console.log('租户功能使用记录创建成功');

    console.log('初始数据添加完成！');
  } catch (error) {
    console.error('添加初始数据失败:', error);
  } finally {
    // 关闭数据库连接
    await publicPrisma.$disconnect();
    await tenantPrisma.$disconnect();
  }
}

// 执行初始化
main().catch(error => {
  console.error('初始化失败:', error);
  process.exit(1);
});
