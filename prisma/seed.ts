import { PrismaClient } from '@prisma/client';
import * as bcryptjs from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 开始数据库初始化...\n');

  // ================================
  // 1. 创建系统级角色
  // ================================
  console.log('👥 创建系统级角色...');

  const systemRoles = [
    {
      code: 'SUPER_ADMIN',
      name: '超级管理员',
      description: '系统最高权限，可以管理所有功能',
      permissions: [
        'SYSTEM_ADMIN',
        'TENANT_MANAGE',
        'USER_MANAGE',
        'SUBSCRIPTION_MANAGE',
        'SYSTEM_CONFIG',
        'AUDIT_LOG',
        'PLATFORM_ANALYTICS',
      ],
      isSystem: true,
      tenantId: null,
    },
    {
      code: 'SYSTEM_ADMIN',
      name: '系统管理员',
      description: '系统管理权限，可以管理租户和用户',
      permissions: [
        'TENANT_MANAGE',
        'USER_MANAGE',
        'SUBSCRIPTION_MANAGE',
        'AUDIT_LOG',
        'PLATFORM_ANALYTICS',
      ],
      isSystem: true,
      tenantId: null,
    },
    {
      code: 'TENANT_ADMIN',
      name: '租户管理员',
      description: '租户管理权限，可以管理租户内的用户和配置',
      permissions: ['USER_MANAGE', 'WEBSITE_MANAGE', 'TENANT_CONFIG'],
      isSystem: false,
      tenantId: null,
    },
  ];

  for (const role of systemRoles) {
    await prisma.role.upsert({
      where: {
        tenantId_code: {
          tenantId: role.tenantId,
          code: role.code,
        },
      },
      update: {
        name: role.name,
        description: role.description,
        permissions: role.permissions,
        updateTime: new Date(),
      },
      create: role,
    });
  }

  console.log('✅ 系统级角色创建完成');

  // ================================
  // 2. 创建系统管理员用户
  // ================================
  console.log('👤 创建系统管理员用户...');

  const hashedPassword = await bcryptjs.hash('FlexiHub2024!', 10);

  const superAdmin = await prisma.user.upsert({
    where: {
      user_tenant_username_key: {
        tenantId: null,
        username: 'admin',
      },
    },
    update: {
      password: hashedPassword,
      email: '<EMAIL>',
      realName: '系统管理员',
      userType: 'SYSTEM',
      updateTime: new Date(),
    },
    create: {
      tenantId: null,
      username: 'admin',
      password: hashedPassword,
      email: '<EMAIL>',
      realName: '系统管理员',
      phoneNumber: '13800138000',
      avatar: null,
      status: 1,
      userType: 'SYSTEM',
      metadata: {
        role: 'super_admin',
        created_by: 'system',
        is_default: true,
      },
    },
  });

  // 分配超级管理员角色
  const superAdminRole = await prisma.role.findFirst({
    where: {
      code: 'SUPER_ADMIN',
      isSystem: true,
    },
  });

  if (superAdminRole) {
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: superAdmin.id,
          roleId: superAdminRole.id,
        },
      },
      update: {},
      create: {
        userId: superAdmin.id,
        roleId: superAdminRole.id,
      },
    });
  }

  console.log('✅ 系统管理员用户创建完成');

  // ================================
  // 3. 创建演示网站
  // ================================
  console.log('🌐 创建演示网站...');

  await prisma.website.upsert({
    where: { id: 1 },
    update: {
      name: 'FlexiHub官网',
      domain: 'www.flexihub.com',
      status: 'draft',
      updatedAt: new Date(),
    },
    create: {
      id: 1,
      tenantId: null,
      name: 'FlexiHub官网',
      domain: 'www.flexihub.com',
      websiteType: 'system',
      status: 'draft',
      config: {
        theme: 'modern',
        colors: {
          primary: '#2563eb',
          secondary: '#64748b',
        },
        layout: 'standard',
      },
      content: {
        header: {
          logo: '/images/logo.png',
          navigation: [
            { label: '首页', href: '/' },
            { label: '产品', href: '/products' },
            { label: '价格', href: '/pricing' },
            { label: '关于', href: '/about' },
          ],
        },
        hero: {
          title: '强大的多租户建站平台',
          subtitle: '让每个人都能轻松创建专业网站',
          cta: {
            primary: { text: '立即开始', href: '/register' },
            secondary: { text: '了解更多', href: '/about' },
          },
        },
      },
      seoConfig: {
        title: 'FlexiHub - 多租户建站平台',
        description: '专业的多租户建站解决方案，支持快速部署、自定义主题、SEO优化',
        keywords: ['建站平台', '多租户', 'SaaS', '网站建设'],
      },
      analytics: {
        google: {
          enabled: true,
          trackingId: 'GA-XXXXXXX',
        },
        baidu: {
          enabled: true,
          trackingId: 'BAIDU-XXXXXXX',
        },
      },
      metadata: {
        version: '1.0.0',
        template: 'official',
        features: ['seo', 'analytics', 'responsive'],
      },
    },
  });

  console.log('✅ 演示网站创建完成');

  // ================================
  // 4. 创建演示页面
  // ================================
  console.log('📄 创建演示页面...');

  const demoPages = [
    {
      websiteId: 1,
      path: '/',
      title: '首页',
      content: {
        type: 'page',
        blocks: [
          {
            type: 'hero',
            title: '欢迎使用FlexiHub',
            subtitle: '强大的多租户建站平台',
          },
          {
            type: 'features',
            items: [
              { title: '快速部署', description: '5分钟即可完成网站搭建' },
              { title: '自定义主题', description: '丰富的主题模板库' },
              { title: 'SEO优化', description: '内置SEO优化功能' },
            ],
          },
        ],
      },
      status: 'draft',
    },
    {
      websiteId: 1,
      path: '/about',
      title: '关于我们',
      content: {
        type: 'page',
        blocks: [
          {
            type: 'text',
            content: '关于FlexiHub的详细介绍...',
          },
        ],
      },
      status: 'draft',
    },
  ];

  for (const page of demoPages) {
    await prisma.websitePage.upsert({
      where: {
        websiteId_path: {
          websiteId: page.websiteId,
          path: page.path,
        },
      },
      update: {
        title: page.title,
        content: page.content,
        status: page.status,
        updatedAt: new Date(),
      },
      create: page,
    });
  }

  console.log('✅ 演示页面创建完成');

  // ================================
  // 5. 创建示例通知
  // ================================
  console.log('📮 创建示例通知...');

  await prisma.notification.upsert({
    where: { id: 1 },
    update: {
      title: '欢迎使用FlexiHub',
      content: '感谢您选择FlexiHub多租户建站平台！',
    },
    create: {
      id: 1,
      tenantId: null,
      userId: superAdmin.id,
      type: 'welcome',
      title: '欢迎使用FlexiHub',
      content: '感谢您选择FlexiHub多租户建站平台！开始您的建站之旅吧。',
      isRead: false,
      metadata: {
        category: 'system',
        priority: 'normal',
      },
    },
  });

  console.log('✅ 示例通知创建完成');

  // ================================
  // 输出统计信息
  // ================================
  console.log('\n📊 数据库初始化统计:');
  console.log('==========================');

  const stats = {
    users: await prisma.user.count(),
    roles: await prisma.role.count(),
    userRoles: await prisma.userRole.count(),
    websites: await prisma.website.count(),
    websitePages: await prisma.websitePage.count(),
    notifications: await prisma.notification.count(),
    usageStats: await prisma.usageStats.count(),
    payments: await prisma.payment.count(),
  };

  Object.entries(stats).forEach(([key, value]) => {
    console.log(`${key}: ${value}`);
  });

  console.log('\n🎉 数据库初始化完成！');
  console.log('默认管理员账号: admin / FlexiHub2024!');
}

main()
  .catch(e => {
    console.error('❌ 数据库初始化失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
