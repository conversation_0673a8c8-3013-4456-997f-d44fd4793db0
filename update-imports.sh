#!/bin/bash

# 更新 api-code.constant 的导入路径
find src -type f -name "*.ts" -exec sed -i '' 's|@/constants/api-code.constant|@/core/common/constants/api-code.constant|g' {} \;

# 更新 date-format.util 的导入路径
find src -type f -name "*.ts" -exec sed -i '' 's|@/utils/date-format.util|@/core/common/utils/date-format.util|g' {} \;

# 更新 IRequestWithProps 的导入路径
find src -type f -name "*.ts" -exec sed -i '' 's|@/types/IRequestWithProps|@/core/common/interfaces/request-with-props.interface|g' {} \;

# 更新 system-paths.constant 的导入路径
find src -type f -name "*.ts" -exec sed -i '' 's|@/constants/system-paths.constant|@/core/common/constants/system-paths.constant|g' {} \;

echo "导入路径更新完成"
