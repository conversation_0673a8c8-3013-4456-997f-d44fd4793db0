import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { JwtModule } from '@nestjs/jwt';
import { TerminusModule } from '@nestjs/terminus';
import * as Joi from 'joi';
import { LoggerModule } from 'nestjs-pino';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigValidatorSchema } from './config/config-validator.schema';
import { AdminGuard } from './core/auth/guards/admin.guard';
import { JwtAuthGuard } from './core/auth/guards/jwt-auth.guard';
import { CacheModule } from './core/cache/cache.module';
import { HttpExceptionFilter } from './core/common/filters/http-exception.filter';
import { PerformanceInterceptor } from './core/common/interceptors/performance.interceptor';
import { TransformInterceptor } from './core/common/interceptors/transform.interceptor';
import { CoreModule } from './core/core.module';
import { GlobalExceptionFilter } from './core/filters/global-exception.filter';
import { HttpLoggerMiddleware } from './core/middleware/http-logger.middleware';
import { RequestIdMiddleware } from './core/middleware/request-id.middleware';
import { TenantDatasourceMiddleware } from './core/middleware/tenant-datasource.middleware';
import { TenantRoutingMiddleware } from './core/middleware/tenant-routing.middleware';
import { SwaggerModule } from './core/swagger/swagger.module';
import { ComponentLibraryModule } from './modules/component-library/component-library.module';
import { DatasourceModule } from './modules/datasource/datasource.module';
import { DepartmentModule } from './modules/department/department.module';
import { MediaAssetModule } from './modules/media-asset/media-asset.module';
import { MembershipModule } from './modules/membership/membership.module';
import { MenuModule } from './modules/menu/menu.module';
import { PaymentModule } from './modules/payment/payment.module';
import { PermissionModule } from './modules/permission/permission.module';
import { RoleModule } from './modules/role/role.module';
import { SitemapModule } from './modules/sitemap/sitemap.module';
import { TenantModule } from './modules/tenant/tenant.module';
import { TenantFeatureModule } from './modules/tenant-feature/tenant-feature.module';
import { TenantSubscriptionModule } from './modules/tenant-subscription/tenant-subscription.module';
import { UserModule } from './modules/user/user.module';
import { WebsiteModule } from './modules/website/website.module';
import { WebsiteFormModule } from './modules/website-form/website-form.module';
import { WebsitePageModule } from './modules/website-page/website-page.module';
import { WebsiteSeoModule } from './modules/website-seo/website-seo.module';
import { WebsiteTemplateModule } from './modules/website-template/website-template.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema: ConfigValidatorSchema,
    }),
    LoggerModule.forRoot({
      pinoHttp: {
        transport:
          process.env.NODE_ENV !== 'production'
            ? {
                target: 'pino-pretty',
                options: {
                  singleLine: true,
                },
              }
            : undefined,
        redact: ['req.headers.authorization'],
      },
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRE_TIME') || '7d',
        },
      }),
    }),
    EventEmitterModule.forRoot({
      // 设置为 true 表示事件将按照顺序处理
      wildcard: false,
      delimiter: '.',
      maxListeners: 10,
      verboseMemoryLeak: true,
      ignoreErrors: false,
    }),
    TerminusModule,
    CoreModule, // 导入核心模块
    CacheModule, // 导入缓存模块
    SwaggerModule, // 导入Swagger模块
    UserModule,
    MenuModule,
    RoleModule,
    PermissionModule,
    TenantModule,
    DatasourceModule,
    DepartmentModule,
    TenantFeatureModule,
    MembershipModule, // 会员模块
    TenantSubscriptionModule, // 租户订阅模块
    PaymentModule,
    WebsiteTemplateModule, // 网站模板模块
    WebsiteModule, // 网站管理模块
    WebsitePageModule, // 网站页面管理模块
    MediaAssetModule, // 媒体资源管理模块
    ComponentLibraryModule, // 组件库管理模块
    WebsiteSeoModule, // 网站SEO管理模块
    SitemapModule, // 站点地图管理模块
    WebsiteFormModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    // 全局守卫 - JWT认证
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    // 全局守卫 - 管理员权限
    {
      provide: APP_GUARD,
      useClass: AdminGuard,
    },
    // 全局拦截器 - 响应转换
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
    // 全局拦截器 - 性能监控
    {
      provide: APP_INTERCEPTOR,
      useClass: PerformanceInterceptor,
    },
    // 全局异常过滤器
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestIdMiddleware).forRoutes('*');
    consumer.apply(HttpLoggerMiddleware).forRoutes('*');
    consumer.apply(TenantDatasourceMiddleware).forRoutes('*');
    consumer.apply(TenantRoutingMiddleware).forRoutes('*');
  }
}
