import * as Jo<PERSON> from 'joi';

export const ConfigValidatorSchema = Joi.object({
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test', 'staging')
    .default('development'),
  PORT: Joi.number().port().default(3000),
  CORS_ORIGINS: Joi.string().default('*'),
  DATABASE_URL: Joi.string(),
  PUBLIC_DATABASE_URL: Joi.string(),
  JWT_SECRET: Joi.string().required(),
  JWT_EXPIRES_IN: Joi.string().default('1h'),
  DEFAULT_ROLE_CODE: Joi.string().default('USER_100001'),
  DEFAULT_HOME_PATH: Joi.string().default('/dashboard'),
  LOG_LEVEL: Joi.string().valid('debug', 'info', 'warn', 'error').default('debug'),
  // Redis配置
  REDIS_ENABLED: Joi.boolean().default(false),
  REDIS_HOST: Joi.string().default('localhost'),
  REDIS_PORT: Joi.number().default(6379),
  REDIS_PASSWORD: Joi.string().allow('').default(''),
  REDIS_DB: Joi.number().default(0),
  CACHE_TTL: Joi.number().default(60 * 60 * 1000), // 默认1小时
});
