/*
 * @Author: yang<PERSON>qi<PERSON> <EMAIL>
 * @Date: 2025-05-08 14:16:44
 * @LastEditors: yangwenqiang <EMAIL>
 * @LastEditTime: 2025-05-09 22:27:55
 * @FilePath: /multi-tenant-nestjs/src/app.controller.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Controller, Get } from '@nestjs/common';
import { HealthCheck } from '@nestjs/terminus';
import * as metadata from 'package.json';

import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @HealthCheck()
  async getHealthcheck() {
    const app = {
      name: metadata.name,
      version: metadata.version,
      environment: process.env.NODE_ENV,
    };

    const healthcheck = await this.appService.getHealthcheck();

    return { app, ...healthcheck };
  }
}
