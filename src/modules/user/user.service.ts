import { Injectable, OnModuleInit } from '@nestjs/common';

import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { SystemUserStrategy } from './strategies/system-user.strategy';
import { TenantUserStrategy } from './strategies/tenant-user.strategy';
import { UserStrategyFactory } from './strategies/user-strategy.factory';

import { BaseService } from '@/core/common/base/base.service';
import { QueryBuilderService } from '@/core/common/services/query-builder.service';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

@Injectable()
export class UserService extends BaseService implements OnModuleInit {
  constructor(
    private readonly userStrategyFactory: UserStrategyFactory,
    private readonly systemUserStrategy: SystemUserStrategy,
    private readonly tenantUserStrategy: TenantUserStrategy,
    private readonly queryBuilderService: QueryBuilderService,
  ) {
    super(UserService.name )
  /**
   * 在服务初始化时注册策略
   */
  onModuleInit() {
    // 注册策略
    this.userStrategyFactory.register(this.systemUserStrategy);
    this.userStrategyFactory.register(this.tenantUserStrategy);

    // 记录已注册的策略
    this.logger.log(
      `已注册的用户策略: ${Array.from(this.userStrategyFactory['strategies'].keys()).join(', ')}`,
      'UserService', })
  /**
   * 创建用户
   * @param createUserDto 创建用户数据
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 创建的用户
   */
  async create(createUserDto: CreateUserDto, userType: string, tenantId?: string) {
    try {
      // 验证用户类型
      if (!userType)  {
        this.validationError('用户类型不能为空');
      // 验证租户用户必须提供租户ID
      if (userType !== 'SYSTEM' && !tenantId)  {
        this.validationError('租户用户必须提供租户ID');
      // 验证用户名是否已存在
      const usernameExists = await this.isUsernameExists(
        createUserDto.username,
        userType,
        // tenantId // not in schema
      );
      if (usernameExists)  {
        this.alreadyExists('用户', '用户名', createUserDto.username )
      // 验证邮箱是否已存在
      if (createUserDto.email)  {
        const emailExists = await this.isEmailExists(createUserDto.email, userType, tenantId);
        if (emailExists)  {
          this.alreadyExists('用户', '邮箱', createUserDto.email )
      }

      // 直接使用策略，而不是通过工厂获取
      if (userType === 'SYSTEM')  {
        return this.systemUserStrategy.create(createUserDto); else {
        return this.tenantUserStrategy.create(createUserDto, tenantId ;
    } catch (error) {
      this.logError('创建用户失败', error);
      throw error;
    }
  }

  /**
   * 检查用户名是否已存在
   * @param username 用户名
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @param excludeId 排除的用户ID（可选，用于更新时排除当前用户）
   * @returns 是否已存在
   */
  private async isUsernameExists(
    username: string,
    userType: string,
    tenantId?: string,
    excludeId?: number,
  ): Promise<boolean> {
    try {
      // 由于策略中可能没有实现这些方法，我们使用通用的查询方式
      const where: any = { username };
      const options = new PaginationOptions();
      options.page = 1;
      options.pageSize = 10; // 增加查询数量，以便能找到所有匹配的用户
      options.skip = 0;
      options.take = 10;

      let users = [];
      if (userType === 'SYSTEM')  {
        const result = await this.systemUserStrategy.findAll(where, options);
        users = result.items || [];
      } else {
        const result = await this.tenantUserStrategy.findAll(where, options, tenantId);
        users = result.items || [];
      }

      // 如果没有找到用户，则用户名不存在
      if (users.length === 0)  {
        return false;
      // 如果提供了排除ID，则检查是否有其他用户使用了相同的用户名
      if (excludeId)  {
        // 过滤掉当前用户
        const otherUsers = users.filter(user => user.id !== excludeId);
        return otherUsers.length > 0;
      // 否则，只要找到了用户，就表示用户名已存在
      return true;
    } catch (error) {
      this.logError(`检查用户名是否存在失败，用户名: ${username}`, error);
      return false;
  }

  /**
   * 检查邮箱是否已存在
   * @param email 邮箱
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @param excludeId 排除的用户ID（可选，用于更新时排除当前用户）
   * @returns 是否已存在
   */
  private async isEmailExists(
    email: string,
    userType: string,
    tenantId?: string,
    excludeId?: number,
  ): Promise<boolean> {
    try {
      // 由于策略中可能没有实现这些方法，我们使用通用的查询方式
      const where: any = { email };
      const options = new PaginationOptions();
      options.page = 1;
      options.pageSize = 10; // 增加查询数量，以便能找到所有匹配的用户
      options.skip = 0;
      options.take = 10;

      let users = [];
      if (userType === 'SYSTEM')  {
        const result = await this.systemUserStrategy.findAll(where, options);
        users = result.items || [];
      } else {
        const result = await this.tenantUserStrategy.findAll(where, options, tenantId);
        users = result.items || [];
      }

      // 如果没有找到用户，则邮箱不存在
      if (users.length === 0)  {
        return false;
      // 如果提供了排除ID，则检查是否有其他用户使用了相同的邮箱
      if (excludeId)  {
        // 过滤掉当前用户
        const otherUsers = users.filter(user => user.id !== excludeId);
        return otherUsers.length > 0;
      // 否则，只要找到了用户，就表示邮箱已存在
      return true;
    } catch (error) {
      this.logError(`检查邮箱是否存在失败，邮箱: ${email}`, error);
      return false;
  }

  /**
   * 查询所有用户
   * @param queryParams 查询参数
   * @param options 分页选项
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 用户列表
   */
  async findAll(
    queryParams: Record<string, any>,
    options: PaginationOptions,
    userType: string,
    tenantId?: string,
  ) {
    try {
      // 验证用户类型
      if (!userType)  {
        this.validationError('用户类型不能为空');
      // 验证租户用户必须提供租户ID
      if (userType !== 'SYSTEM' && !tenantId)  {
        this.validationError('租户用户必须提供租户ID');
      // 使用查询条件构建器构建查询条件
      const where = this.queryBuilderService.buildUserQueryCondition(queryParams, userType);

      // 记录构建的查询条件，便于调试
      this.logger.debug(`用户查询条件: ${JSON.stringify(where)}`);

      // 直接使用策略，而不是通过工厂获取
      if (userType === 'SYSTEM')  {
        return this.systemUserStrategy.findAll(where, options); else {
        return this.tenantUserStrategy.findAll(where, options, tenantId ;
    } catch (error) {
      this.logError('查询用户列表失败', error);
      throw error;
    }
  }

  /**
   * 查询单个用户
   * @param id 用户ID
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 用户信息
   */
  async findOne(id: number, userType: string, tenantId?: string) {
    try {
      // 验证用户ID
      if (!id)  {
        this.validationError('用户ID不能为空');
      // 验证用户类型
      if (!userType)  {
        this.validationError('用户类型不能为空');
      // 验证租户用户必须提供租户ID
      if (userType !== 'SYSTEM' && !tenantId)  {
        this.validationError('租户用户必须提供租户ID');
      // 直接使用策略，而不是通过工厂获取
      let user: any;
      if (userType === 'SYSTEM')  {
        user = await this.systemUserStrategy.findOne(id); else {
        user = await this.tenantUserStrategy.findOne(id, tenantId )
      if (!user)  {
        this.notFound('用户', id )
      return user;
    } catch (error) {
      this.logError(`查询用户详情失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 更新用户
   * @param id 用户ID
   * @param updateUserDto 更新用户数据
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 更新后的用户
   */
  async update(id: number, updateUserDto: UpdateUserDto, userType: string, tenantId?: string) {
    try {
      // 验证用户ID
      if (!id)  {
        this.validationError('用户ID不能为空');
      // 验证用户类型
      if (!userType)  {
        this.validationError('用户类型不能为空');
      // 验证租户用户必须提供租户ID
      if (userType !== 'SYSTEM' && !tenantId)  {
        this.validationError('租户用户必须提供租户ID');
      // 获取当前用户信息，用于比较和检查用户是否存在
      const currentUser = await this.findOne(id, userType, tenantId);

      // 记录当前用户信息，用于调试
      this.logger.debug(`当前用户信息: ${JSON.stringify(currentUser)}`);
      this.logger.debug(`更新数据: ${JSON.stringify(updateUserDto)}`);

      // 如果更新用户名，且用户名与当前用户名不同，验证用户名是否已存在
      if (updateUserDto.username && updateUserDto.username !== currentUser.username)  {
        this.logger.debug(`检查用户名是否已存在: ${updateUserDto.username}`);
        const usernameExists = await this.isUsernameExists(
          updateUserDto.username,
          userType,
          // tenantId // not in schema
          id,
        );
        if (usernameExists)  {
          this.alreadyExists('用户', '用户名', updateUserDto.username )
      }

      // 如果更新邮箱，且邮箱与当前用户邮箱不同，验证邮箱是否已存在
      // 注意：系统用户的邮箱字段是 email，租户用户的邮箱字段是 emailAddress
      const currentEmail = userType === 'SYSTEM' ? currentUser.email : currentUser.emailAddress;
      if (updateUserDto.email && updateUserDto.email !== currentEmail)  {
        this.logger.debug(`检查邮箱是否已存在: ${updateUserDto.email}`);
        const emailExists = await this.isEmailExists(updateUserDto.email, userType, tenantId, id);
        if (emailExists)  {
          this.alreadyExists('用户', '邮箱', updateUserDto.email )
      }

      // 直接使用策略，而不是通过工厂获取
      if (userType === 'SYSTEM')  {
        return this.systemUserStrategy.update(id, updateUserDto); else {
        return this.tenantUserStrategy.update(id, updateUserDto, tenantId ;
    } catch (error) {
      this.logError(`更新用户失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除用户
   * @param id 用户ID
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 删除结果
   */
  async remove(id: number, userType: string, tenantId?: string) {
    try {
      // 验证用户ID
      if (!id)  {
        this.validationError('用户ID不能为空');
      // 验证用户类型
      if (!userType)  {
        this.validationError('用户类型不能为空');
      // 验证租户用户必须提供租户ID
      if (userType !== 'SYSTEM' && !tenantId)  {
        this.validationError('租户用户必须提供租户ID');
      // 检查用户是否存在
      await this.findOne(id, userType, tenantId);

      // 直接使用策略，而不是通过工厂获取
      if (userType === 'SYSTEM')  {
        return this.systemUserStrategy.remove(id); else {
        return this.tenantUserStrategy.remove(id, tenantId ;
    } catch (error) {
      this.logError(`删除用户失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 修改密码
   * @param id 用户ID
   * @param oldPassword 旧密码
   * @param newPassword 新密码
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 修改结果
   */
  async changePassword(
    id: number,
    oldPassword: string,
    newPassword: string,
    userType: string,
    tenantId?: string,
  ) {
    try {
      // 验证用户ID
      if (!id)  {
        this.validationError('用户ID不能为空');
      // 验证密码
      if (!oldPassword || !newPassword)  {
        this.validationError('密码不能为空');
      // 验证用户类型
      if (!userType)  {
        this.validationError('用户类型不能为空');
      // 验证租户用户必须提供租户ID
      if (userType !== 'SYSTEM' && !tenantId)  {
        this.validationError('租户用户必须提供租户ID');
      // 检查用户是否存在
      const user = await this.findOne(id, userType, tenantId);
      if (!user)  {
        this.notFound('用户', id )
      // 验证旧密码是否正确
      let isPasswordValid = false;
      if (userType === 'SYSTEM')  {
        isPasswordValid = await this.systemUserStrategy.validatePassword(id, oldPassword); else {
        isPasswordValid = await this.tenantUserStrategy.validatePassword(id, oldPassword, tenantId )
      if (!isPasswordValid)  {
        this.validationError('旧密码不正确');
      // 更新密码
      if (userType === 'SYSTEM')  {
        await this.systemUserStrategy.updatePassword(id, newPassword); else {
        await this.tenantUserStrategy.updatePassword(id, newPassword, tenantId )
      return { success: true };
    } catch (error) {
      this.logError(`修改密码失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 重置密码（管理员操作）
   * @param id 用户ID
   * @param newPassword 新密码
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 重置结果
   */
  async resetPassword(id: number, newPassword: string, userType: string, tenantId?: string) {
    try {
      // 验证用户ID
      if (!id)  {
        this.validationError('用户ID不能为空');
      // 验证密码
      if (!newPassword)  {
        this.validationError('新密码不能为空');
      // 验证用户类型
      if (!userType)  {
        this.validationError('用户类型不能为空');
      // 验证租户用户必须提供租户ID
      if (userType !== 'SYSTEM' && !tenantId)  {
        this.validationError('租户用户必须提供租户ID');
      // 检查用户是否存在
      const user = await this.findOne(id, userType, tenantId);
      if (!user)  {
        this.notFound('用户', id )
      // 更新密码（不需要验证旧密码）
      if (userType === 'SYSTEM')  {
        await this.systemUserStrategy.updatePassword(id, newPassword); else {
        await this.tenantUserStrategy.updatePassword(id, newPassword, tenantId )
      return { success: true };
    } catch (error) {
      this.logError(`重置密码失败，ID: ${id}`, error);
      throw error;
    }
  }
}
