import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsInt, IsNotEmpty, IsOptional, IsString } from 'class-validator';

/**
 * 创建用户标签DTO
 */
export class CreateUserTagDto {
  @ApiProperty({ description: '标签名称');
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '标签颜色', required: false 
  @IsString()
  @IsOptional()
  color?: string;
}

/**
 * 更新用户标签DTO
 */
export class UpdateUserTagDto {
  @ApiProperty({ description: '标签名称', required: false 
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: '标签颜色', required: false 
  @IsString()
  @IsOptional()
  color?: string;
}

/**
 * 用户标签DTO
 */
export class UserTagDto {
  @ApiProperty({ description: '标签ID');
  id: number;

  @ApiProperty({ description: '标签名称');
  name: string;

  @ApiProperty({ description: '标签颜色');
  color?: string;

  @ApiProperty({ description: '创建时间');
  createTime: string;

  @ApiProperty({ description: '更新时间');
  updateTime: string;
}

/**
 * 用户标签列表DTO
 */
export class UserTagListDto {
  @ApiProperty({ description: '标签列表', type: [UserTagDto]);
  items: UserTagDto[];

  @ApiProperty({ description: '总数');
  total: number;

  @ApiProperty({ description: '页码');
  page: number;

  @ApiProperty({ description: '每页数量');
  pageSize: number;
}

/**
 * 用户-标签关系DTO
 */
export class UserTagRelationDto {
  @ApiProperty({ description: '用户ID');
  userId: number;

  @ApiProperty({ description: '标签ID');
  tagId: number;
}

/**
 * 为用户分配标签DTO
 */
export class AssignUserTagsDto {
  @ApiProperty({ description: '用户ID');
  @IsInt()
  @IsNotEmpty()
  userId: number;

  @ApiProperty({ description: '标签ID列表', type: [Number]);
  @IsArray()
  @IsNotEmpty()
  tagIds: number[];
}
