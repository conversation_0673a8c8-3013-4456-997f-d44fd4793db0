import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsObject } from 'class-validator';

/**
 * 创建用户扩展信息DTO
 */
export class CreateUserExtensionDto {
  @ApiProperty({ description: '用户ID');
  @IsInt()
  @IsNotEmpty()
  userId: number;

  @ApiProperty({ description: '扩展信息', type: 'object');
  @IsObject()
  @IsNotEmpty()
  extendedInfo: Record<string, any>;
}

/**
 * 更新用户扩展信息DTO
 */
export class UpdateUserExtensionDto {
  @ApiProperty({ description: '扩展信息', type: 'object');
  @IsObject()
  @IsNotEmpty()
  extendedInfo: Record<string, any>;
}

/**
 * 用户扩展信息DTO
 */
export class UserExtensionDto {
  @ApiProperty({ description: '扩展信息ID');
  id: number;

  @ApiProperty({ description: '用户ID');
  userId: number;

  @ApiProperty({ description: '扩展信息', type: 'object');
  extendedInfo: Record<string, any>;

  @ApiProperty({ description: '创建时间');
  createTime: string;

  @ApiProperty({ description: '更新时间');
  updateTime: string;
}
