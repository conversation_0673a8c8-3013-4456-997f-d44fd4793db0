import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';

import { PaginationOptions } from '@/core/pagination-options/pagination-options';
import { IBaseStrategy } from '@/core/strategies/base-strategy.interface';

/**
 * 用户策略接口
 * 定义用户相关的操作
 */
export interface IUserStrategy extends IBaseStrategy {
  /**
   * 创建用户
   * @param createUserDto 创建用户数据
   * @param tenantId 租户ID（可选）
   * @returns 创建的用户
   */
  create(createUserDto: CreateUserDto, tenantId?: string): Promise<any>;

  /**
   * 查询所有用户
   * @param where 查询条件
   * @param options 分页选项
   * @param tenantId 租户ID（可选）
   * @returns 用户列表
   */
  findAll(where: any, options: PaginationOptions, tenantId?: string): Promise<any>;

  /**
   * 查询单个用户
   * @param id 用户ID
   * @param tenantId 租户ID（可选）
   * @returns 用户信息
   */
  findOne(id: number, tenantId?: string): Promise<any>;

  /**
   * 更新用户
   * @param id 用户ID
   * @param updateUserDto 更新用户数据
   * @param tenantId 租户ID（可选）
   * @returns 更新后的用户
   */
  update(id: number, updateUserDto: UpdateUserDto, tenantId?: string): Promise<any>;

  /**
   * 删除用户
   * @param id 用户ID
   * @param tenantId 租户ID（可选）
   * @returns 删除结果
   */
  remove(id: number, tenantId?: string): Promise<any>;

  /**
   * 验证用户密码
   * @param id 用户ID
   * @param password 密码
   * @param tenantId 租户ID（可选）
   * @returns 密码是否正确
   */
  validatePassword(id: number, password: string, tenantId?: string): Promise<boolean>;

  /**
   * 更新用户密码
   * @param id 用户ID
   * @param newPassword 新密码
   * @param tenantId 租户ID（可选）
   * @returns 更新结果
   */
  updatePassword(id: number, newPassword: string, tenantId?: string): Promise<any>;
}
