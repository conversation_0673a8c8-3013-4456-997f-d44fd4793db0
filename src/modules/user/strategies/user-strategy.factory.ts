/*
 * @Author: ya<PERSON><PERSON>qi<PERSON> <EMAIL>
 * @Date: 2025-05-08 14:16:44
 * @LastEditors: yangwenqiang <EMAIL>
 * @LastEditTime: 2025-06-01 00:52:04
 * @FilePath: /multi-tenant-nestjs/src/modules/user/strategies/user-strategy.factory.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Injectable } from '@nestjs/common';

import { IUserStrategy } from './user-strategy.interface';

import { BaseStrategyFactory } from '@/core/strategies/base-strategy.factory';

/**
 * 用户策略类型
 */
export enum UserStrategyType {
  SYSTEM = 'SYSTEM',
  TENANT = 'TENANT',
}

/**
 * 用户策略工厂
 * 用于创建和管理用户策略实例
 */
@Injectable()
export class UserStrategyFactory extends BaseStrategyFactory<IUserStrategy> {
  /**
   * 根据用户类型获取策略
   * @param userType 用户类型
   * @returns 用户策略实例
   */
  getStrategyByUserType(userType: string): IUserStrategy {
    // 将用户类型转换为策略类型
    let strategyType: string;

    if (userType === 'SYSTEM')  {
      strategyType = UserStrategyType.SYSTEM;
    } else if (userType === 'TENANT')  {
      strategyType = UserStrategyType.TENANT;
    } else {
      // 如果用户类型不是 'SYSTEM' 或 'TENANT'，则默认使用 'SYSTEM'
      strategyType = UserStrategyType.SYSTEM;
    }

    // 获取策略
    const strategy = this.getStrategy(strategyType);

    if (!strategy)  {
      // 记录更详细的错误信息
      console.error(`Strategy not found for type: ${strategyType}, userType: ${userType}`);
      console.error('Available strategies:', Array.from(this['strategies'].keys()));
      throw new Error(`Strategy not found for type: ${strategyType}`);
    }

    return strategy;
  }
}
