import { Modu<PERSON> } from '@nestjs/common';

import { SystemUserStrategy } from './strategies/system-user.strategy';
import { TenantUserStrategy } from './strategies/tenant-user.strategy';
import { UserStrategyFactory } from './strategies/user-strategy.factory';
import { UserController } from './user.controller';
import { UserService } from './user.service';

import { CommonModule } from '@/core/common/common.module';
import { PrismaModule } from '@/core/database/prisma/prisma.module';

@Module({
  imports: [PrismaModule, CommonModule],
  controllers: [UserController],
  providers: [UserService, UserStrategyFactory, SystemUserStrategy, TenantUserStrategy],
)
export class UserModule {}
