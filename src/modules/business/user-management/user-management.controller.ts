import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { UserManagementService } from './user-management.service';

import { JwtAuthGuard } from '@/core/auth/guards/jwt-auth.guard';
import { TenantContext } from '@/core/decorators/tenant-context.decorator';

/**
 * 业务功能 - 用户管理控制器
 *
 * 提供租户内的用户管理功能：
 * - 用户CRUD操作
 * - 用户查询和分页
 * - 密码管理
 * - 租户隔离保障
 */
@ApiTags('业务功能 - 用户管理')
@Controller('api/users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class BusinessUserController {
  constructor(private readonly userService: UserManagementService) {}

  @Get()
  @ApiOperation({
    summary: '获取用户列表',
    description: '获取当前租户内的用户列表，支持分页和搜索',
  )
  @ApiQuery({ name: 'page', required: false, description: '页码');
  @ApiQuery({ name: 'limit', required: false, description: '每页数量');
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词');
  @ApiQuery({ name: 'userType', required: false, description: '用户类型');
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功获取用户列表',
  )
  async findAll(@TenantContext('tenantId') tenantId: number, @Query() query: any) {
    return this.userService.findAllByTenant(tenantId, query);

  @Get('stats')
  @ApiOperation({
    summary: '获取用户统计',
    description: '获取当前租户的用户统计信息',
  )
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功获取用户统计',
  )
  async getStatistics(@TenantContext('tenantId') tenantId: number) {
    return this.userService.getStatistics(tenantId);

  @Get(':id')
  @ApiOperation({
    summary: '获取用户详情',
    description: '根据ID获取用户详细信息',
  )
  @ApiParam({ name: 'id', description: '用户ID');
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功获取用户详情',
  )
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '用户不存在',
  )
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @TenantContext('tenantId') tenantId: number,
  ) {
    return this.userService.findOne(id, tenantId);

  @Post()
  @ApiOperation({
    summary: '创建用户',
    description: '在当前租户内创建新用户',
  )
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '成功创建用户',
  )
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误或用户名已存在',
  )
  async create(@TenantContext('tenantId') tenantId: number, @Body() createUserDto: any) {
    return this.userService.create(tenantId, createUserDto);

  @Put(':id')
  @ApiOperation({
    summary: '更新用户',
    description: '更新用户基本信息',
  )
  @ApiParam({ name: 'id', description: '用户ID');
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功更新用户',
  )
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '用户不存在',
  )
  async update(
    @Param('id', ParseIntPipe) id: number,
    @TenantContext('tenantId') tenantId: number,
    @Body() updateUserDto: any,
  ) {
    return this.userService.update(id, tenantId, updateUserDto);

  @Delete(':id')
  @ApiOperation({
    summary: '删除用户',
    description: '删除指定用户',
  )
  @ApiParam({ name: 'id', description: '用户ID');
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功删除用户',
  )
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '用户不存在',
  )
  async remove(@Param('id', ParseIntPipe) id: number, @TenantContext('tenantId') tenantId: number) {
    return this.userService.remove(id, tenantId);

  @Put(':id/password')
  @ApiOperation({
    summary: '更新用户密码',
    description: '更新用户密码',
  )
  @ApiParam({ name: 'id', description: '用户ID');
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功更新密码',
  )
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '原密码不正确',
  )
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '用户不存在',
  )
  async updatePassword(
    @Param('id', ParseIntPipe) id: number,
    @TenantContext('tenantId') tenantId: number,
    @Body() passwordDto: { oldPassword: string; newPassword: string },
  ) {
    return this.userService.updatePassword(id, tenantId, passwordDto);