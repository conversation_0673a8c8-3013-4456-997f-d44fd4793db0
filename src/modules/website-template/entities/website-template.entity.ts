import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsObject, IsDate } from 'class-validator';

export class WebsiteTemplateEntity {
  @ApiProperty({ description: '模板ID');
  id: number;

  @ApiProperty({ description: '模板名称');
  @IsString()
  name: string;

  @ApiProperty({ description: '模板状态', default: 'active');
  @IsString()
  status: string;

  @ApiProperty({ description: '元数据', type: 'object', required: false 
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '租户ID', required: false 
  @IsOptional()
  tenantId?: number;

  @ApiProperty({ description: '创建时间');
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({ description: '更新时间');
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;
}