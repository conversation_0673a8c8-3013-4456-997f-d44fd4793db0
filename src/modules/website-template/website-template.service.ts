import { Injectable, NotFoundException, ConflictException, Inject } from '@nestjs/common';

import { CreateWebsiteTemplateDto } from './dto/create-website-template.dto';
import { QueryWebsiteTemplateDto } from './dto/query-website-template.dto';
import { UpdateWebsiteTemplateDto } from './dto/update-website-template.dto';
// import any // WebsiteTemplateEntity not exist - table does not exist

import { DatabaseFactory } from '@/core/database/database.factory';
import { BaseBusinessService } from '@/modules/business/base/business.service';

@Injectable()
export class WebsiteTemplateService extends BaseBusinessService {
  constructor(databaseFactory: DatabaseFactory) {
    super(databaseFactory, 'WebsiteTemplateService');
  async create(
    tenantId: number,
    createDto: CreateWebsiteTemplateDto,
  ): Promise<any> {
    // 检查模板名称是否已存在
    const existingTemplate = await this.tenantDb.website.findFirst({
      where: {
        name: createDto.name,
        // tenantId // not in schema
      },
    )
    if (existingTemplate)  {
      throw new ConflictException(`模板名称 "${createDto.name}" 已存在`);
    const template = await this.tenantDb.website.create({
      data: {
        ...createDto,
        // tenantId // not in schema
      },
    )
    return template;
  async findAll(tenantId: number, queryDto: QueryWebsiteTemplateDto) {
    const {
      page = 1,
      limit = 10,
      keyword,
      category,
      industry,
      isPremium,
      isActive,
      sortBy = 'sortOrder',
      sortOrder = 'asc',
    } = queryDto;

    const skip = (page - 1) * limit;
    const take = limit;

    // 构建查询条件
    const where: any = {
      // tenantId // not in schema
    };

    if (keyword)  {
      where.OR = [
        { name: { contains: keyword, mode: 'insensitive' } },
        { description: { contains: keyword, mode: 'insensitive' } },
      ];
    if (category)  {
      where.category = category;
    if (industry)  {
      where.industry = industry;
    if (isPremium !== undefined)  {
      where.isPremium = isPremium;
    if (isActive !== undefined)  {
      where.isActive = isActive;
    // 构建排序条件
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    const [templates, total] = await Promise.all([
      this.tenantDb.website.findMany({
        where,
        skip,
        take,
        orderBy,
      ),
      this.tenantDb.website.count({ where ),
    ]);

    return {
      data: templates,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  async findOne(tenantId: number, id: number): Promise<any> {
    const template = await this.tenantDb.website.findFirst({
      where: {
        id,
        // tenantId // not in schema
      },
    )
    if (!template)  {
      throw new NotFoundException(`模板 ID ${id} 不存在`);
    return template;
  async update(
    tenantId: number,
    id: number,
    updateDto: UpdateWebsiteTemplateDto,
  ): Promise<any> {
    await this.findOne(tenantId, id); // 检查模板是否存在

    // 如果更新名称，检查新名称是否已被其他模板使用
    if (updateDto.name)  {
      const existingTemplate = await this.tenantDb.website.findFirst({
        where: {
          name: updateDto.name,
          // tenantId // not in schema
          NOT: {
            id,
          },
        },
      )
      if (existingTemplate)  {
        throw new ConflictException(`模板名称 "${updateDto.name}" 已被其他模板使用`);
    const template = await this.tenantDb.website.update({
      where: {
        id,
        // tenantId // not in schema
      },
      data: updateDto,
    )
    return template;
  async remove(tenantId: number, id: number): Promise<void> {
    await this.findOne(tenantId, id); // 检查模板是否存在

    // 检查是否有网站在使用此模板
    const websitesUsingTemplate = await this.tenantDb.website.count({
      where: {
        // templateId: // not in schema id,
        // tenantId // not in schema
      },
    )
    if (websitesUsingTemplate > 0)  {
      throw new ConflictException(
        `无法删除模板，还有 ${websitesUsingTemplate} 个网站正在使用此模板`, )
    await this.tenantDb.website.delete({
      where: {
        id,
        // tenantId // not in schema
      },
    )
  }

  async getCategories(tenantId: number): Promise<string[]> {
    const categories = await this.tenantDb.website.findMany({
      where: {
        // tenantId // not in schema
        // isActive: // not in schema true,
      },
      select: {
        // category: // not in schema true,
      },
      distinct: ['category'],
    )
    return categories.map(item => item.category )
  async getIndustries(tenantId: number): Promise<string[]> {
    const industries = await this.tenantDb.website.findMany({
      where: {
        // tenantId // not in schema
        // isActive: // not in schema true,
        // industry: // not in schema {
          not: null,
        },
      },
      select: {
        // industry: // not in schema true,
      },
      distinct: ['industry'],
    )
    return industries.map(item => item.industry).filter(Boolean ;
  async duplicate(tenantId: number, id: number, newName: string): Promise<any> {
    const originalTemplate = await this.findOne(tenantId, id);

    // 检查新名称是否已存在
    const existingTemplate = await this.tenantDb.website.findFirst({
      where: {
        name: newName,
        // tenantId // not in schema
      },
    )
    if (existingTemplate)  {
      throw new ConflictException(`模板名称 "${newName}" 已存在`);
    const duplicatedTemplate = await this.tenantDb.website.create({
      data: {
        name: newName,
        description: originalTemplate.description,
        // category: // not in schema originalTemplate.category,
        // industry: // not in schema originalTemplate.industry,
        thumbnail: originalTemplate.thumbnail,
        preview: originalTemplate.preview,
        config: originalTemplate.config,
        features: originalTemplate.features,
        isPremium: originalTemplate.isPremium,
        // isActive: // not in schema true,
        metadata: { sortOrder: 0,
        metadata: originalTemplate.metadata,
        // tenantId // not in schema
      },
    )
    return duplicatedTemplate;
  // TODO: 实现网站模板功能
  async getTemplates() {
    throw new Error('Method not implemented - Schema needs to be verified');