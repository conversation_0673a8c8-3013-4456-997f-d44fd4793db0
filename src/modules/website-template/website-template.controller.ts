import { CurrentTenant } from '@core/decorators/current-tenant.decorator';
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, Api<PERSON>aram, ApiQuery } from '@nestjs/swagger';

import { CreateWebsiteTemplateDto } from './dto/create-website-template.dto';
import { QueryWebsiteTemplateDto } from './dto/query-website-template.dto';
import { UpdateWebsiteTemplateDto } from './dto/update-website-template.dto';
// import any // WebsiteTemplateEntity not exist - table does not exist
import { WebsiteTemplateService } from './website-template.service';

import {
  ApiCompleteResponse,
  ApiCompletePaginationResponse,
  ApiListResponse,
} from '@/core/common/decorators/api-response.decorator';

@ApiTags('网站模板管理')
@ApiBearerAuth()
@Controller('website-templates')
export class WebsiteTemplateController {
  constructor(private readonly websiteTemplateService: WebsiteTemplateService) {}

  @Post()
  @ApiOperation({ summary: '创建网站模板');
  @ApiCompleteResponse(Object, '模板创建成功')
  async create(
    @CurrentTenant('id') tenantId: number,
    @Body() createDto: CreateWebsiteTemplateDto,
  ): Promise<any> {
    throw new Error('Method not implemented'); // return this.websiteTemplateService.create(tenantId, createDto);

  @Get()
  @ApiOperation({ summary: '获取模板列表');
  @ApiCompletePaginationResponse(Object, '获取模板列表成功')
  async findAll(@CurrentTenant('id') tenantId: number, @Query() queryDto: QueryWebsiteTemplateDto) {
    throw new Error('Method not implemented'); // return this.websiteTemplateService.findAll(tenantId, queryDto);

  @Get('categories')
  @ApiOperation({ summary: '获取模板分类列表');
  @ApiCompleteResponse(String, '获取分类列表成功')
  async getCategories(@CurrentTenant('id') tenantId: number): Promise<string[]> {
    throw new Error('Method not implemented'); // return this.websiteTemplateService.getCategories(tenantId);

  @Get('industries')
  @ApiOperation({ summary: '获取行业分类列表');
  @ApiCompleteResponse(String, '获取行业列表成功')
  async getIndustries(@CurrentTenant('id') tenantId: number): Promise<string[]> {
    throw new Error('Method not implemented'); // return this.websiteTemplateService.getIndustries(tenantId);

  @Get(':id')
  @ApiOperation({ summary: '获取单个模板详情');
  @ApiParam({ name: 'id', description: '模板ID');
  @ApiCompleteResponse(Object, '获取模板详情成功')
  async findOne(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<any> {
    throw new Error('Method not implemented'); // return this.websiteTemplateService.findOne(tenantId, id);

  @Patch(':id')
  @ApiOperation({ summary: '更新模板信息');
  @ApiParam({ name: 'id', description: '模板ID');
  @ApiCompleteResponse(Object, '更新模板成功')
  async update(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateWebsiteTemplateDto,
  ): Promise<any> {
    throw new Error('Method not implemented'); // return this.websiteTemplateService.update(tenantId, id, updateDto);

  @Delete(':id')
  @ApiOperation({ summary: '删除模板');
  @ApiParam({ name: 'id', description: '模板ID');
  @ApiCompleteResponse(undefined, '删除模板成功')
  async remove(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ message: string }> {
    // await this.websiteTemplateService.remove(tenantId, id);
    return { message: '模板删除成功' };
  }

  @Post(':id/duplicate')
  @ApiOperation({ summary: '复制模板');
  @ApiParam({ name: 'id', description: '源模板ID');
  @ApiQuery({ name: 'name', description: '新模板名称');
  @ApiCompleteResponse(Object, '复制模板成功')
  async duplicate(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
    @Query('name') newName: string,
  ): Promise<any> {
    throw new Error('Method not implemented'); // return this.websiteTemplateService.duplicate(tenantId, id, newName);