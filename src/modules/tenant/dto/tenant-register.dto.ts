import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  MinLength,
  IsEnum,
  IsObject,
  ValidateNested,
} from 'class-validator';

export enum SubscriptionPlanType {
  FREE = 'free',
  BASIC = 'basic',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise',
}

export class CompanyInfoDto {
  @ApiProperty({ description: '公司名称');
  @IsString()
  @IsNotEmpty()
  companyName: string;

  @ApiProperty({ description: '公司规模', required: false 
  @IsOptional()
  @IsString()
  companySize?: string;

  @ApiProperty({ description: '行业类型', required: false 
  @IsOptional()
  @IsString()
  industry?: string;

  @ApiProperty({ description: '公司网站', required: false 
  @IsOptional()
  @IsString()
  website?: string;
}

export class AdminUserDto {
  @ApiProperty({ description: '管理员用户名');
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  username: string;

  @ApiProperty({ description: '管理员密码');
  @IsString()
  @MinLength(8)
  password: string;

  @ApiProperty({ description: '管理员邮箱');
  @IsEmail()
  email: string;

  @ApiProperty({ description: '管理员真实姓名');
  @IsString()
  @IsNotEmpty()
  realName: string;

  @ApiProperty({ description: '手机号', required: false 
  @IsOptional()
  @IsString()
  phoneNumber?: string;
}

export class TenantRegisterDto {
  @ApiProperty({ description: '租户代码（唯一标识）');
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  tenantCode: string;

  @ApiProperty({ description: '租户名称');
  @IsString()
  @IsNotEmpty()
  tenantName: string;

  @ApiProperty({ description: '自定义域名（可选）', required: false 
  @IsOptional()
  @IsString()
  customDomain?: string;

  @ApiProperty({ description: '公司信息', type: CompanyInfoDto 
  @ValidateNested()
  @Type(() => CompanyInfoDto)
  companyInfo: CompanyInfoDto;

  @ApiProperty({ description: '管理员用户信息', type: AdminUserDto 
  @ValidateNested()
  @Type(() => AdminUserDto)
  adminUser: AdminUserDto;

  @ApiProperty({ description: '订阅计划类型', enum: SubscriptionPlanType 
  @IsEnum(SubscriptionPlanType)
  subscriptionPlan: SubscriptionPlanType;

  @ApiProperty({ description: '扩展信息', required: false 
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiProperty({ description: '是否同意服务条款', default: false 
  @IsOptional()
  agreeToTerms?: boolean;
}

export class TenantRegisterResponseDto {
  @ApiProperty({ description: '注册是否成功');
  success: boolean;

  @ApiProperty({ description: '租户ID');
  tenantId: number;

  @ApiProperty({ description: '租户代码');
  tenantCode: string;

  @ApiProperty({ description: '访问URL');
  accessUrl: string;

  @ApiProperty({ description: '管理员登录令牌');
  adminToken: string;

  @ApiProperty({ description: '下一步操作提示');
  nextSteps: string[];
}

export class TenantVerifyDto {
  @ApiProperty({ description: '租户代码');
  @IsString()
  @IsNotEmpty()
  tenantCode: string;

  @ApiProperty({ description: '验证码');
  @IsString()
  @IsNotEmpty()
  verificationCode: string;
}
