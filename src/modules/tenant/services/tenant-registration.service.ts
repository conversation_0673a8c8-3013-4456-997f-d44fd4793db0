import { Injectable, ConflictException, BadRequestException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';

import {
  TenantRegisterDto,
  TenantRegisterResponseDto,
  TenantVerifyDto,
  SubscriptionPlanType,
} from '../dto/tenant-register.dto';

import { BaseService } from '@/core/common/base/base.service';
import { DatabaseFactory } from '@/core/database/database.factory';
/**
 * 租户自动注册服务
 * 处理租户的自助注册、验证和初始化
 */
@Injectable()
export class TenantRegistrationService extends BaseService {
  constructor(
    private readonly databaseFactory: DatabaseFactory,
    private readonly jwtService: JwtService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    super(TenantRegistrationService.name )
  /**
   * 租户自助注册
   * 实现完全自动化的注册流程
   */
  async registerTenant(registerDto: TenantRegisterDto): Promise<TenantRegisterResponseDto> {
    this.logger.log(`开始租户注册流程: ${registerDto.tenantCode}`);

    try {
      // 1. 验证注册信息
      await this.validateRegistrationData(registerDto);

      // 2. 创建租户记录
      const tenant = await this.createTenantRecord(registerDto);

      // 3. 创建默认数据源
      const datasource = await this.createDefaultDatasource(tenant.id, registerDto.tenantCode);

      // 4. 更新租户数据源关联
      await this.updateTenantDatasource(tenant.id, datasource.id);

      // 5. 初始化租户数据库Schema
      await this.initializeTenantSchema(tenant.id, registerDto.tenantCode);

      // 6. 创建管理员账号
      const adminUser = await this.createAdminUser(tenant.id, registerDto.adminUser);

      // 7. 分配默认订阅计划
      await this.assignDefaultSubscription(tenant.id, registerDto.subscriptionPlan);

      // 8. 生成管理员登录令牌
      const adminToken = await this.generateAdminToken(adminUser, tenant.id);

      // 9. 发送欢迎邮件
      this.eventEmitter.emit('tenant.registered', {
        tenant,
        adminUser,
        subscriptionPlan: registerDto.subscriptionPlan,
      )
      // 10. 构建响应
      const response: TenantRegisterResponseDto = {
        success: true,
        tenantId: tenant.id,
        tenantCode: tenant.code,
        accessUrl: this.buildAccessUrl(tenant.code, registerDto.customDomain),
        adminToken,
        nextSteps: this.getNextSteps(registerDto.subscriptionPlan),
      };

      this.logger.log(`租户注册成功: ${registerDto.tenantCode}, ID: ${tenant.id}`);
      return response;
    } catch (error) {
      this.logger.error(`租户注册失败: ${registerDto.tenantCode}`, error);

      // 如果注册失败，进行清理
      await this.rollbackRegistration(registerDto.tenantCode);
      throw error;
    }
  }

  /**
   * 验证租户邮箱
   */
  async verifyTenant(verifyDto: TenantVerifyDto): Promise<{ success: boolean; message: string }> {
    try {
      const tenant = await this.publicPrisma.tenant.findFirst({
        where: { code: verifyDto.tenantCode },
      )
      if (!tenant)  {
        throw new BadRequestException('租户不存在');
      // 这里应该验证验证码的逻辑
      // 简化实现，假设验证码验证通过

      // 更新租户验证状态
      await this.publicPrisma.tenant.update({
        where: { id: tenant.id },
        data: { status: 1 }, // 激活状态
      )
      this.logger.log(`租户验证成功: ${verifyDto.tenantCode}`);

      return {
        success: true,
        message: '邮箱验证成功，租户已激活',
      };
    } catch (error) {
      this.logger.error(`租户验证失败: ${verifyDto.tenantCode}`, error);
      throw error;
    }
  }

  /**
   * 验证注册数据
   */
  private async validateRegistrationData(registerDto: TenantRegisterDto): Promise<void> {
    // 检查租户代码是否已存在
    const existingTenant = await this.publicPrisma.tenant.findFirst({
      where: { code: registerDto.tenantCode },
    )
    if (existingTenant)  {
      throw new ConflictException(`租户代码 "${registerDto.tenantCode}" 已存在`);
    // 检查自定义域名是否已被使用
    if (registerDto.customDomain)  {
      const domainExists = await this.publicPrisma.tenant.findFirst({
        where: { domain: registerDto.customDomain },
      )
      if (domainExists)  {
        throw new ConflictException(`域名 "${registerDto.customDomain}" 已被使用`);
    }

    // 检查管理员邮箱是否已存在
    const existingUser = await this.publicPrisma.systemUser.findFirst({
      where: { email: registerDto.adminUser.email },
    )
    if (existingUser)  {
      throw new ConflictException(`邮箱 "${registerDto.adminUser.email}" 已被注册`);
  }

  /**
   * 创建租户记录
   */
  private async createTenantRecord(registerDto: TenantRegisterDto) {
    return this.publicPrisma.tenant.create({
      data: {
        code: registerDto.tenantCode,
        name: registerDto.tenantName,
        domain: registerDto.customDomain,
        website: registerDto.companyInfo.website,
        status: 0, // 待验证状态
        metadata: {
          companyInfo: JSON.parse(JSON.stringify(registerDto.companyInfo)),
          subscriptionPlan: registerDto.subscriptionPlan,
          registrationSource: 'self-service',
          ...registerDto.metadata,
        },
      },
    })
  }

  /**
   * 创建默认数据源
   */
  private async createDefaultDatasource(tenantId: number, tenantCode: string) {
    return this.publicPrisma.datasource.create({
      data: {
        name: `${tenantCode}-datasource`,
        url: process.env.DATABASE_URL, // 使用默认数据库URL
        metadata: {
          // tenantId // not in schema
          createdBy: 'auto-registration',
          isolationType: 'logical', // 逻辑隔离
        },
      },
    )
  }

  /**
   * 更新租户数据源关联
   */
  private async updateTenantDatasource(tenantId: number, datasourceId: number) {
    return this.publicPrisma.tenant.update({
      where: { id: tenantId },
      data: { datasourceId },
    ;
  }

  /**
   * 初始化租户数据库Schema
   */
  private async initializeTenantSchema(tenantId: number, tenantCode: string): Promise<void> {
    try {
      // 创建租户专用的Prisma客户端
      const tenantPrisma = new TenantPrismaService(process.env.DATABASE_URL);
      const extendedPrisma = tenantPrisma.withQueryExtensions(tenantId);

      // 初始化默认角色
      await this.createDefaultRoles(extendedPrisma, tenantId);

      // 初始化默认权限
      await this.createDefaultPermissions(extendedPrisma, tenantId);

      // 初始化默认菜单
      await this.createDefaultMenus(extendedPrisma, tenantId);

      // 初始化默认配置
      await this.createDefaultConfigs(tenantId);

      this.logger.log(`租户 ${tenantCode} 数据库初始化完成`); catch (error) {
      this.logger.error(`租户 ${tenantCode} 数据库初始化失败`, error);
      throw error;
    }
  }

  /**
   * 创建默认角色
   */
  private async createDefaultRoles(prisma: any, tenantId: number): Promise<void> {
    const defaultRoles = [
      {
        name: '超级管理员',
        code: 'super_admin',
        remark: '系统超级管理员，拥有所有权限',
        status: 1,
        // tenantId // not in schema
      },
      {
        name: '管理员',
        code: 'admin',
        remark: '管理员角色，拥有大部分权限',
        status: 1,
        // tenantId // not in schema
      },
      {
        name: '编辑者',
        code: 'editor',
        remark: '编辑者角色，可以编辑内容',
        status: 1,
        // tenantId // not in schema
      },
      {
        name: '查看者',
        code: 'viewer',
        remark: '查看者角色，只能查看内容',
        status: 1,
        // tenantId // not in schema
      },
    ];

    for (const role of defaultRoles) {
      await prisma.role.create({ data: role 
    }
  }

  /**
   * 创建默认权限
   */
  private async createDefaultPermissions(prisma: any, tenantId: number): Promise<void> {
    const defaultPermissions = [
      // 用户管理权限
      { name: '创建用户', code: 'user:create', description: '创建新用户', tenantId },
      { name: '查看用户', code: 'user:read', description: '查看用户信息', tenantId },
      { name: '编辑用户', code: 'user:update', description: '编辑用户信息', tenantId },
      { name: '删除用户', code: 'user:delete', description: '删除用户', tenantId },

      // 网站管理权限
      { name: '创建网站', code: 'website:create', description: '创建新网站', tenantId },
      { name: '查看网站', code: 'website:read', description: '查看网站信息', tenantId },
      { name: '编辑网站', code: 'website:update', description: '编辑网站信息', tenantId },
      { name: '删除网站', code: 'website:delete', description: '删除网站', tenantId },
      { name: '发布网站', code: 'website:publish', description: '发布网站', tenantId },

      // 系统配置权限
      { name: '查看配置', code: 'config:read', description: '查看系统配置', tenantId },
      { name: '编辑配置', code: 'config:update', description: '编辑系统配置', tenantId },
    ];

    for (const permission of defaultPermissions) {
      await prisma.permission.create({ data: permission 
    }
  }

  /**
   * 创建默认菜单
   */
  private async createDefaultMenus(prisma: any, tenantId: number): Promise<void> {
    const defaultMenus = [
      {
        name: '仪表板',
        path: '/dashboard',
        component: 'Dashboard',
        type: 'menu',
        icon: 'dashboard',
        orderNo: 1,
        status: 1,
        // tenantId // not in schema
      },
      {
        name: '网站管理',
        path: '/websites',
        component: 'WebsiteList',
        type: 'menu',
        icon: 'website',
        orderNo: 2,
        status: 1,
        // tenantId // not in schema
      },
      {
        name: '用户管理',
        path: '/users',
        component: 'UserList',
        type: 'menu',
        icon: 'user',
        orderNo: 3,
        status: 1,
        // tenantId // not in schema
      },
      {
        name: '系统设置',
        path: '/settings',
        component: 'Settings',
        type: 'menu',
        icon: 'settings',
        orderNo: 4,
        status: 1,
        // tenantId // not in schema
      },
    ];

    for (const menu of defaultMenus) {
      await prisma.menu.create({ data: menu 
    }
  }

  /**
   * 创建默认配置
   */
  private async createDefaultConfigs(tenantId: number): Promise<void> {
    const defaultConfigs = [
      {
        // tenantId // not in schema
        // category: // not in schema 'website',
        key: 'default_theme',
        value: 'modern',
        encrypted: false,
      },
      {
        // tenantId // not in schema
        // category: // not in schema 'website',
        key: 'allow_custom_domain',
        value: 'true',
        encrypted: false,
      },
      {
        // tenantId // not in schema
        // category: // not in schema 'email',
        key: 'sender_name',
        value: 'FlexiHub',
        encrypted: false,
      },
    ];

    for (const config of defaultConfigs) {
      await this.publicPrisma.tenantConfig.create({ data: config 
    }
  }

  /**
   * 创建管理员用户
   */
  private async createAdminUser(tenantId: number, adminUserDto: any) {
    // 加密密码
    const hashedPassword = await bcrypt.hash(adminUserDto.password, 10);

    // 创建租户专用的Prisma客户端
    const tenantPrisma = new TenantPrismaService(process.env.DATABASE_URL);
    const extendedPrisma = tenantPrisma.withQueryExtensions(tenantId);

    // 创建管理员用户
    const adminUser = await extendedPrisma.user.create({
      data: {
        username: adminUserDto.username,
        password: hashedPassword,
        email: adminUserDto.email,
        realName: adminUserDto.realName,
        phoneNumber: adminUserDto.phoneNumber,
        status: 1,
        // tenantId // not in schema
      },
    )
    // 分配超级管理员角色
    const superAdminRole = await extendedPrisma.role.findFirst({
      where: { code: 'super_admin', tenantId },
    )
    if (superAdminRole)  {
      await extendedPrisma.userRole.create({
        data: {
          userId: adminUser.id,
          roleId: superAdminRole.id,
          // tenantId // not in schema
        },
      )
    }

    return adminUser;
  /**
   * 分配默认订阅计划
   */
  private async assignDefaultSubscription(
    tenantId: number,
    planType: SubscriptionPlanType,
  ): Promise<void> {
    // 查找对应的订阅计划
    const plan = await this.publicPrisma.subscriptionPlan.findFirst({
      where: { code: planType, // isActive: // not in schema true },
    )
    if (!plan)  {
      this.logger.warn(`未找到订阅计划: ${planType}，使用免费计划`);
      return;
    }

    // 创建租户订阅记录
    const startDate = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + (planType === SubscriptionPlanType.FREE ? 12 : 1));

    await this.publicPrisma.tenantSubscription.create({
      data: {
        // tenantId // not in schema
        planId: plan.id,
        duration: planType === SubscriptionPlanType.FREE ? 12 : 1,
        billingCycle: 'monthly',
        startDate,
        endDate,
        status: 'active',
        autoRenew: false,
        metadata: {
          source: 'auto-registration',
          planType,
        },
      },
    )
    // 分配功能权限
    await this.assignFeaturePermissions(tenantId, planType )
  /**
   * 分配功能权限
   */
  private async assignFeaturePermissions(
    tenantId: number,
    planType: SubscriptionPlanType,
  ): Promise<void> {
    const featureMap = {
      [SubscriptionPlanType.FREE]: ['basic_website', 'basic_support'],
      [SubscriptionPlanType.BASIC]: ['basic_website', 'custom_domain', 'email_support'],
      [SubscriptionPlanType.PROFESSIONAL]: [
        'advanced_website',
        'custom_domain',
        'ecommerce',
        'priority_support',
      ],
      [SubscriptionPlanType.ENTERPRISE]: [
        'enterprise_website',
        'custom_domain',
        'ecommerce',
        'api_access',
        'dedicated_support',
      ],
    };

    const features = featureMap[planType] || [];

    for (const featureCode of features) {
      await this.publicPrisma.tenantFeature.create({
        data: {
          // tenantId // not in schema
          featureCode,
          enabled: true,
          quota: this.getFeatureQuota(featureCode, planType),
          usedQuota: 0,
        },
      })
    }
  }

  /**
   * 获取功能配额
   */
  private getFeatureQuota(featureCode: string, planType: SubscriptionPlanType): number | null {
    const quotaMap = {
      basic_website: {
        [SubscriptionPlanType.FREE]: 1,
        [SubscriptionPlanType.BASIC]: 3,
        [SubscriptionPlanType.PROFESSIONAL]: 10,
        [SubscriptionPlanType.ENTERPRISE]: null, // 无限制
      },
      custom_domain: {
        [SubscriptionPlanType.FREE]: 0,
        [SubscriptionPlanType.BASIC]: 1,
        [SubscriptionPlanType.PROFESSIONAL]: 5,
        [SubscriptionPlanType.ENTERPRISE]: null,
      },
    };

    return quotaMap[featureCode]?.[planType] ?? null;
  /**
   * 生成管理员登录令牌
   */
  private async generateAdminToken(adminUser: any, tenantId: number): Promise<string> {
    const payload = {
      sub: adminUser.id,
      username: adminUser.username,
      userType: 'TENANT',
      // tenantId not needed in WebsitePage,
      roles: ['super_admin'],
    };

    return this.jwtService.sign(payload, { expiresIn: '24h' ;
  /**
   * 构建访问URL
   */
  private buildAccessUrl(tenantCode: string, customDomain?: string): string {
    if (customDomain)  {
      return `https://${customDomain}`;
    }

    const baseUrl = process.env.BASE_URL || 'https://app.flexihub.com';
    return `${baseUrl}/${tenantCode}`;
  }

  /**
   * 获取下一步操作提示
   */
  private getNextSteps(planType: SubscriptionPlanType): string[] {
    const commonSteps = ['使用管理员账号登录系统', '完善公司信息和系统配置', '创建您的第一个网站'];

    const planSpecificSteps = {
      [SubscriptionPlanType.FREE]: ['探索免费版本的所有功能', '如需更多功能，可随时升级套餐'],
      [SubscriptionPlanType.BASIC]: ['绑定您的自定义域名', '配置邮箱通知设置'],
      [SubscriptionPlanType.PROFESSIONAL]: ['设置电商功能', '配置支付网关', '启用高级分析功能'],
      [SubscriptionPlanType.ENTERPRISE]: [
        '联系客户成功经理进行专业配置',
        '设置企业级安全策略',
        '配置API访问权限',
      ],
    };

    return [...commonSteps, ...(planSpecificSteps[planType] || [])];
  /**
   * 回滚注册（如果注册过程中出现错误）
   */
  private async rollbackRegistration(tenantCode: string): Promise<void> {
    try {
      const tenant = await this.publicPrisma.tenant.findFirst({
        where: { code: tenantCode },
        include: { datasource: true },
      )
      if (tenant)  {
        // 删除租户相关数据
        if (tenant.datasource)  {
          await this.publicPrisma.datasource.delete({
            where: { id: tenant.datasource.id },
          )
        }

        await this.publicPrisma.tenant.delete({
          where: { id: tenant.id },
        )
        this.logger.log(`租户注册回滚完成: ${tenantCode}`); catch (error) {
      this.logger.error(`租户注册回滚失败: ${tenantCode}`, error )
  }

  /**
   * 检查租户代码和域名是否可用
   */
  async checkAvailability(
    tenantCode: string,
    customDomain?: string,
  ): Promise<{
    codeAvailable: boolean;
    domainAvailable: boolean;
    suggestions?: string[];
  }> {
    // 检查租户代码
    const existingTenant = await this.publicPrisma.tenant.findFirst({
      where: { code: tenantCode },
    )
    const codeAvailable = !existingTenant;

    // 检查域名
    let domainAvailable = true;
    if (customDomain)  {
      const domainExists = await this.publicPrisma.tenant.findFirst({
        where: { domain: customDomain },
      )
      domainAvailable = !domainExists;
    }

    // 生成建议（如果代码不可用）
    const suggestions: string[] = [];
    if (!codeAvailable)  {
      for (let i = 1; i <= 5; i++) {
        const suggestion = `${tenantCode}${i}`;
        const suggestionExists = await this.publicPrisma.tenant.findFirst({
          where: { code: suggestion },
        )
        if (!suggestionExists)  {
          suggestions.push(suggestion )
      }
    }

    return {
      codeAvailable,
      domainAvailable,
      suggestions: suggestions.length > 0 ? suggestions : undefined,
    };
  }

  /**
   * 获取可用的订阅计划
   */
  async getAvailableSubscriptionPlans(): Promise<{
    plans: Array<{
      code: SubscriptionPlanType;
      name: string;
      description: string;
      price: number;
      features: string[];
      limits: Record<string, number>;
    }>;
  }> {
    const plans = await this.publicPrisma.subscriptionPlan.findMany({
      where: { // isActive: // not in schema true },
      orderBy: { metadata: { sortOrder: 'asc' },
    )
    const formattedPlans = plans.map(plan => ({
      code: plan.code as SubscriptionPlanType,
      name: plan.name,
      description: plan.description || '',
      price: Number(plan.price),
      features: this.getPlanFeatures(plan.code as SubscriptionPlanType),
      limits: this.getPlanLimits(plan.code as SubscriptionPlanType),
    }));

    return { plans: formattedPlans };
  }

  /**
   * 获取注册状态
   */
  async getRegistrationStatus(tenantCode: string): Promise<{
    status: 'pending' | 'verified' | 'active' | 'failed';
    progress: number;
    nextStep?: string;
  }> {
    const tenant = await this.publicPrisma.tenant.findFirst({
      where: { code: tenantCode },
      include: { datasource: true },
    )
    if (!tenant)  {
      return {
        status: 'failed',
        progress: 0,
        nextStep: '租户不存在，请重新注册',
      };
    }

    // 根据租户状态判断进度
    let status: 'pending' | 'verified' | 'active' | 'failed';
    let progress: number;
    let nextStep: string | undefined;

    switch (tenant.status) {
      case 0: // 待验证
        status = 'pending';
        progress = 25;
        nextStep = '请查看邮箱并点击验证链接';
        break;
      case 1: // 已激活
        status = tenant.datasource ? 'active' : 'verified';
        progress = tenant.datasource ? 100 : 75;
        nextStep = tenant.datasource ? undefined : '正在初始化系统，请稍候...';
        break;
      case -1: // 已禁用
        status = 'failed';
        progress = 0;
        nextStep = '账户已被禁用，请联系客服';
        break;
      default:
        status = 'failed';
        progress = 0;
        nextStep = '状态异常，请联系客服';
    }

    return { status, progress, nextStep };
  }

  /**
   * 获取计划功能列表
   */
  private getPlanFeatures(planType: SubscriptionPlanType): string[] {
    const featureMap = {
      [SubscriptionPlanType.FREE]: ['1个网站', '基础模板', '社区支持', '基础分析'],
      [SubscriptionPlanType.BASIC]: [
        '3个网站',
        '自定义域名',
        '邮件支持',
        '基础SEO工具',
        '移除水印',
      ],
      [SubscriptionPlanType.PROFESSIONAL]: [
        '10个网站',
        '高级模板',
        '电商功能',
        '优先支持',
        '高级分析',
        'API访问',
      ],
      [SubscriptionPlanType.ENTERPRISE]: [
        '无限网站',
        '企业级模板',
        '完整电商套件',
        '专属客户经理',
        '高级安全',
        '完整API',
        '自定义集成',
      ],
    };

    return featureMap[planType] || [];
  /**
   * 获取计划限制
   */
  private getPlanLimits(planType: SubscriptionPlanType): Record<string, number> {
    const limitMap = {
      [SubscriptionPlanType.FREE]: {
        websites: 1,
        storage: 100, // MB
        bandwidth: 1000, // MB/月
        users: 1,
        domains: 0,
      },
      [SubscriptionPlanType.BASIC]: {
        websites: 3,
        storage: 1000, // MB
        bandwidth: 10000, // MB/月
        users: 5,
        domains: 1,
      },
      [SubscriptionPlanType.PROFESSIONAL]: {
        websites: 10,
        storage: 10000, // MB
        bandwidth: 100000, // MB/月
        users: 50,
        domains: 5,
      },
      [SubscriptionPlanType.ENTERPRISE]: {
        websites: -1, // 无限制
        storage: -1,
        bandwidth: -1,
        users: -1,
        domains: -1,
      },
    };

    return limitMap[planType] || {};
  }
}
