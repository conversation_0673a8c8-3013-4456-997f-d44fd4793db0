
import { Injectable, Logger } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

import { TestConnectionDto } from './dto/test-connection.dto';

@Injectable()
export class DatasourceService {
  private readonly logger = new Logger(DatasourceService.name);

  /**
   * 测试数据源连接
   * @param testConnectionDto 测试连接DTO
   * @returns 测试结果
   */
  async testConnection(
    testConnectionDto: TestConnectionDto,
  ): Promise<{ success: boolean; message: string }> {
    const { url } = testConnectionDto;

    try {
      this.logger.log(`测试数据源连接: ${this.maskDatabaseUrl(url)}`);

      // 创建临时Prisma客户端
      const prisma = new PrismaClient({
        datasources: {
          db: {
            url,
          },
        },
      });

      // 尝试连接数据库
      await prisma.$connect();

      // 测试查询
      await prisma.$queryRaw`SELECT 1`;

      // 断开连接
      await prisma.$disconnect();

      this.logger.log('数据源连接测试成功');

      return {
        success: true,
        message: '连接成功',
      };
    } catch (error) {
      this.logger.error(`数据源连接测试失败: ${error.message}`);

      return {
        success: false,
        message: `连接失败: ${error.message}`,
      };
    }
  }

  /**
   * 掩盖数据库URL中的敏感信息
   * @param url 数据库URL
   * @returns 掩盖后的URL
   */
  private maskDatabaseUrl(url: string): string {
    try {
      // 使用正则表达式替换密码部分
      return url.replace(/(postgresql:\/\/[^:]+:)([^@]+)(@.+)/, '$1******$3');
    } catch (error) {
      return '无效的数据库URL';
    }
  }
}
