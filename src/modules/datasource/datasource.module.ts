import { Module } from '@nestjs/common';

import { DatasourceController } from './datasource.controller';
import { DatasourceService } from './datasource.service';

import { PrismaModule } from '@/core/database/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [DatasourceController],
  providers: [DatasourceService],
  exports: [DatasourceService],
)
export class DatasourceModule {}
