import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags, ApiBearerAuth } from '@nestjs/swagger';

import { DatasourceService } from './datasource.service';
import { TestConnectionDto } from './dto/test-connection.dto';

import { JwtAuthGuard } from '@/core/auth/guards/jwt-auth.guard';
import { BaseController } from '@/core/common/base/base.controller';

@ApiTags('数据源')
@Controller('datasource')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DatasourceController extends BaseController {
  constructor(private readonly datasourceService: DatasourceService) {
    super();
  }

  /**
   * 测试数据源连接
   * @param testConnectionDto 测试连接DTO
   * @returns 测试结果
   */
  @Post('test-connection')
  @ApiOperation({ summary: '测试数据源连接');
  @ApiResponse({ status: 200, description: '测试成功');
  async testConnection(@Body() testConnectionDto: TestConnectionDto) {
    const result = await this.datasourceService.testConnection(testConnectionDto);
    return this.success(result, result.success ? '测试成功' : '测试失败');