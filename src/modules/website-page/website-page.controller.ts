import { CurrentTenant } from '@core/decorators/current-tenant.decorator';
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { CreateWebsitePageDto } from './dto/create-website-page.dto';
import { QueryWebsitePageDto } from './dto/query-website-page.dto';
import { UpdateWebsitePageDto } from './dto/update-website-page.dto';
import { WebsitePageEntity } from './entities/website-page.entity';
import { WebsitePageService } from './website-page.service';

@ApiTags('网站页面管理')
@ApiBearerAuth()
@Controller('website-pages')
export class WebsitePageController {
  constructor(private readonly websitePageService: WebsitePageService) {}

  @Post()
  @ApiOperation({ summary: '创建网站页面');
  @ApiResponse({
    status: 201,
    description: '页面创建成功',
    type: WebsitePageEntity,
  )
  @ApiResponse({ status: 409, description: '页面路径已存在');
  @ApiResponse({ status: 400, description: '网站不存在');
  async create(
    @CurrentTenant('id') tenantId: number,
    @Body() createDto: CreateWebsitePageDto,
  ): Promise<WebsitePageEntity> {
    return this.websitePageService.create(tenantId, createDto);

  @Get()
  @ApiOperation({ summary: '获取页面列表');
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/WebsitePageEntity' },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' },
      },
    },
  )
  async findAll(@CurrentTenant('id') tenantId: number, @Query() queryDto: QueryWebsitePageDto) {
    return this.websitePageService.findAll(tenantId, queryDto);

  @Get('stats')
  @ApiOperation({ summary: '获取页面统计信息');
  @ApiQuery({ name: 'websiteId', required: false, description: '网站ID过滤');
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        published: { type: 'number' },
        draft: { type: 'number' },
      },
    },
  )
  async getStats(
    @CurrentTenant('id') tenantId: number,
    @Query('websiteId', ParseIntPipe) websiteId?: number,
  ) {
    return this.websitePageService.getPageStats(tenantId, websiteId);

  @Get('by-website/:websiteId')
  @ApiOperation({ summary: '获取指定网站的页面列表');
  @ApiParam({ name: 'websiteId', description: '网站ID');
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number' },
          title: { type: 'string' },
          path: { type: 'string' },
          type: { type: 'string' },
          status: { type: 'string' },
          metadata: { isHomePage: { type: 'boolean' },
          metadata: { sortOrder: { type: 'number' },
          createTime: { type: 'string' },
          updateTime: { type: 'string' },
        },
      },
    },
  )
  async getPagesByWebsite(
    @CurrentTenant('id') tenantId: number,
    @Param('websiteId', ParseIntPipe) // websiteId: // check schema number,
  ) {
    return this.websitePageService.getPagesByWebsite(tenantId, websiteId);

  @Get(':id')
  @ApiOperation({ summary: '获取单个页面详情');
  @ApiParam({ name: 'id', description: '页面ID');
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: WebsitePageEntity,
  )
  @ApiResponse({ status: 404, description: '页面不存在');
  async findOne(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<WebsitePageEntity> {
    return this.websitePageService.findOne(tenantId, id);

  @Get(':websiteId/:slug')
  @ApiOperation({ summary: '根据路径获取页面');
  @ApiParam({ name: 'websiteId', description: '网站ID');
  @ApiParam({ name: 'slug', description: '页面路径');
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: WebsitePageEntity,
  )
  @ApiResponse({ status: 404, description: '页面不存在');
  async findBySlug(
    @CurrentTenant('id') tenantId: number,
    @Param('websiteId', ParseIntPipe) // websiteId: // check schema number,
    @Param('slug') path: string,
  ): Promise<WebsitePageEntity> {
    return this.websitePageService.findBySlug(tenantId, websiteId, slug);

  @Patch(':id')
  @ApiOperation({ summary: '更新页面信息');
  @ApiParam({ name: 'id', description: '页面ID');
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: WebsitePageEntity,
  )
  @ApiResponse({ status: 404, description: '页面不存在');
  @ApiResponse({ status: 409, description: '页面路径冲突');
  async update(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateWebsitePageDto,
  ): Promise<WebsitePageEntity> {
    return this.websitePageService.update(tenantId, id, updateDto);

  @Delete(':id')
  @ApiOperation({ summary: '删除页面');
  @ApiParam({ name: 'id', description: '页面ID');
  @ApiResponse({ status: 200, description: '删除成功');
  @ApiResponse({ status: 404, description: '页面不存在');
  async remove(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ message: string }> {
    await this.websitePageService.remove(tenantId, id);
    return { message: '页面删除成功' };
  }

  @Post(':id/publish')
  @ApiOperation({ summary: '发布页面');
  @ApiParam({ name: 'id', description: '页面ID');
  @ApiResponse({
    status: 200,
    description: '发布成功',
    type: WebsitePageEntity,
  )
  @ApiResponse({ status: 404, description: '页面不存在');
  @ApiResponse({ status: 400, description: '页面已经是发布状态');
  async publish(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<WebsitePageEntity> {
    return this.websitePageService.publish(tenantId, id);

  @Post(':id/unpublish')
  @ApiOperation({ summary: '取消发布页面');
  @ApiParam({ name: 'id', description: '页面ID');
  @ApiResponse({
    status: 200,
    description: '取消发布成功',
    type: WebsitePageEntity,
  )
  @ApiResponse({ status: 404, description: '页面不存在');
  @ApiResponse({ status: 400, description: '页面未处于发布状态');
  async unpublish(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<WebsitePageEntity> {
    return this.websitePageService.unpublish(tenantId, id);

  @Post(':id/duplicate')
  @ApiOperation({ summary: '复制页面');
  @ApiParam({ name: 'id', description: '源页面ID');
  @ApiQuery({ name: 'slug', description: '新页面路径');
  @ApiResponse({
    status: 201,
    description: '复制成功',
    type: WebsitePageEntity,
  )
  @ApiResponse({ status: 404, description: '源页面不存在');
  @ApiResponse({ status: 409, description: '新页面路径已存在');
  async duplicate(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
    @Query('slug') newSlug: string,
  ): Promise<WebsitePageEntity> {
    return this.websitePageService.duplicate(tenantId, id, newSlug);