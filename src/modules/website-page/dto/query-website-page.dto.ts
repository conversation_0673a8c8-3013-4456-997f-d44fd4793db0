import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString, IsInt, IsBoolean, Min, Max } from 'class-validator';

export class QueryWebsitePageDto {
  @ApiProperty({ description: '页码', default: 1, required: false 
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({ description: '每页数量', default: 10, required: false 
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({ description: '关键词搜索', required: false 
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({
    description: '页面状态过滤',
    enum: ['draft', 'published'],
    required: false,
  )
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({
    description: '页面类型过滤',
    examples: ['page', 'home', 'about', 'contact', 'blog'],
    required: false,
  )
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({ description: '所属网站ID过滤', required: false 
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  websiteId?: number;

  @ApiProperty({ description: '是否只显示首页', required: false 
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isHomePage?: boolean;

  @ApiProperty({
    description: '排序字段',
    enum: ['createTime', 'updateTime', 'title', 'sortOrder'],
    required: false,
  )
  @IsOptional()
  @IsString()
  sortBy?: string = 'sortOrder';

  @ApiProperty({
    description: '排序方向',
    enum: ['asc', 'desc'],
    required: false,
  )
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'asc';

  @ApiProperty({ description: '是否包含关联数据', required: false 
  @IsOptional()
  @Type(() => Boolean)
  includeRelations?: boolean = false;
}
