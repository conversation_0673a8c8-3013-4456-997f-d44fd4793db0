import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Inject,
} from '@nestjs/common';

import { CreateWebsitePageDto } from './dto/create-website-page.dto';
import { QueryWebsitePageDto } from './dto/query-website-page.dto';
import { UpdateWebsitePageDto } from './dto/update-website-page.dto';
import { WebsitePageEntity } from './entities/website-page.entity';

import { DatabaseFactory } from '@/core/database/database.factory';
import { BaseBusinessService } from '@/modules/business/base/business.service';

@Injectable()
export class WebsitePageService extends BaseBusinessService {
  constructor(databaseFactory: DatabaseFactory) {
    super(databaseFactory, 'WebsitePageService');
  async create(tenantId: number, createDto: CreateWebsitePageDto): Promise<WebsitePageEntity> {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async findAll(tenantId: number, queryDto: QueryWebsitePageDto = { {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async findOne(tenantId: number, id: number): Promise<WebsitePageEntity> {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async findBySlug(tenantId: number, websiteId: number, path: string): Promise<WebsitePageEntity> {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async update(
    tenantId: number,
    id: number,
    updateDto: UpdateWebsitePageDto,
  ): Promise<WebsitePageEntity> {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async remove(tenantId: number, id: number): Promise<{ success: boolean }> {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async publish(tenantId: number, id: number): Promise<WebsitePageEntity> {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async unpublish(tenantId: number, id: number): Promise<WebsitePageEntity> {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async duplicate(tenantId: number, id: number, newSlug: string): Promise<WebsitePageEntity> {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async getPagesByWebsite(tenantId: number, websiteId: number) {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async getPageStats(tenantId: number, websiteId?: number) {
    throw new Error('Method not implemented - Schema fields need to be verified');
  // 基础服务方法
  async createPage(pageData: any) {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async getPageList(query: any) {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async getPageById(id: number) {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async updatePage(id: number, updateData: any) {
    throw new Error('Method not implemented - Schema fields need to be verified');
  async deletePage(id: number) {
    throw new Error('Method not implemented - Schema fields need to be verified');
}
