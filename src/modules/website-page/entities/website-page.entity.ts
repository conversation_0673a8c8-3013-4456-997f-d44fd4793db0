import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsObject, IsDate } from 'class-validator';

export class WebsitePageEntity {
  @ApiProperty({ description: '页面ID');
  id: number;

  @ApiProperty({ description: '页面标题');
  @IsString()
  title: string;

  @ApiProperty({ description: '页面路径/别名');
  @IsString()
  path: string;

  @ApiProperty({ description: '页面内容/组件配置', type: 'object');
  @IsObject()
  content: JsonValue;

  @ApiProperty({
    description: '页面状态',
    enum: ['draft', 'published'],
    default: 'draft',
  )
  @IsString()
  status: string;

  @ApiProperty({ description: 'SEO元信息', type: 'object', required: false 
  @IsOptional()
  @IsObject()
  seoMeta?: JsonValue;

  @ApiProperty({ description: '元数据', type: 'object', required: false 
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '所属网站ID');
  websiteId: number;

  @ApiProperty({ description: '创建时间');
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({ description: '更新时间');
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;

  // 关联对象（可选）
  @ApiProperty({ description: '所属网站信息', required: false 
  @IsOptional()
  website?: any;
}
