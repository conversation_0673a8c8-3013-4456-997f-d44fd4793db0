import { Module } from '@nestjs/common';

import { PaymentController, TenantPaymentController } from './controllers/payment.controller';
import { PaymentService } from './services/payment.service';

import { PrismaModule } from '@/core/database/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [PaymentController, TenantPaymentController],
  providers: [PaymentService],
  exports: [PaymentService],
)
export class PaymentModule {}
