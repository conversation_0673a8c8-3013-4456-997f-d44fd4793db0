import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  DefaultValuePipe,
  ParseIntPipe,
  Put,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiOkResponse } from '@nestjs/swagger';

import {
  CreatePaymentDto,
  PaymentResponseDto,
  PaymentResultDto,
  PaymentStatus,
  QueryPaymentDto,
  RefundRequestDto,
  PaymentStatsDto,
  ProcessRefundDto,
} from '../dto/payment.dto';
import { PaymentService } from '../services/payment.service';

import { BaseController } from '@/core/common/base/base.controller';
import { ApiPaginatedResponse } from '@/core/decorators/api-paginated-response.decorator';
import { CurrentTenant } from '@/core/decorators/current-tenant.decorator';
import { CurrentUser } from '@/core/decorators/current-user.decorator';
import { RequirePermissions } from '@/core/decorators/require-permissions.decorator';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 用户支付控制器
 */
@ApiTags('支付')
@Controller('payment')
export class PaymentController extends BaseController {
  constructor(private readonly paymentService: PaymentService) {
    super();
  }

  @Post('create')
  @ApiOperation({ summary: '创建支付订单');
  @ApiOkResponse({ type: PaymentResultDto 
  @RequirePermissions(['payment.use'])
  async createPayment(
    @Body() createDto: CreatePaymentDto,
    @CurrentUser('id') userId: number,
    @CurrentTenant() tenantId: number,
  ) {
    const result = await this.paymentService.createPayment(userId, createDto, tenantId);
    return this.success(result, '创建支付订单成功');

  @Get('status/:orderNo')
  @ApiOperation({ summary: '查询支付状态');
  @ApiOkResponse({ type: PaymentResponseDto 
  @RequirePermissions(['payment.use'])
  async getPaymentStatus(@Param('orderNo') orderNo: string, @CurrentTenant() tenantId: string) {
    const result = await this.paymentService.getPaymentStatus(orderNo, tenantId);
    return this.success(result);

  @Post('refund')
  @ApiOperation({ summary: '申请退款');
  @RequirePermissions(['payment.refund'])
  async requestRefund(
    @Body() refundDto: RefundRequestDto,
    @CurrentUser('id') userId: number,
    @CurrentTenant() tenantId: string,
  ) {
    const result = await this.paymentService.requestRefund(userId, refundDto, tenantId);
    return this.success(result, '申请退款成功，请等待处理');

  @Post('callback/:provider')
  @ApiOperation({ summary: '支付回调接口');
  async handleCallback(
    @Param('provider') provider: string,
    @Body() callbackData: any,
    @CurrentTenant() tenantId: string,
  ) {
    const result = await this.paymentService.handlePaymentCallback(
      provider,
      callbackData,
      // tenantId // not in schema
    );
    return this.success(result);

  @Put('test/update-status/:orderNo')
  @ApiOperation({ summary: '测试用-手动更新支付状态');
  @RequirePermissions(['payment.manage'])
  async updatePaymentStatus(
    @Param('orderNo') orderNo: string,
    @Body('status') status: PaymentStatus,
    @CurrentTenant() tenantId: string,
  ) {
    const result = await this.paymentService.updatePaymentStatus(orderNo, status, tenantId);
    return this.success(result, '更新支付状态成功');
/**
 * 租户支付管理控制器
 */
@ApiTags('租户支付管理')
@Controller('tenant/payment')
export class TenantPaymentController extends BaseController {
  constructor(private readonly paymentService: PaymentService) {
    super();
  }

  @Get('orders/list')
  @ApiOperation({ summary: '获取支付订单列表');
  @ApiPaginatedResponse(PaymentResponseDto)
  @RequirePermissions(['payment.manage'])
  async getPaymentList(
    @CurrentTenant() tenantId: string,
    @Query('businessType') businessType?: string,
    @Query('status') status?: PaymentStatus,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number = 10,
  ) {
    const options = new PaginationOptions(pageSize, (page - 1) * pageSize, page, pageSize);
    const queryDto: QueryPaymentDto = { businessType, status, startTime, endTime, page, pageSize };
    const result = await this.paymentService.getPaymentList(queryDto, options, tenantId);
    return this.success(result);

  @Get('statistics')
  @ApiOperation({ summary: '获取支付统计数据');
  @ApiOkResponse({ type: PaymentStatsDto 
  @RequirePermissions(['payment.manage'])
  async getPaymentStats(
    @CurrentTenant() tenantId: string,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string,
  ) {
    const result = await this.paymentService.getPaymentStats(startTime, endTime, tenantId);
    return this.success(result);

  @Post('refund/process')
  @ApiOperation({ summary: '处理退款申请');
  @RequirePermissions(['payment.refund'])
  async processRefund(@Body() processDto: ProcessRefundDto, @CurrentTenant() tenantId: string) {
    const result = await this.paymentService.processRefund(processDto, tenantId);
    return this.success(result, '处理退款申请成功');

  @Put('test/update-status/:orderNo')
  @ApiOperation({ summary: '测试用-手动更新支付状态');
  @RequirePermissions(['payment.manage'])
  async updatePaymentStatus(
    @Param('orderNo') orderNo: string,
    @Body('status') status: PaymentStatus,
    @CurrentTenant() tenantId: string,
  ) {
    const result = await this.paymentService.updatePaymentStatus(orderNo, status, tenantId);
    return this.success(result, '更新支付状态成功');