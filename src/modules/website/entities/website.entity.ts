import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsObject, IsDate } from 'class-validator';

export class WebsiteEntity {
  @ApiProperty({ description: '网站ID');
  id: number;

  @ApiProperty({ description: '网站名称');
  @IsString()
  name: string;

  @ApiProperty({ description: '自定义域名', required: false 
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiProperty({ description: '网站类型', default: 'tenant');
  @IsString()
  websiteType: string;

  @ApiProperty({
    description: '网站状态',
    enum: ['draft', 'published', 'archived'],
    default: 'draft',
  )
  @IsString()
  status: string;

  @ApiProperty({ description: '网站配置', type: 'object', required: false 
  @IsOptional()
  @IsObject()
  config?: JsonValue;

  @ApiProperty({ description: '网站内容', type: 'object', required: false 
  @IsOptional()
  @IsObject()
  content?: JsonValue;

  @ApiProperty({ description: 'SEO配置', type: 'object', required: false 
  @IsOptional()
  @IsObject()
  seoConfig?: JsonValue;

  @ApiProperty({ description: '分析统计配置', type: 'object', required: false 
  @IsOptional()
  @IsObject()
  analytics?: JsonValue;

  @ApiProperty({ description: '元数据', type: 'object', required: false 
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '发布时间', required: false 
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  publishedAt?: Date;

  @ApiProperty({ description: '租户ID', required: false 
  @IsOptional()
  tenantId?: number;

  @ApiProperty({ description: '创建时间');
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({ description: '更新时间');
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;

  // 关联对象（可选）
  @ApiProperty({ description: '页面列表', required: false 
  @IsOptional()
  pages?: any[];
}
