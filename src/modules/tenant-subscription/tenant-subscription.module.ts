import { Module } from '@nestjs/common';

import { SystemSubscriptionStrategy } from './strategies/system-subscription.strategy';
import { TenantSubscriptionController } from './tenant-subscription.controller';
import { TenantSubscriptionService } from './tenant-subscription.service';

import { CoreModule } from '@/core/core.module';

@Module({
  imports: [CoreModule],
  controllers: [TenantSubscriptionController],
  providers: [TenantSubscriptionService, SystemSubscriptionStrategy],
  exports: [TenantSubscriptionService],
)
export class TenantSubscriptionModule {}
