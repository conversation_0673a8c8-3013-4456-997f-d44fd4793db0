import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsArray,
  IsObject,
  Min,
} from 'class-validator';

/**
 * 订阅计划状态枚举
 */
export enum SubscriptionPlanStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived',
}

/**
 * 计费周期枚举
 */
export enum PlanBillingCycle {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
}

/**
 * 创建订阅计划DTO
 */
export class CreateSubscriptionPlanDto {
  @ApiProperty({ description: '计划代码', example: 'basic');
  @IsString()
  code: string;

  @ApiProperty({ description: '计划名称', example: '基础版');
  @IsString()
  name: string;

  @ApiProperty({ description: '计划描述');
  @IsString()
  description: string;

  @ApiProperty({ description: '价格', example: 99 
  @IsNumber()
  @Min(0)
  price: number;

  @ApiPropertyOptional({ description: '原价', example: 199 
  @IsOptional()
  @IsNumber()
  @Min(0)
  originalPrice?: number;

  @ApiProperty({
    description: '计费周期',
    enum: PlanBillingCycle,
    example: PlanBillingCycle.MONTHLY,
  )
  @IsEnum(PlanBillingCycle)
  billingCycle: PlanBillingCycle;

  @ApiProperty({ description: '功能配置', example: { maxTenants: 10, maxUsers: 100 } )
  @IsObject()
  features: Record<string, any>;

  @ApiPropertyOptional({ description: '是否启用', default: true 
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({ description: '排序顺序', default: 0 
  @IsOptional()
  @IsNumber()
  sortOrder?: number;

  @ApiPropertyOptional({ description: '计划元数据');
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 更新订阅计划DTO
 */
export class UpdateSubscriptionPlanDto extends PartialType(CreateSubscriptionPlanDto) {
  @ApiPropertyOptional({
    description: '计划状态',
    enum: SubscriptionPlanStatus,
  )
  @IsOptional()
  @IsEnum(SubscriptionPlanStatus)
  status?: SubscriptionPlanStatus;
}

/**
 * 查询订阅计划DTO
 */
export class QuerySubscriptionPlanDto {
  @ApiPropertyOptional({
    description: '计划状态',
    enum: SubscriptionPlanStatus,
  )
  @IsOptional()
  @IsEnum(SubscriptionPlanStatus)
  status?: SubscriptionPlanStatus;

  @ApiPropertyOptional({ description: '是否启用');
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean;

  @ApiPropertyOptional({ description: '页码', default: 1 
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', default: 10 
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  pageSize?: number;
}

/**
 * 订阅计划响应DTO
 */
export class SubscriptionPlanResponseDto {
  @ApiProperty({ description: '计划ID');
  id: number;

  @ApiProperty({ description: '计划代码');
  code: string;

  @ApiProperty({ description: '计划名称');
  name: string;

  @ApiProperty({ description: '计划描述');
  description: string;

  @ApiProperty({ description: '价格');
  price: number;

  @ApiProperty({ description: '原价');
  originalPrice: number;

  @ApiProperty({
    description: '计费周期',
    enum: PlanBillingCycle,
  )
  billingCycle: PlanBillingCycle;

  @ApiProperty({ description: '功能配置');
  features: Record<string, any>;

  @ApiProperty({ description: '是否启用');
  // isActive: // not in schema boolean;

  @ApiProperty({
    description: '计划状态',
    enum: SubscriptionPlanStatus,
  )
  status: SubscriptionPlanStatus;

  @ApiProperty({ description: '排序顺序');
  metadata: { sortOrder: number;

  @ApiProperty({ description: '计划元数据');
  metadata: Record<string, any>;

  @ApiProperty({ description: '创建时间');
  createTime: string;

  @ApiProperty({ description: '更新时间');
  updateTime: string;
}

/**
 * 订阅计划功能配置DTO
 */
export class PlanFeatureConfigDto {
  @ApiProperty({ description: '功能代码');
  @IsString()
  featureCode: string;

  @ApiProperty({ description: '是否启用');
  @IsBoolean()
  enabled: boolean;

  @ApiPropertyOptional({ description: '配额限制');
  @IsOptional()
  @IsNumber()
  quota?: number;

  @ApiPropertyOptional({ description: '功能配置');
  @IsOptional()
  @IsObject()
  config?: Record<string, any>;
}

/**
 * 批量更新计划功能DTO
 */
export class UpdatePlanFeaturesDto {
  @ApiProperty({ description: '功能配置列表', type: [PlanFeatureConfigDto]);
  @IsArray()
  @Type(() => PlanFeatureConfigDto)
  features: PlanFeatureConfigDto[];
}
