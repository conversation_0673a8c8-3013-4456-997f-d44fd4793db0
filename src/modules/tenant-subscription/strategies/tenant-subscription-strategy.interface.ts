import { CreateSubscriptionPlanDto, UpdateSubscriptionPlanDto } from '../dto/subscription-plan.dto';
import {
  CreateTenantSubscriptionDto,
  UpdateTenantSubscriptionDto,
} from '../dto/tenant-subscription.dto';

import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 租户订阅策略接口
 */
export interface TenantSubscriptionStrategyInterface {
  // 订阅计划管理
  createPlan(createPlanDto: CreateSubscriptionPlanDto): Promise<any>;
  findAllPlans(where: any, options: PaginationOptions): Promise<any>;
  findPlanById(id: number): Promise<any>;
  findPlanByCode(code: string): Promise<any>;
  updatePlan(id: number, updatePlanDto: UpdateSubscriptionPlanDto): Promise<any>;
  removePlan(id: number): Promise<any>;
  isPlanCodeExists(code: string, excludeId?: number): Promise<boolean>;

  // 租户订阅管理
  createSubscription(createSubscriptionDto: CreateTenantSubscriptionDto): Promise<any>;
  findAllSubscriptions(where: any, options: PaginationOptions): Promise<any>;
  findSubscriptionById(id: number): Promise<any>;
  findActiveSubscriptionByTenant(tenantId: string): Promise<any>;
  updateSubscription(id: number, updateSubscriptionDto: UpdateTenantSubscriptionDto): Promise<any>;
  removeSubscription(id: number): Promise<any>;

  // 统计方法
  getSubscriptionStats(): Promise<any>;
  getTenantSubscriptionHistory(tenantId: string): Promise<any>;
}
