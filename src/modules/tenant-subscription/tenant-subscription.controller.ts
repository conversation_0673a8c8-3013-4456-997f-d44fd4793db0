import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiOkResponse, ApiQuery } from '@nestjs/swagger';

import {
  CreateSubscriptionPlanDto,
  UpdateSubscriptionPlanDto,
  QuerySubscriptionPlanDto,
  SubscriptionPlanResponseDto,
  SubscriptionPlanStatus,
} from './dto/subscription-plan.dto';
import {
  CreateTenantSubscriptionDto,
  UpdateTenantSubscriptionDto,
  QueryTenantSubscriptionDto,
  TenantSubscriptionResponseDto,
  SubscriptionStatus,
} from './dto/tenant-subscription.dto';
import { TenantSubscriptionService } from './tenant-subscription.service';

import { BaseController } from '@/core/common/base/base.controller';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 租户订阅控制器
 * 处理系统管理员对租户订阅的管理
 */
@ApiTags('租户订阅管理')
@Controller('system/tenant-subscription')
export class TenantSubscriptionController extends BaseController {
  constructor(private readonly tenantSubscriptionService: TenantSubscriptionService) {
    super();
  }

  // ==================== 订阅计划管理 ====================

  @Post('plans')
  @ApiOperation({ summary: '创建订阅计划');
  @ApiOkResponse({ type: SubscriptionPlanResponseDto 
  async createPlan(@Body() createPlanDto: CreateSubscriptionPlanDto) {
    const data = await this.tenantSubscriptionService.createPlan(createPlanDto);
    return this.success(data, '创建订阅计划成功');

  @Get('plans')
  @ApiOperation({ summary: '获取订阅计划列表');
  @ApiOkResponse({ type: [SubscriptionPlanResponseDto]);
  @ApiQuery({ name: 'status', enum: SubscriptionPlanStatus, required: false 
  @ApiQuery({ name: 'isActive', type: Boolean, required: false 
  @ApiQuery({ name: 'page', type: Number, required: false 
  @ApiQuery({ name: 'pageSize', type: Number, required: false 
  async getPlans(
    @Query('status') status?: SubscriptionPlanStatus,
    @Query('isActive') isActive?: boolean,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number = 10,
  ) {
    const options = new PaginationOptions(pageSize, (page - 1) * pageSize, page, pageSize);
    const queryDto: QuerySubscriptionPlanDto = { status, isActive, page, pageSize };
    const data = await this.tenantSubscriptionService.findAllPlans(queryDto, options);
    return this.success(data);

  @Get('plans/:id')
  @ApiOperation({ summary: '获取订阅计划详情');
  @ApiOkResponse({ type: SubscriptionPlanResponseDto 
  async getPlanById(@Param('id', ParseIntPipe) id: number) {
    const data = await this.tenantSubscriptionService.findPlanById(id);
    return this.success(data);

  @Put('plans/:id')
  @ApiOperation({ summary: '更新订阅计划');
  @ApiOkResponse({ type: SubscriptionPlanResponseDto 
  async updatePlan(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePlanDto: UpdateSubscriptionPlanDto,
  ) {
    const data = await this.tenantSubscriptionService.updatePlan(id, updatePlanDto);
    return this.success(data, '更新订阅计划成功');

  @Delete('plans/:id')
  @ApiOperation({ summary: '删除订阅计划');
  async removePlan(@Param('id', ParseIntPipe) id: number) {
    await this.tenantSubscriptionService.removePlan(id);
    return this.success(null, '删除订阅计划成功');

  // ==================== 租户订阅管理 ====================

  @Post('subscriptions')
  @ApiOperation({ summary: '创建租户订阅');
  @ApiOkResponse({ type: TenantSubscriptionResponseDto 
  async createSubscription(@Body() createSubscriptionDto: CreateTenantSubscriptionDto) {
    const data = await this.tenantSubscriptionService.createSubscription(createSubscriptionDto);
    return this.success(data, '创建租户订阅成功');

  @Get('subscriptions')
  @ApiOperation({ summary: '获取租户订阅列表');
  @ApiOkResponse({ type: [TenantSubscriptionResponseDto]);
  @ApiQuery({ name: 'tenantId', type: String, required: false 
  @ApiQuery({ name: 'planId', type: Number, required: false 
  @ApiQuery({ name: 'status', enum: SubscriptionStatus, required: false 
  @ApiQuery({ name: 'page', type: Number, required: false 
  @ApiQuery({ name: 'pageSize', type: Number, required: false 
  async getSubscriptions(
    @Query('tenantId') tenantId?: string,
    @Query('planId', new DefaultValuePipe(0), ParseIntPipe) planId?: number,
    @Query('status') status?: SubscriptionStatus,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number = 10,
  ) {
    const options = new PaginationOptions(pageSize, (page - 1) * pageSize, page, pageSize);
    const queryDto: QueryTenantSubscriptionDto = { tenantId, planId, status, page, pageSize };
    const data = await this.tenantSubscriptionService.findAllSubscriptions(queryDto, options);
    return this.success(data);

  @Get('subscriptions/:id')
  @ApiOperation({ summary: '获取租户订阅详情');
  @ApiOkResponse({ type: TenantSubscriptionResponseDto 
  async getSubscriptionById(@Param('id', ParseIntPipe) id: number) {
    const data = await this.tenantSubscriptionService.findSubscriptionById(id);
    return this.success(data);

  @Put('subscriptions/:id')
  @ApiOperation({ summary: '更新租户订阅');
  @ApiOkResponse({ type: TenantSubscriptionResponseDto 
  async updateSubscription(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateSubscriptionDto: UpdateTenantSubscriptionDto,
  ) {
    const data = await this.tenantSubscriptionService.updateSubscription(id, updateSubscriptionDto);
    return this.success(data, '更新租户订阅成功');

  @Delete('subscriptions/:id')
  @ApiOperation({ summary: '取消租户订阅');
  async removeSubscription(@Param('id', ParseIntPipe) id: number) {
    await this.tenantSubscriptionService.removeSubscription(id);
    return this.success(null, '取消租户订阅成功');

  // ==================== 统计接口 ====================

  @Get('stats')
  @ApiOperation({ summary: '获取订阅统计数据');
  async getStats() {
    const data = await this.tenantSubscriptionService.getSubscriptionStats();
    return this.success(data);

  @Get('tenant/:tenantId/history')
  @ApiOperation({ summary: '获取租户订阅历史');
  async getTenantHistory(@Param('tenantId') tenantId: string) {
    const data = await this.tenantSubscriptionService.getTenantSubscriptionHistory(tenantId);
    return this.success(data);

  @Get('tenant/:tenantId/active')
  @ApiOperation({ summary: '获取租户活跃订阅');
  async getTenantActiveSubscription(@Param('tenantId') tenantId: string) {
    const data = await this.tenantSubscriptionService.getTenantActiveSubscription(tenantId);
    return this.success(data);