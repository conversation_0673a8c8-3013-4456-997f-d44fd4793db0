import { Injectable, NotFoundException, ConflictException, Inject } from '@nestjs/common';

import { CreateWebsiteSeoDto } from './dto/create-website-seo.dto';
import { QueryWebsiteSeoDto } from './dto/query-website-seo.dto';
import { UpdateWebsiteSeoDto } from './dto/update-website-seo.dto';
// import any // WebsiteSeoEntity not exist - table does not exist

import { DatabaseFactory } from '@/core/database/database.factory';
import { BaseBusinessService } from '@/modules/business/base/business.service';

@Injectable()
export class WebsiteSeoService extends BaseBusinessService {
  constructor(databaseFactory: DatabaseFactory) {
    super(databaseFactory, 'WebsiteSeoService');
  async create(tenantId: number, createDto: CreateWebsiteSeoDto): Promise<any> {
    // 检查网站是否存在
    const website = await this.tenantDb.website.findFirst({
      where: {
        id: createDto.websiteId,
        // tenantId // not in schema
      },
    )
    if (!website)  {
      throw new NotFoundException(`网站 ID ${createDto.websiteId} 不存在`);
    // 检查是否已存在SEO配置
    const existingSeo = await this.tenantDb.website.findFirst({
      where: {
        // websiteId: // check schema createDto.websiteId,
        // tenantId // not in schema
      },
    )
    if (existingSeo)  {
      throw new ConflictException(`网站 ID ${createDto.websiteId} 已存在SEO配置`);
    const seo = await this.tenantDb.website.create({
      data: {
        ...createDto,
        // tenantId // not in schema
      },
      include: {
        website: true,
      },
    )
    return this.mapToEntity(seo);
  async findAll(tenantId: number, queryDto: QueryWebsiteSeoDto = {; {
    const {
      page = 1,
      limit = 20,
      keyword,
      websiteId,
      sortBy = 'createTime',
      sortOrder = 'desc',
      includeRelations = false,
    } = queryDto;

    const skip = (page - 1) * limit;
    const take = limit;

    // 构建查询条件
    const where: any = {
      // tenantId // not in schema
    };

    if (keyword)  {
      where.OR = [
        { title: { contains: keyword, mode: 'insensitive' } },
        { description: { contains: keyword, mode: 'insensitive' } },
        { keywords: { contains: keyword, mode: 'insensitive' } },
      ];
    }

    if (websiteId)  {
      where.websiteId = websiteId;
    }

    // 构建排序条件
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    // 构建包含关系
    const include = includeRelations
      ? {
          website: {
            select: {
              id: true,
              name: true,
              domain: true,
              // subdomain: // not in schema true,
              status: true,
            },
          },
        }
      : undefined;

    const [seos, total] = await Promise.all([
      this.tenantDb.website.findMany({
        where,
        skip,
        take,
        orderBy,
        include,
      ),
      this.tenantDb.website.count({ where ),
    ]);

    return {
      data: seos.map(seo => this.mapToEntity(seo)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(tenantId: number, id: number): Promise<any> {
    const seo = await this.tenantDb.website.findFirst({
      where: {
        id,
        // tenantId // not in schema
      },
      include: {
        website: true,
      },
    )
    if (!seo)  {
      throw new NotFoundException(`SEO配置 ID ${id} 不存在`);
    return this.mapToEntity(seo);
  async findByWebsiteId(tenantId: number, // websiteId: // check schema number): Promise<any // WebsiteSeoEntity not exist | null> {
    const seo = await this.tenantDb.website.findFirst({
      where: {
        websiteId,
        // tenantId // not in schema
      },
      include: {
        website: true,
      },
    ;
    return seo ? this.mapToEntity(seo) : null;
  async update(
    tenantId: number,
    id: number,
    updateDto: UpdateWebsiteSeoDto,
  ): Promise<any> {
    await this.findOne(tenantId, id); // 检查是否存在

    const seo = await this.tenantDb.website.update({
      where: {
        id,
        // tenantId // not in schema
      },
      data: updateDto,
      include: {
        website: true,
      },
    )
    return this.mapToEntity(seo ;
  async remove(tenantId: number, id: number): Promise<void> {
    await this.findOne(tenantId, id); // 检查是否存在

    await this.tenantDb.website.delete({
      where: {
        id,
        // tenantId // not in schema
      },
    )
  }

  async generateMetaTags(tenantId: number, // websiteId: // check schema number): Promise<Record<string, string>> {
    const seo = await this.findByWebsiteId(tenantId, websiteId);

    if (!seo)  {
      return {};
    }

    const metaTags: Record<string, string> = {};

    // 基础meta标签
    if (seo.title) metaTags['title'] = seo.title;
    if (seo.description) metaTags['description'] = seo.description;
    if (seo.keywords) metaTags['keywords'] = seo.keywords;
    if (seo.author) metaTags['author'] = seo.author;
    if (seo.robots) metaTags['robots'] = seo.robots;
    if (seo.canonical) metaTags['canonical'] = seo.canonical;

    // Open Graph标签
    if (seo.ogTitle) metaTags['og:title'] = seo.ogTitle;
    if (seo.ogDescription) metaTags['og:description'] = seo.ogDescription;
    if (seo.ogImage) metaTags['og:image'] = seo.ogImage;
    if (seo.ogType) metaTags['og:type'] = seo.ogType;

    // Twitter标签
    if (seo.twitterCard) metaTags['twitter:card'] = seo.twitterCard;
    if (seo.twitterSite) metaTags['twitter:site'] = seo.twitterSite;
    if (seo.twitterCreator) metaTags['twitter:creator'] = seo.twitterCreator;

    // 自定义meta标签
    if (seo.customMeta && typeof seo.customMeta === 'object')  {
      Object.assign(metaTags, seo.customMeta )
    return metaTags;
  async generateStructuredData(tenantId: number, // websiteId: // check schema number): Promise<any> {
    const seo = await this.findByWebsiteId(tenantId, websiteId);

    if (!seo || !seo.structuredData)  {
      return null;
    return seo.structuredData;
  async getAnalyticsConfig(tenantId: number, // websiteId: // check schema number): Promise<any> {
    const seo = await this.findByWebsiteId(tenantId, websiteId);

    if (!seo || !seo.analytics)  {
      return {};
    }

    return seo.analytics;
  async getVerificationCodes(tenantId: number, // websiteId: // check schema number): Promise<any> {
    const seo = await this.findByWebsiteId(tenantId, websiteId);

    if (!seo || !seo.verification)  {
      return {};
    }

    return seo.verification;
  // 映射数据库模型到实体
  private mapToEntity(dbSeo: any): any // WebsiteSeoEntity not exist {
    return {
      id: dbSeo.id,
      // websiteId: // check schema dbSeo.websiteId,
      title: dbSeo.title,
      description: dbSeo.description,
      keywords: dbSeo.keywords,
      author: dbSeo.author,
      robots: dbSeo.robots,
      canonical: dbSeo.canonical,
      ogTitle: dbSeo.ogTitle,
      ogDescription: dbSeo.ogDescription,
      ogImage: dbSeo.ogImage,
      ogType: dbSeo.ogType,
      twitterCard: dbSeo.twitterCard,
      twitterSite: dbSeo.twitterSite,
      twitterCreator: dbSeo.twitterCreator,
      structuredData: dbSeo.structuredData,
      customMeta: dbSeo.customMeta,
      analytics: dbSeo.analytics,
      verification: dbSeo.verification,
      tenantId: dbSeo.// tenantId // not in schema
      createTime: dbSeo.createTime,
      updateTime: dbSeo.updateTime,
      website: dbSeo.website,
    };
  }

  // 基础服务方法暂时留空，需要根据实际Schema调整
  async createSeo(seoData: any) {
    throw new Error('Method not implemented - Schema fields need to be verified');
}
