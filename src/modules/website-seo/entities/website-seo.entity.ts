import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsInt, IsObject, IsDate } from 'class-validator';

export class WebsiteSeoEntity {
  @ApiProperty({ description: 'SEO配置ID');
  id: number;

  @ApiProperty({ description: '所属网站ID');
  websiteId: number;

  @ApiProperty({ description: '网站标题', required: false 
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ description: '网站描述', required: false 
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '关键词', required: false 
  @IsOptional()
  @IsString()
  keywords?: string;

  @ApiProperty({ description: '作者', required: false 
  @IsOptional()
  @IsString()
  author?: string;

  @ApiProperty({ description: 'robots指令', default: 'index,follow');
  @IsString()
  robots: string;

  @ApiProperty({ description: '规范URL', required: false 
  @IsOptional()
  @IsString()
  canonical?: string;

  @ApiProperty({ description: 'Open Graph标题', required: false 
  @IsOptional()
  @IsString()
  ogTitle?: string;

  @ApiProperty({ description: 'Open Graph描述', required: false 
  @IsOptional()
  @IsString()
  ogDescription?: string;

  @ApiProperty({ description: 'Open Graph图片', required: false 
  @IsOptional()
  @IsString()
  ogImage?: string;

  @ApiProperty({ description: 'Open Graph类型', default: 'website');
  @IsString()
  ogType: string;

  @ApiProperty({ description: 'Twitter卡片类型', default: 'summary');
  @IsString()
  twitterCard: string;

  @ApiProperty({ description: 'Twitter站点', required: false 
  @IsOptional()
  @IsString()
  twitterSite?: string;

  @ApiProperty({ description: 'Twitter创建者', required: false 
  @IsOptional()
  @IsString()
  twitterCreator?: string;

  @ApiProperty({ description: '结构化数据', type: 'object', required: false 
  @IsOptional()
  @IsObject()
  structuredData?: JsonValue;

  @ApiProperty({ description: '自定义meta标签', type: 'object', required: false 
  @IsOptional()
  @IsObject()
  customMeta?: JsonValue;

  @ApiProperty({ description: '分析工具配置', type: 'object', required: false 
  @IsOptional()
  @IsObject()
  analytics?: JsonValue;

  @ApiProperty({ description: '网站验证码', type: 'object', required: false 
  @IsOptional()
  @IsObject()
  verification?: JsonValue;

  @ApiProperty({ description: '租户ID');
  tenantId: number;

  @ApiProperty({ description: '创建时间');
  @IsDate()
  @Type(() => Date)
  createTime: Date;

  @ApiProperty({ description: '更新时间');
  @IsDate()
  @Type(() => Date)
  updateTime: Date;

  // 关联对象（可选）
  @ApiProperty({ description: '网站信息', required: false 
  @IsOptional()
  website?: any;
}
