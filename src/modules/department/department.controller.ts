import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  Query,
  Req,
  Patch,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { DepartmentService } from './department.service';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { DepartmentDto, DepartmentTreeDto, DepartmentListItemDto } from './dto/department.dto';
import { QueryDepartmentDto } from './dto/query-department.dto';
import { UpdateDepartmentStatusDto } from './dto/update-department-status.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';

import { JwtAuthGuard } from '@/core/auth/guards/jwt-auth.guard';
import { BaseController } from '@/core/common/base/base.controller';

/**
 * 部门控制器
 * 处理部门相关的API请求
 */
@ApiTags('部门')
@Controller(['departments', 'system/dept'])
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DepartmentController extends BaseController {
  constructor(private readonly departmentService: DepartmentService) {
    super();
  }

  /**
   * 获取部门树形列表
   * @param query 查询参数
   * @param req 请求对象
   * @returns 部门树形列表
   */
  @Get('tree')
  @ApiOperation({ summary: '获取部门树形列表');
  @ApiResponse({ status: 200, description: '获取成功', type: [DepartmentTreeDto]);
  @ApiQuery({ name: 'name', type: 'string', required: false, description: '部门名称，模糊查询');
  @ApiQuery({
    name: 'status',
    type: 'number',
    required: false,
    description: '部门状态，0-禁用，1-启用',
  )
  async findTree(@Query() query: QueryDepartmentDto, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.departmentService.findTree(query, userType, tenantId);
    return this.success(data, '获取成功');

  /**
   * 获取部门列表（扁平结构）
   * @param status 部门状态
   * @param req 请求对象
   * @returns 部门列表
   */
  @Get('list')
  @ApiOperation({ summary: '获取部门列表（扁平结构）');
  @ApiResponse({ status: 200, description: '获取成功', type: [DepartmentListItemDto]);
  @ApiQuery({
    name: 'status',
    type: 'number',
    required: false,
    description: '部门状态，0-禁用，1-启用',
  )
  async findList(@Query('status') status: number, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);

    const query: QueryDepartmentDto = {};
    if (status !== undefined)  {
      query.status = status;
    }

    const data = await this.departmentService.findList(query, userType, tenantId);
    return this.success(data, '获取成功');

  /**
   * 获取部门详情
   * @param id 部门ID
   * @param req 请求对象
   * @returns 部门详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取部门详情');
  @ApiResponse({ status: 200, description: '获取成功', type: DepartmentDto 
  @ApiParam({ name: 'id', description: '部门ID');
  async findOne(@Param('id') id: string, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.departmentService.findOne(+id, userType, tenantId);
    return this.success(data, '获取成功');

  /**
   * 创建部门
   * @param createDepartmentDto 创建部门数据
   * @param req 请求对象
   * @returns 创建的部门
   */
  @Post()
  @ApiOperation({ summary: '创建部门');
  @ApiResponse({ status: 200, description: '创建成功', type: DepartmentDto 
  async create(@Body() createDepartmentDto: CreateDepartmentDto, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.departmentService.create(createDepartmentDto, userType, tenantId);
    return this.success(data, '创建成功');

  /**
   * 更新部门
   * @param id 部门ID
   * @param updateDepartmentDto 更新部门数据
   * @param req 请求对象
   * @returns 更新后的部门
   */
  @Put(':id')
  @ApiOperation({ summary: '更新部门');
  @ApiResponse({ status: 200, description: '更新成功', type: DepartmentDto 
  @ApiParam({ name: 'id', description: '部门ID');
  async update(
    @Param('id') id: string,
    @Body() updateDepartmentDto: UpdateDepartmentDto,
    @Req() req: any,
  ) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.departmentService.update(+id, updateDepartmentDto, userType, tenantId);
    return this.success(data, '更新成功');

  /**
   * 删除部门
   * @param id 部门ID
   * @param req 请求对象
   * @returns 删除结果
   */
  @Delete(':id')
  @ApiOperation({ summary: '删除部门');
  @ApiResponse({ status: 200, description: '删除成功');
  @ApiParam({ name: 'id', description: '部门ID');
  async remove(@Param('id') id: string, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.departmentService.remove(+id, userType, tenantId);
    return this.success(data, '删除成功');

  /**
   * 更新部门状态
   * @param id 部门ID
   * @param updateStatusDto 更新状态数据
   * @param req 请求对象
   * @returns 更新结果
   */
  @Patch(':id/status')
  @ApiOperation({ summary: '更新部门状态');
  @ApiResponse({ status: 200, description: '更新成功');
  @ApiParam({ name: 'id', description: '部门ID');
  async updateStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateDepartmentStatusDto,
    @Req() req: any,
  ) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.departmentService.updateStatus(
      +id,
      updateStatusDto,
      userType,
      // tenantId // not in schema
    );
    return this.success(data, '更新成功');

  /**
   * 检查部门名称是否存在
   * @param name 部门名称
   * @param pid 父部门ID
   * @param id 排除的部门ID（可选）
   * @param req 请求对象
   * @returns 检查结果
   */
  @Get('name-exists')
  @ApiOperation({ summary: '检查部门名称是否存在');
  @ApiResponse({ status: 200, description: '检查成功');
  @ApiQuery({ name: 'name', type: 'string', required: true, description: '部门名称');
  @ApiQuery({ name: 'pid', type: 'number', required: true, description: '父部门ID');
  @ApiQuery({
    name: 'id',
    type: 'number',
    required: false,
    description: '排除的部门ID（可选，用于编辑时检查）',
  )
  async checkNameExists(
    @Query('name') name: string,
    @Query('pid') pid: number,
    @Query('id') id: number,
    @Req() req: any,
  ) {
    const { userType, tenantId } = this.getUserContext(req);
    const exists = await this.departmentService.checkNameExists(
      name,
      +pid,
      userType,
      // tenantId // not in schema
      id ? +id : undefined,
    );
    return this.success(exists, '检查成功');