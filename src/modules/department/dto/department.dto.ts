import { ApiProperty } from '@nestjs/swagger';

/**
 * 部门DTO
 */
export class DepartmentDto {
  @ApiProperty({ description: '部门ID');
  id: number;

  @ApiProperty({ description: '部门名称');
  name: string;

  @ApiProperty({ description: '父部门ID，0表示顶级部门');
  pid: number;

  @ApiProperty({ description: '部门状态：0-禁用，1-启用');
  status: number;

  @ApiProperty({ description: '排序号');
  orderNo: number;

  @ApiProperty({ description: '创建时间');
  createTime: string;

  @ApiProperty({ description: '更新时间');
  updateTime: string;

  @ApiProperty({ description: '备注', required: false 
  remark?: string;
}

/**
 * 部门树形结构DTO
 */
export class DepartmentTreeDto extends DepartmentDto {
  @ApiProperty({ description: '子部门列表', type: [DepartmentTreeDto]);
  children: DepartmentTreeDto[];
}

/**
 * 部门列表DTO（扁平结构）
 */
export class DepartmentListItemDto {
  @ApiProperty({ description: '部门ID');
  id: number;

  @ApiProperty({ description: '部门名称');
  name: string;

  @ApiProperty({ description: '父部门ID，0表示顶级部门');
  pid: number;

  @ApiProperty({ description: '部门状态：0-禁用，1-启用');
  status: number;

  @ApiProperty({ description: '排序号');
  orderNo: number;

  @ApiProperty({ description: '创建时间');
  createTime: string;

  @ApiProperty({ description: '更新时间');
  updateTime: string;

  @ApiProperty({ description: '备注', required: false 
  remark?: string;

  @ApiProperty({ description: '子部门列表', type: [DepartmentListItemDto]);
  children: DepartmentListItemDto[];
}
