import { CreateDepartmentDto } from '../dto/create-department.dto';
import { DepartmentDto, DepartmentTreeDto, DepartmentListItemDto } from '../dto/department.dto';
import { QueryDepartmentDto } from '../dto/query-department.dto';
import { UpdateDepartmentStatusDto } from '../dto/update-department-status.dto';
import { UpdateDepartmentDto } from '../dto/update-department.dto';

/**
 * 部门策略接口
 * 定义部门操作的通用方法
 */
export interface IDepartmentStrategy {
  /**
   * 获取策略类型
   * @returns 策略类型
   */
  getType(): string;

  /**
   * 创建部门
   * @param createDepartmentDto 创建部门数据
   * @param tenantId 租户ID（可选）
   * @returns 创建的部门
   */
  create(createDepartmentDto: CreateDepartmentDto, tenantId?: string): Promise<DepartmentDto>;

  /**
   * 获取部门树形列表
   * @param query 查询条件
   * @param tenantId 租户ID（可选）
   * @returns 部门树形列表
   */
  findTree(query: QueryDepartmentDto, tenantId?: string): Promise<DepartmentTreeDto[]>;

  /**
   * 获取部门列表（扁平结构）
   * @param query 查询条件
   * @param tenantId 租户ID（可选）
   * @returns 部门列表
   */
  findList(query: QueryDepartmentDto, tenantId?: string): Promise<DepartmentListItemDto[]>;

  /**
   * 获取部门详情
   * @param id 部门ID
   * @param tenantId 租户ID（可选）
   * @returns 部门详情
   */
  findOne(id: number, tenantId?: string): Promise<DepartmentDto>;

  /**
   * 更新部门
   * @param id 部门ID
   * @param updateDepartmentDto 更新部门数据
   * @param tenantId 租户ID（可选）
   * @returns 更新后的部门
   */
  update(
    id: number,
    updateDepartmentDto: UpdateDepartmentDto,
    tenantId?: string,
  ): Promise<DepartmentDto>;

  /**
   * 更新部门状态
   * @param id 部门ID
   * @param updateStatusDto 更新状态数据
   * @param tenantId 租户ID（可选）
   * @returns 更新结果
   */
  updateStatus(
    id: number,
    updateStatusDto: UpdateDepartmentStatusDto,
    tenantId?: string,
  ): Promise<{ id: number; status: number }>;

  /**
   * 删除部门
   * @param id 部门ID
   * @param tenantId 租户ID（可选）
   * @returns 删除结果
   */
  remove(id: number, tenantId?: string): Promise<{ success: boolean }>;

  /**
   * 检查部门名称是否存在
   * @param name 部门名称
   * @param pid 父部门ID
   * @param tenantId 租户ID（可选）
   * @param excludeId 排除的部门ID（可选）
   * @returns 是否存在
   */
  checkNameExists(
    name: string,
    pid: number,
    tenantId?: string,
    excludeId?: number,
  ): Promise<boolean>;
}
