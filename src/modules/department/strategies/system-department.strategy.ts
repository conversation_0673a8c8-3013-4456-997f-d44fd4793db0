import { Injectable } from '@nestjs/common';

import type { CreateDepartmentDto } from '../dto/create-department.dto';
import type { DepartmentDto } from '../dto/department.dto';
import type { DepartmentListItemDto } from '../dto/department-list-item.dto';
import type { DepartmentTreeDto } from '../dto/department-tree.dto';
import type { QueryDepartmentDto } from '../dto/query-department.dto';
import type { UpdateDepartmentDto } from '../dto/update-department.dto';
import type { UpdateDepartmentStatusDto } from '../dto/update-department-status.dto';
import { BaseDepartmentStrategy } from './base-department.strategy';

import { DatabaseFactory } from '@/core/database/database.factory';

@Injectable()
export class SystemDepartmentStrategy extends BaseDepartmentStrategy {
  constructor(private readonly databaseFactory: DatabaseFactory) {
    super();
  }

  async create(...args: any[]): Promise<DepartmentDto> {
    const [createDepartmentDto] = args as [CreateDepartmentDto];
    const db = this.databaseFactory.getPublicClient();

    const department = await db.department.create({
      data: createDepartmentDto,
    );

    return department as DepartmentDto;
  async findTree(...args: any[]): Promise<DepartmentTreeDto[]> {
    const [queryDto] = args as [QueryDepartmentDto];
    const db = this.databaseFactory.getPublicClient();

    const departments = await db.department.findMany({
      where: {
        ...(queryDto.status !== undefined && { status: queryDto.status ,
      },
      orderBy: { createTime: 'asc' },
    );

    return this.buildTree(departments as any[]);

  async findList(...args: any[]): Promise<DepartmentListItemDto[]> {
    const [queryDto] = args as [QueryDepartmentDto];
    const db = this.databaseFactory.getPublicClient();

    const departments = await db.department.findMany({
      where: {
        ...(queryDto.status !== undefined && { status: queryDto.status ,
        ...(queryDto.name && { name: { contains: queryDto.name } ),
      },
      orderBy: { createTime: 'asc' },
    });

    return departments.map(dept => ({
      id: dept.id,
      name: dept.name,
      pid: dept.pid,
      status: dept.status,
      createTime: dept.createTime,
      updateTime: dept.updateTime,
    ;) as DepartmentListItemDto[];
  }

  async findOne(...args: any[]): Promise<DepartmentDto | null> {
    const [id] = args as [number];
    const db = this.databaseFactory.getPublicClient();

    const department = await db.department.findUnique({
      where: { id },
    );

    return department as DepartmentDto | null;
  async update(...args: any[]): Promise<DepartmentDto> {
    const [id, updateDepartmentDto] = args as [number, UpdateDepartmentDto];
    const db = this.databaseFactory.getPublicClient();

    const department = await db.department.update({
      where: { id },
      data: updateDepartmentDto,
    );

    return department as DepartmentDto;
  async updateStatus(...args: any[]): Promise<{ id: number; status: number }> {
    const [id, updateStatusDto] = args as [number, UpdateDepartmentStatusDto];
    const db = this.databaseFactory.getPublicClient();

    const department = await db.department.update({
      where: { id },
      data: { status: updateStatusDto.status },
    );

    return { id: department.id, status: department.status };
  }

  async remove(...args: any[]): Promise<{ success: boolean }> {
    const [id] = args as [number];
    const db = this.databaseFactory.getPublicClient();

    await db.department.delete({
      where: { id },
    );

    return { success: true };
  }

  async checkNameExists(...args: any[]): Promise<boolean> {
    const [name, pid, _, excludeId] = args as [string, number, undefined, number?];
    const db = this.databaseFactory.getPublicClient();

    const existing = await db.department.findFirst({
      where: {
        name,
        pid,
        ...(excludeId && { id: { not: excludeId } ),
      },
    });

    return !!existing;
  private buildTree(departments: any[], pid = 0): DepartmentTreeDto[] {
    return departments
      .filter(dept => dept.pid === pid)
      .map(dept => ({
        ...dept,
        children: this.buildTree(departments, dept.id),
      ;);
  }
}