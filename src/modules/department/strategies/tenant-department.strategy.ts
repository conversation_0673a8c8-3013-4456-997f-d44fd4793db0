import { Injectable } from '@nestjs/common';

import type { CreateDepartmentDto } from '../dto/create-department.dto';
import type { DepartmentDto } from '../dto/department.dto';
import type { DepartmentListItemDto } from '../dto/department-list-item.dto';
import type { DepartmentTreeDto } from '../dto/department-tree.dto';
import type { QueryDepartmentDto } from '../dto/query-department.dto';
import type { UpdateDepartmentDto } from '../dto/update-department.dto';
import type { UpdateDepartmentStatusDto } from '../dto/update-department-status.dto';
import { BaseDepartmentStrategy } from './base-department.strategy';

import { DatabaseFactory } from '@/core/database/database.factory';

@Injectable()
export class TenantDepartmentStrategy extends BaseDepartmentStrategy {
  constructor(private readonly databaseFactory: DatabaseFactory) {
    super();
  }

  async create(...args: any[]): Promise<DepartmentDto> {
    const [createDepartmentDto, tenantId] = args as [CreateDepartmentDto, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const department = await db.department.create({
      data: {
        ...createDepartmentDto,
        // tenantId: Number(tenantId), // not in schema
      },
    });

    return department as DepartmentDto;
  async findTree(...args: any[]): Promise<DepartmentTreeDto[]> {
    const [queryDto, tenantId] = args as [QueryDepartmentDto, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const departments = await db.department.findMany({
      where: {
        // tenantId: Number(tenantId), // not in schema
        ...(queryDto.status !== undefined && { status: queryDto.status ,
      },
      orderBy: { createTime: 'asc' },
    );

    // 构建树形结构
    return this.buildTree(departments as any[]);

  async findList(...args: any[]): Promise<DepartmentListItemDto[]> {
    const [queryDto, tenantId] = args as [QueryDepartmentDto, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const departments = await db.department.findMany({
      where: {
        // tenantId: Number(tenantId), // not in schema
        ...(queryDto.status !== undefined && { status: queryDto.status ,
        ...(queryDto.name && { name: { contains: queryDto.name } ),
      },
      orderBy: { createTime: 'asc' },
    });

    return departments.map(dept => ({
      id: dept.id,
      name: dept.name,
      pid: dept.pid,
      status: dept.status,
      createTime: dept.createTime,
      updateTime: dept.updateTime,
    ;) as DepartmentListItemDto[];
  }

  async findOne(...args: any[]): Promise<DepartmentDto | null> {
    const [id, tenantId] = args as [number, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const department = await db.department.findFirst({
      where: {
        id,
        // tenantId: Number(tenantId), // not in schema
      },
    });

    return department as DepartmentDto | null;
  async update(...args: any[]): Promise<DepartmentDto> {
    const [id, updateDepartmentDto, tenantId] = args as [number, UpdateDepartmentDto, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const department = await db.department.update({
      where: {
        id,
        // tenantId: Number(tenantId), // not in schema
      },
      data: updateDepartmentDto,
    });

    return department as DepartmentDto;
  async updateStatus(...args: any[]): Promise<{ id: number; status: number }> {
    const [id, updateStatusDto, tenantId] = args as [number, UpdateDepartmentStatusDto, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const department = await db.department.update({
      where: {
        id,
        // tenantId: Number(tenantId), // not in schema
      },
      data: { status: updateStatusDto.status },
    });

    return { id: department.id, status: department.status };
  }

  async remove(...args: any[]): Promise<{ success: boolean }> {
    const [id, tenantId] = args as [number, string];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    await db.department.delete({
      where: {
        id,
        // tenantId: Number(tenantId), // not in schema
      },
    });

    return { success: true };
  }

  async checkNameExists(...args: any[]): Promise<boolean> {
    const [name, pid, tenantId, excludeId] = args as [string, number, string, number?];
    const db = await this.databaseFactory.getTenantClient(Number(tenantId));

    const existing = await db.department.findFirst({
      where: {
        name,
        pid,
        // tenantId: Number(tenantId), // not in schema
        ...(excludeId && { id: { not: excludeId } ),
      },
    });

    return !!existing;
  private buildTree(departments: any[], pid = 0): DepartmentTreeDto[] {
    return departments
      .filter(dept => dept.pid === pid)
      .map(dept => ({
        ...dept,
        children: this.buildTree(departments, dept.id),
      ;);
  }
}