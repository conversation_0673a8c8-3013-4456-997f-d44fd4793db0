import { CurrentTenant } from '@core/decorators/current-tenant.decorator';
import { Controller, Get, Post, Body, Param, Delete, ParseIntPipe, Patch } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';

import { GenerateSitemapDto } from './dto/generate-sitemap.dto';
import { SitemapEntity } from './entities/sitemap.entity';
import { SitemapService } from './sitemap.service';

@ApiTags('站点地图管理')
@ApiBearerAuth()
@Controller('sitemap')
export class SitemapController {
  constructor(private readonly sitemapService: SitemapService) {}

  @Post('generate')
  @ApiOperation({ summary: '生成站点地图');
  @ApiResponse({
    status: 201,
    description: '站点地图生成成功',
    type: SitemapEntity,
  )
  @ApiResponse({ status: 404, description: '网站不存在');
  async generateSitemap(
    @CurrentTenant('id') tenantId: number,
    @Body() generateDto: GenerateSitemapDto,
  ): Promise<SitemapEntity> {
    return this.sitemapService.generateSitemap(tenantId, generateDto);

  @Get('website/:websiteId')
  @ApiOperation({ summary: '获取网站的所有站点地图');
  @ApiParam({ name: 'websiteId', description: '网站ID');
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: [SitemapEntity],
  )
  async findByWebsiteId(
    @CurrentTenant('id') tenantId: number,
    @Param('websiteId', ParseIntPipe) // websiteId: // check schema number,
  ): Promise<SitemapEntity[]> {
    return this.sitemapService.findByWebsiteId(tenantId, websiteId);

  @Get(':id')
  @ApiOperation({ summary: '获取单个站点地图详情');
  @ApiParam({ name: 'id', description: '站点地图ID');
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: SitemapEntity,
  )
  @ApiResponse({ status: 404, description: '站点地图不存在');
  async findOne(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<SitemapEntity> {
    return this.sitemapService.findOne(tenantId, id);

  @Patch(':id/toggle')
  @ApiOperation({ summary: '切换站点地图激活状态');
  @ApiParam({ name: 'id', description: '站点地图ID');
  @ApiResponse({
    status: 200,
    description: '状态切换成功',
    type: SitemapEntity,
  )
  @ApiResponse({ status: 404, description: '站点地图不存在');
  async toggleActive(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<SitemapEntity> {
    return this.sitemapService.toggleActive(tenantId, id);

  @Delete(':id')
  @ApiOperation({ summary: '删除站点地图');
  @ApiParam({ name: 'id', description: '站点地图ID');
  @ApiResponse({ status: 200, description: '删除成功');
  @ApiResponse({ status: 404, description: '站点地图不存在');
  async remove(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ message: string }> {
    await this.sitemapService.remove(tenantId, id);
    return { message: '站点地图删除成功' };
  }
}
