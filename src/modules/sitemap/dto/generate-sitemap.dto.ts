import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsInt, IsBoolean, Min } from 'class-validator';

export class GenerateSitemapDto {
  @ApiProperty({ description: '所属网站ID');
  @IsInt()
  @Min(1)
  @Type(() => Number)
  // websiteId: // check schema number;

  @ApiProperty({
    description: '站点地图类型',
    enum: ['xml', 'html', 'txt'],
    default: 'xml',
  )
  @IsOptional()
  @IsString()
  type?: string = 'xml';

  @ApiProperty({ description: '是否包含图片', default: true 
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  includeImages?: boolean = true;

  @ApiProperty({ description: '是否包含视频', default: false 
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  includeVideos?: boolean = false;

  @ApiProperty({ description: '是否包含新闻', default: false 
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  includeNews?: boolean = false;

  @ApiProperty({ description: '最大URL数量', default: 50000 
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  maxUrls?: number = 50000;
}
