import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString, IsBoolean, IsArray, Min, Max, IsInt } from 'class-validator';

export class QueryComponentLibraryDto {
  @ApiProperty({ description: '页码', default: 1, required: false 
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({ description: '每页数量', default: 20, required: false 
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  limit?: number = 20;

  @ApiProperty({ description: '关键词搜索', required: false 
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({
    description: '组件类型过滤',
    examples: ['basic', 'layout', 'form', 'media', 'navigation', 'ecommerce'],
    required: false,
  )
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({
    description: '组件分类过滤',
    examples: ['header', 'footer', 'hero', 'content', 'sidebar', 'button', 'form'],
    required: false,
  )
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({
    description: '标签过滤',
    type: [String],
    required: false,
    example: ['响应式', '现代'],
  )
  @IsOptional()
  @IsArray()
  @IsString({ each: true 
  tags?: string[];

  @ApiProperty({ description: '是否只显示系统组件', required: false 
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isSystem?: boolean;

  @ApiProperty({ description: '是否只显示激活组件', required: false 
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean;

  @ApiProperty({ description: '创建者ID过滤', required: false 
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  userId?: number;

  @ApiProperty({
    description: '排序字段',
    enum: ['createTime', 'updateTime', 'name', 'sortOrder', 'usageCount'],
    required: false,
  )
  @IsOptional()
  @IsString()
  sortBy?: string = 'sortOrder';

  @ApiProperty({
    description: '排序方向',
    enum: ['asc', 'desc'],
    required: false,
  )
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'asc';

  @ApiProperty({ description: '是否包含关联数据', required: false 
  @IsOptional()
  @Type(() => Boolean)
  includeRelations?: boolean = false;
}
