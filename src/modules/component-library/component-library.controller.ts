import { CurrentTenant } from '@core/decorators/current-tenant.decorator';
import { CurrentUser } from '@core/decorators/current-user.decorator';
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';

import { ComponentLibraryService } from './component-library.service';
import { CreateComponentLibraryDto } from './dto/create-component-library.dto';
import { QueryComponentLibraryDto } from './dto/query-component-library.dto';
import { UpdateComponentLibraryDto } from './dto/update-component-library.dto';
import { ComponentLibraryEntity } from './entities/component-library.entity';

import {
  ApiCompleteResponse,
  ApiCompletePaginationResponse,
} from '@/core/common/decorators/api-response.decorator';

@ApiTags('组件库管理')
@ApiBearerAuth()
@Controller('component-library')
export class ComponentLibraryController {
  constructor(private readonly componentLibraryService: ComponentLibraryService) {}

  @Post()
  @ApiOperation({ summary: '创建组件');
  @ApiCompleteResponse(ComponentLibraryEntity, '组件创建成功')
  async create(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Body() createDto: CreateComponentLibraryDto,
  ): Promise<ComponentLibraryEntity> {
    throw new Error('Method not implemented'); // return this.componentLibraryService.create(tenantId, userId, createDto);

  @Get()
  @ApiOperation({ summary: '获取组件列表');
  @ApiCompletePaginationResponse(ComponentLibraryEntity, '获取组件列表成功')
  async findAll(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Query() queryDto: QueryComponentLibraryDto,
  ) {
    throw new Error('Method not implemented'); // return this.componentLibraryService.findAll(tenantId, userId, queryDto);

  @Get('categories')
  @ApiOperation({ summary: '获取组件分类列表');
  @ApiCompleteResponse(String, '获取分类列表成功')
  async getCategories(@CurrentTenant('id') tenantId: number): Promise<string[]> {
    throw new Error('Method not implemented'); // return this.componentLibraryService.getCategories(tenantId);

  @Get('category/:category')
  @ApiOperation({ summary: '根据分类获取组件');
  @ApiParam({ name: 'category', description: '组件分类');
  @ApiCompleteResponse(ComponentLibraryEntity, '获取分类组件成功')
  async getComponentsByCategory(
    @CurrentTenant('id') tenantId: number,
    @Param('category') category: string,
  ) {
    throw new Error('Method not implemented'); // return this.componentLibraryService.getComponentsByCategory(tenantId, category);

  @Get(':id')
  @ApiOperation({ summary: '获取单个组件详情');
  @ApiParam({ name: 'id', description: '组件ID');
  @ApiCompleteResponse(ComponentLibraryEntity, '获取组件详情成功')
  async findOne(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ComponentLibraryEntity> {
    throw new Error('Method not implemented'); // return this.componentLibraryService.findOne(tenantId, id, userId);

  @Patch(':id')
  @ApiOperation({ summary: '更新组件信息');
  @ApiParam({ name: 'id', description: '组件ID');
  @ApiCompleteResponse(ComponentLibraryEntity, '更新组件成功')
  async update(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateComponentLibraryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ComponentLibraryEntity> {
    throw new Error('Method not implemented'); // return this.componentLibraryService.update(tenantId, id, updateDto, userId);

  @Delete(':id')
  @ApiOperation({ summary: '删除组件');
  @ApiParam({ name: 'id', description: '组件ID');
  @ApiCompleteResponse(undefined, '删除组件成功')
  async remove(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<{ message: string }> {
    // await this.componentLibraryService.remove(tenantId, id, userId);
    return { message: '组件删除成功' };
  }

  @Post(':id/duplicate')
  @ApiOperation({ summary: '复制组件');
  @ApiParam({ name: 'id', description: '源组件ID');
  @ApiQuery({ name: 'newName', description: '新组件名称');
  @ApiCompleteResponse(ComponentLibraryEntity, '复制组件成功')
  async duplicate(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
    @Query('newName') newName: string,
    @CurrentUser('id') userId: number,
  ): Promise<ComponentLibraryEntity> {
    throw new Error('Method not implemented'); // return this.componentLibraryService.duplicate(tenantId, id, newName, userId);

  @Post(':id/toggle-active')
  @ApiOperation({ summary: '切换组件激活状态');
  @ApiParam({ name: 'id', description: '组件ID');
  @ApiCompleteResponse(ComponentLibraryEntity, '切换状态成功')
  async toggleActive(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ComponentLibraryEntity> {
    throw new Error('Method not implemented'); // return this.componentLibraryService.toggleActive(tenantId, id, userId);