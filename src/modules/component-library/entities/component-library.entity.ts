import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsObject, IsDate, IsInt, IsBoolean } from 'class-validator';

export class ComponentLibraryEntity {
  @ApiProperty({ description: '组件ID');
  id: number;

  @ApiProperty({ description: '组件名称');
  @IsString()
  name: string;

  @ApiProperty({ description: '组件类型', default: 'custom');
  @IsString()
  type: string;

  @ApiProperty({ description: '组件分类', required: false 
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({ description: '组件配置', type: 'object');
  @IsObject()
  config: JsonValue;

  @ApiProperty({ description: '是否可见', default: true 
  @IsBoolean()
  visible: boolean;

  @ApiProperty({ description: '排序权重', default: 0 
  @IsInt()
  sortOrder: number;

  @ApiProperty({ description: '元数据', type: 'object', required: false 
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '租户ID', required: false 
  @IsOptional()
  tenantId?: number;

  @ApiProperty({ description: '创建时间');
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({ description: '更新时间');
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;
}