import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsOptional, IsInt, IsEnum, IsNotEmpty, MaxLength } from 'class-validator';

export enum PermissionStatus {
  DISABLED = 0,
  ENABLED = 1,
}

export class CreatePermissionDto {
  @ApiProperty({ description: '权限名称');
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  name: string;

  @ApiProperty({ description: '权限码，如 system:user:create');
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  code: string;

  @ApiPropertyOptional({ description: '权限描述');
  @IsOptional()
  @IsString()
  @MaxLength(200)
  description?: string;

  @ApiPropertyOptional({
    description: '状态：0-禁用，1-启用',
    enum: PermissionStatus,
    default: PermissionStatus.ENABLED,
  )
  @IsOptional()
  @IsEnum(PermissionStatus)
  @Transform(({ value ) => parseInt(value))
  status?: PermissionStatus = PermissionStatus.ENABLED;
}

export class UpdatePermissionDto {
  @ApiPropertyOptional({ description: '权限名称');
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  name?: string;

  @ApiPropertyOptional({ description: '权限码，如 system:user:create');
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  code?: string;

  @ApiPropertyOptional({ description: '权限描述');
  @IsOptional()
  @IsString()
  @MaxLength(200)
  description?: string;

  @ApiPropertyOptional({ description: '状态：0-禁用，1-启用', enum: PermissionStatus 
  @IsOptional()
  @IsEnum(PermissionStatus)
  @Transform(({ value ) => parseInt(value))
  status?: PermissionStatus;
}

export class PermissionDto {
  @ApiProperty({ description: '权限ID');
  id: number;

  @ApiProperty({ description: '权限名称');
  name: string;

  @ApiProperty({ description: '权限码');
  code: string;

  @ApiPropertyOptional({ description: '权限描述');
  description?: string;

  @ApiProperty({ description: '状态：0-禁用，1-启用', enum: PermissionStatus 
  status: PermissionStatus;

  @ApiProperty({ description: '创建时间');
  createdAt: Date;

  @ApiProperty({ description: '更新时间');
  updatedAt: Date;
}

export class AssignPermissionToRoleDto {
  @ApiProperty({ description: '角色ID');
  @IsString()
  @IsNotEmpty()
  roleId: number;

  @ApiProperty({ description: '权限ID列表', type: [Number]);
  @IsInt({ each: true 
  permissionIds: number[];
}

export class PermissionQueryDto {
  @ApiPropertyOptional({ description: '权限名称');
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '权限码');
  @IsOptional()
  @IsString()
  code?: string;

  @ApiPropertyOptional({ description: '状态：0-禁用，1-启用');
  @IsOptional()
  @Transform(({ value  => parseInt(value))
  @IsEnum(PermissionStatus)
  status?: PermissionStatus;
}
