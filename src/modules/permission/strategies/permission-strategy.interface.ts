import {
  CreatePermissionDto,
  UpdatePermissionDto,
  PermissionDto,
  AssignPermissionToRoleDto,
  PermissionQueryDto,
} from '../dto/permission.dto';

export interface PermissionStrategy {
  /**
   * 获取权限列表
   * @param query 查询条件
   * @param tenantId 租户ID（可选，用于租户权限）
   * @returns 权限列表
   */
  getPermissions(query?: PermissionQueryDto, tenantId?: number): Promise<PermissionDto[]>;

  /**
   * 根据ID获取权限
   * @param id 权限ID
   * @param tenantId 租户ID（可选，用于租户权限）
   * @returns 权限信息
   */
  getPermissionById(id: number, tenantId?: number): Promise<PermissionDto | null>;

  /**
   * 创建权限
   * @param createPermissionDto 创建权限DTO
   * @param tenantId 租户ID（可选，用于租户权限）
   * @returns 创建的权限信息
   */
  createPermission(
    createPermissionDto: CreatePermissionDto,
    tenantId?: number,
  ): Promise<PermissionDto>;

  /**
   * 更新权限
   * @param id 权限ID
   * @param updatePermissionDto 更新权限DTO
   * @param tenantId 租户ID（可选，用于租户权限）
   * @returns 更新后的权限信息
   */
  updatePermission(
    id: number,
    updatePermissionDto: UpdatePermissionDto,
    tenantId?: number,
  ): Promise<PermissionDto>;

  /**
   * 删除权限
   * @param id 权限ID
   * @param tenantId 租户ID（可选，用于租户权限）
   * @returns 删除成功标志
   */
  deletePermission(id: number, tenantId?: number): Promise<boolean>;

  /**
   * 检查权限码是否存在
   * @param code 权限码
   * @param excludeId 排除的权限ID（用于更新时检查）
   * @param tenantId 租户ID（可选，用于租户权限）
   * @returns 是否存在
   */
  checkPermissionCodeExists(code: string, excludeId?: number, tenantId?: number): Promise<boolean>;

  /**
   * 分配权限给角色
   * @param assignDto 分配权限DTO
   * @param tenantId 租户ID（可选，用于租户权限）
   * @returns 操作成功标志
   */
  assignPermissionsToRole(
    assignDto: AssignPermissionToRoleDto,
    tenantId?: number,
  ): Promise<boolean>;

  /**
   * 获取角色的权限列表
   * @param roleId 角色ID
   * @param tenantId 租户ID（可选，用于租户权限）
   * @returns 权限列表
   */
  getRolePermissions(roleId: number, tenantId?: number): Promise<PermissionDto[]>;

  /**
   * 移除角色的权限
   * @param roleId 角色ID
   * @param permissionId 权限ID
   * @param tenantId 租户ID（可选，用于租户权限）
   * @returns 操作成功标志
   */
  removePermissionFromRole(
    roleId: number,
    permissionId: number,
    tenantId?: number,
  ): Promise<boolean>;
}
