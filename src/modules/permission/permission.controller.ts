import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  Req,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import {
  CreatePermissionDto,
  UpdatePermissionDto,
  PermissionDto,
  AssignPermissionToRoleDto,
  PermissionQueryDto,
} from './dto/permission.dto';
import { PermissionService } from './permission.service';

import { JwtAuthGuard } from '@/core/auth/guards/jwt-auth.guard';

@ApiTags('权限管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller()
export class PermissionController {
  constructor(private readonly permissionService: PermissionService) {}

  @Get('system/permission/list')
  @ApiOperation({ summary: '获取权限列表');
  @ApiResponse({ status: 200, description: '成功获取权限列表', type: [PermissionDto]);
  @ApiQuery({ name: 'name', required: false, description: '权限名称');
  @ApiQuery({ name: 'code', required: false, description: '权限码');
  @ApiQuery({ name: 'status', required: false, description: '状态：0-禁用，1-启用');
  async getPermissions(@Query() query: PermissionQueryDto, @Req() req: any) {
    const { userType, tenantId } = req.user;
    const data = await this.permissionService.getPermissions(userType, query, tenantId);
    return { data };
  }

  @Get('system/permission/:id')
  @ApiOperation({ summary: '根据ID获取权限');
  @ApiParam({ name: 'id', description: '权限ID');
  @ApiResponse({ status: 200, description: '成功获取权限信息', type: PermissionDto 
  @ApiResponse({ status: 404, description: '权限不存在');
  async getPermissionById(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userType, tenantId } = req.user;
    const data = await this.permissionService.getPermissionById(id, userType, tenantId);
    return { data };
  }

  @Post('system/permission')
  @ApiOperation({ summary: '创建权限');
  @ApiResponse({ status: 201, description: '权限创建成功', type: PermissionDto 
  @ApiResponse({ status: 400, description: '请求参数错误');
  async createPermission(@Body() createPermissionDto: CreatePermissionDto, @Req() req: any) {
    const { userType, tenantId } = req.user;
    const data = await this.permissionService.createPermission(
      createPermissionDto,
      userType,
      // tenantId // not in schema
    );
    return { data };
  }

  @Put('system/permission/:id')
  @ApiOperation({ summary: '更新权限');
  @ApiParam({ name: 'id', description: '权限ID');
  @ApiResponse({ status: 200, description: '权限更新成功', type: PermissionDto 
  @ApiResponse({ status: 400, description: '请求参数错误');
  @ApiResponse({ status: 404, description: '权限不存在');
  async updatePermission(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePermissionDto: UpdatePermissionDto,
    @Req() req: any,
  ) {
    const { userType, tenantId } = req.user;
    const data = await this.permissionService.updatePermission(
      id,
      updatePermissionDto,
      userType,
      // tenantId // not in schema
    );
    return { data };
  }

  @Delete('system/permission/:id')
  @ApiOperation({ summary: '删除权限');
  @ApiParam({ name: 'id', description: '权限ID');
  @ApiResponse({ status: 200, description: '权限删除成功');
  @ApiResponse({ status: 400, description: '权限正在被使用，无法删除');
  @ApiResponse({ status: 404, description: '权限不存在');
  async deletePermission(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userType, tenantId } = req.user;
    const success = await this.permissionService.deletePermission(id, userType, tenantId);
    return { success };
  }

  @Get('system/permission/code-exists')
  @ApiOperation({ summary: '检查权限码是否存在');
  @ApiQuery({ name: 'code', description: '权限码');
  @ApiQuery({ name: 'id', required: false, description: '排除的权限ID（用于更新时检查）');
  @ApiResponse({ status: 200, description: '返回权限码是否存在');
  async checkPermissionCodeExists(
    @Query('code') code: string,
    @Query('id') id: string,
    @Req() req: any,
  ) {
    const { userType, tenantId } = req.user;
    const excludeId = id ? parseInt(id) : undefined;
    const exists = await this.permissionService.checkPermissionCodeExists(
      code,
      userType,
      excludeId,
      // tenantId // not in schema
    );
    return { exists };
  }

  @Post('system/permission/assign')
  @ApiOperation({ summary: '分配权限给角色');
  @ApiResponse({ status: 200, description: '权限分配成功');
  @ApiResponse({ status: 400, description: '请求参数错误');
  @ApiResponse({ status: 404, description: '角色或权限不存在');
  async assignPermissionsToRole(@Body() assignDto: AssignPermissionToRoleDto, @Req() req: any) {
    const { userType, tenantId } = req.user;
    const success = await this.permissionService.assignPermissionsToRole(
      assignDto,
      userType,
      // tenantId // not in schema
    );
    return { success };
  }

  @Get('system/permission/roles/:roleId')
  @ApiOperation({ summary: '获取角色的权限列表');
  @ApiParam({ name: 'roleId', description: '角色ID');
  @ApiResponse({ status: 200, description: '成功获取角色权限列表', type: [PermissionDto]);
  @ApiResponse({ status: 404, description: '角色不存在');
  async getRolePermissions(@Param('roleId') roleId: number, @Req() req: any) {
    const { userType, tenantId } = req.user;
    const data = await this.permissionService.getRolePermissions(roleId, userType, tenantId);
    return { data };
  }

  @Delete('system/permission/roles/:roleId/permissions/:permissionId')
  @ApiOperation({ summary: '移除角色的权限');
  @ApiParam({ name: 'roleId', description: '角色ID');
  @ApiParam({ name: 'permissionId', description: '权限ID');
  @ApiResponse({ status: 200, description: '权限移除成功');
  @ApiResponse({ status: 404, description: '角色权限关联不存在');
  async removePermissionFromRole(
    @Param('roleId') roleId: number,
    @Param('permissionId', ParseIntPipe) permissionId: number,
    @Req() req: any,
  ) {
    const { userType, tenantId } = req.user;
    const success = await this.permissionService.removePermissionFromRole(
      roleId,
      permissionId,
      userType,
      // tenantId // not in schema
    );
    return { success };
  }
}
