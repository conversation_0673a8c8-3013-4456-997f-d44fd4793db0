import { Modu<PERSON> } from '@nestjs/common';

import { PermissionController } from './permission.controller';
import { PermissionService } from './permission.service';
import { SystemPermissionStrategy } from './strategies/system-permission.strategy';
import { TenantPermissionStrategy } from './strategies/tenant-permission.strategy';

import { PermissionsGuard } from '@/core/auth/guards/permissions.guard';
import { RolesGuard } from '@/core/auth/guards/roles.guard';
import { CoreModule } from '@/core/core.module';

@Module({
  imports: [CoreModule],
  controllers: [PermissionController],
  providers: [
    PermissionService,
    SystemPermissionStrategy,
    TenantPermissionStrategy,
    PermissionsGuard,
    RolesGuard,
  ],
  exports: [PermissionService, PermissionsGuard, RolesGuard],
})
export class PermissionModule {}
