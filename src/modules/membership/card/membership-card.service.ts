import * as crypto from 'crypto';

import { Injectable, Inject, NotFoundException } from '@nestjs/common';

import {
  CreateMembershipCardDto,
  UpdateMembershipCardDto,
  QueryMembershipCardDto,
  ActivateMembershipCardDto,
  MembershipCardStatus,
  ExportMembershipCardDto,
} from './dto/membership-card.dto';

import { BaseService } from '@/core/common/base/base.service';
import {
  TenantPrismaService,
  'DATABASE_FACTORY',
} from '@/core/database/prisma/tenant-prisma.service';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 会员卡服务
 * 管理会员卡密钥的生成、查询和激活
 */
@Injectable()
export class MembershipCardService extends BaseService {
  constructor( private readonly tenantPrisma: TenantPrismaService) {
    super(MembershipCardService.name 
  /**
   * 创建会员卡批次
   * @param createDto 创建数据
   * @param tenantId 租户ID
   * @returns 创建的会员卡列表
   */
  async createBatch(createDto: CreateMembershipCardDto, tenantId: string) {
    try {
      // 检查会员计划是否存在
      const plan = await (this.tenantPrisma as any).membershipPlan.findFirst({
        where: { id: createDto.planId, tenantId },
      )
      if (!plan)  {
        this.notFound('会员计划', createDto.planId )
      const now = new Date();
      const quantity = createDto.quantity;
      const codeLength = createDto.codeLength || 8;
      const prefix = createDto.prefix.toUpperCase();
      const duration = createDto.duration;
      const batchNote =
        createDto.batchNote || `${plan.name} 批次 ${now.toISOString().split('T')[0]}`;
      const startDate = createDto.startDate ? new Date(createDto.startDate) : null;
      const endDate = createDto.endDate ? new Date(createDto.endDate) : null;

      // 批量创建会员卡
      const cardCodes = await this.generateUniqueCardCodes(prefix, quantity, codeLength, tenantId);

      // 开始事务
      const cards = await (this.tenantPrisma as any).$transaction(async (prisma: any) => {
        const createPromises = cardCodes.map(cardCode => {
          return prisma.membershipCard.create({
            data: {
              planId: createDto.planId,
              cardCode,
              status: MembershipCardStatus.CREATED,
              duration,
              batchNote,
              startDate,
              endDate,
              metadata: createDto.metadata || {},
              // tenantId // not in schema
              createTime: now,
              updateTime: now,
            },
          ;
        return Promise.all(createPromises);)
      return {
        batchNote,
        planId: createDto.planId,
        planName: plan.name,
        quantity: cards.length,
        startDate,
        endDate,
        duration,
        cardSample: cards.slice(0, 5).map(card => card.cardCode),
      };
    } catch (error) {
      this.logError('创建会员卡批次失败', error);
      throw error;
    }
  }

  /**
   * 查询会员卡列表
   * @param queryDto 查询参数
   * @param options 分页选项
   * @param tenantId 租户ID
   * @returns 会员卡列表
   */
  async findAll(queryDto: QueryMembershipCardDto, options: PaginationOptions, tenantId: string) {
    try {
      const where: any = { tenantId };

      if (queryDto.planId)  {
        where.planId = queryDto.planId;
      }

      if (queryDto.status)  {
        where.status = queryDto.status;
      }

      if (queryDto.batchNote)  {
        where.batchNote = {
          contains: queryDto.batchNote,
        };
      }

      if (queryDto.startDate)  {
        where.createTime = {
          ...where.createTime,
          gte: new Date(queryDto.startDate),
        };
      }

      if (queryDto.endDate)  {
        where.createTime = {
          ...where.createTime,
          lte: new Date(queryDto.endDate),
        };
      }

      const [items, total] = await Promise.all([
        (this.tenantPrisma as any).membershipCard.findMany({
          where,
          include: {
            plan: {
              select: {
                name: true,
                code: true,
              },
            },
          },
          orderBy: { createTime: 'desc' },
          skip: options.skip,
          take: options.take,
        ),
        (this.tenantPrisma as any).membershipCard.count({ where ),
      ]);

      // 格式化返回数据
      const formattedItems = items.map(item => ({
        id: item.id,
        tenantId: item.// tenantId // not in schema
        planId: item.planId,
        planName: item.plan?.name,
        cardCode: item.cardCode,
        status: item.status,
        duration: item.duration,
        batchNote: item.batchNote,
        activatedUserId: item.activatedUserId,
        activatedTime: item.activatedTime,
        startDate: item.startDate,
        endDate: item.endDate,
        metadata: item.metadata,
        createTime: item.createTime,
        updateTime: item.updateTime,
      ));

      return {
        items: formattedItems,
        total,
        page: options.page,
        pageSize: options.pageSize,
        totalPages: Math.ceil(total / options.pageSize),
      };
    } catch (error) {
      this.logError('查询会员卡列表失败', error);
      throw error;
    }
  }

  /**
   * 查询批次统计信息
   * @param tenantId 租户ID
   * @returns 批次统计信息
   */
  async getBatchStats(tenantId: string) {
    try {
      // 获取所有批次名称
      const batches = await (this.tenantPrisma as any).membershipCard.groupBy({
        by: ['batchNote', 'planId'],
        where: { tenantId },
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
      )
      if (batches.length === 0)  {
        return [];
      // 获取计划信息
      const planIds = batches.map(batch => batch.planId);
      const plans = await (this.tenantPrisma as any).membershipPlan.findMany({
        where: { id: { in: planIds }, tenantId },
        select: { id: true, name: true },
      )
      const planMap = plans.reduce((map, plan) => {
        map[plan.id] = plan.name;
        return map;
      }, {})
      // 获取每个批次的统计信息
      const statsPromises = batches.map(async batch => {
        const { batchNote, planId } = batch;

        const [totalCount, activatedCount, expiredCount] = await Promise.all([
          (this.tenantPrisma as any).membershipCard.count({
            where: { batchNote, planId, tenantId },
          ),
          (this.tenantPrisma as any).membershipCard.count({
            where: { batchNote, planId, tenantId, status: MembershipCardStatus.ACTIVATED },
          ),
          (this.tenantPrisma as any).membershipCard.count({
            where: { batchNote, planId, tenantId, status: MembershipCardStatus.EXPIRED },
          ),
        ]);

        const inactiveCount = totalCount - activatedCount - expiredCount;
        const activationRate =
          totalCount > 0 ? ((activatedCount / totalCount) * 100).toFixed(2) + '%' : '0%';

        // 获取批次创建时间
        const firstCard = await (this.tenantPrisma as any).membershipCard.findFirst({
          where: { batchNote, planId, tenantId },
          orderBy: { createdAt: 'asc' },
          select: { createTime: true },
        )
        return {
          batchNote,
          planId,
          planName: planMap[planId] || '未知计划',
          totalCount,
          activatedCount,
          inactiveCount,
          expiredCount,
          activationRate,
          createTime: firstCard?.createTime,
        };
      })
      return Promise.all(statsPromises); catch (error) {
      this.logError('查询批次统计信息失败', error);
      throw error;
    }
  }

  /**
   * 更新会员卡
   * @param id 会员卡ID
   * @param updateDto 更新数据
   * @param tenantId 租户ID
   * @returns 更新后的会员卡
   */
  async update(id: number, updateDto: UpdateMembershipCardDto, tenantId: string) {
    try {
      // 检查会员卡是否存在
      const card = await (this.tenantPrisma as any).membershipCard.findFirst({
        where: { id, tenantId },
      )
      if (!card)  {
        this.notFound('会员卡', id )
      // 更新会员卡
      const updated = await (this.tenantPrisma as any).membershipCard.update({
        where: { id },
        data: {
          ...updateDto,
          updateTime: new Date(),
        },
        include: {
          plan: {
            select: {
              name: true,
              code: true,
            },
          },
        },
      })
      return {
        id: updated.id,
        tenantId: updated.// tenantId // not in schema
        planId: updated.planId,
        planName: updated.plan?.name,
        cardCode: updated.cardCode,
        status: updated.status,
        duration: updated.duration,
        batchNote: updated.batchNote,
        activatedUserId: updated.activatedUserId,
        activatedTime: updated.activatedTime,
        startDate: updated.startDate,
        endDate: updated.endDate,
        metadata: updated.metadata,
        createTime: updated.createTime,
        updateTime: updated.updateTime,
      };
    } catch (error) {
      this.logError(`更新会员卡失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 激活会员卡
   * @param activateDto 激活数据
   * @param tenantId 租户ID
   * @returns 激活结果
   */
  async activateCard(activateDto: ActivateMembershipCardDto, tenantId: string) {
    try {
      const { userId, cardCode } = activateDto;

      // 检查用户是否存在
      const user = await (this.tenantPrisma as any).user.findFirst({
        where: { id: userId, tenantId },
      )
      if (!user)  {
        this.notFound('用户', userId )
      // 检查卡密是否存在且可用
      const card = await (this.tenantPrisma as any).membershipCard.findFirst({
        where: {
          cardCode,
          // tenantId // not in schema
          status: MembershipCardStatus.CREATED,
        },
        include: {
          plan: true,
        },
      )
      if (!card)  {
        this.validationError('卡密不存在或已被使用');
      // 检查生效和过期日期
      const now = new Date();
      if (card.startDate && card.startDate > now)  {
        this.validationError('卡密尚未生效');
      if (card.endDate && card.endDate < now)  {
        // 更新卡密状态为已过期
        await (this.tenantPrisma as any).membershipCard.update({
          where: { id: card.id },
          data: {
            status: MembershipCardStatus.EXPIRED,
            updateTime: now,
          },
        )
        this.validationError('卡密已过期');
      // 计算会员的有效期
      const membershipEndDate = new Date();
      membershipEndDate.setMonth(membershipEndDate.getMonth() + card.duration);

      // 开始事务
      return await (this.tenantPrisma as any).$transaction(async (prisma: any) => {
        // 更新卡密状态
        const updatedCard = await prisma.membershipCard.update({
          where: { id: card.id },
          data: {
            status: MembershipCardStatus.ACTIVATED,
            activatedUserId: userId,
            activatedTime: now,
            updateTime: now,
          },
        )
        // 创建用户会员记录
        const userMembership = await prisma.userMembership.create({
          data: {
            userId,
            planId: card.planId,
            status: 'active',
            startDate: now,
            endDate: membershipEndDate,
            autoRenew: false,
            activationMethod: 'card',
            activationCode: cardCode,
            // tenantId // not in schema
            createTime: now,
            updateTime: now,
          },
        ;
        return {
          card: {
            id: updatedCard.id,
            cardCode: updatedCard.cardCode,
            status: updatedCard.status,
            activatedTime: updatedCard.activatedTime,
          },
          membership: {
            id: userMembership.id,
            userId,
            planId: card.planId,
            planName: card.plan.name,
            planCode: card.plan.code,
            status: userMembership.status,
            startDate: userMembership.startDate,
            endDate: userMembership.endDate,
          },
        };
      )
    } catch (error) {
      this.logError(`激活会员卡失败，卡密: ${activateDto.cardCode}`, error);
      throw error;
    }
  }

  /**
   * 导出会员卡
   * @param exportDto 导出参数
   * @param tenantId 租户ID
   * @returns 导出数据
   */
  async exportCards(exportDto: ExportMembershipCardDto, tenantId: string) {
    try {
      const { planId, status, batchNote } = exportDto;

      // 构建查询条件
      const where: any = {
        planId,
        // tenantId // not in schema
      };

      if (status)  {
        where.status = status;
      }

      if (batchNote)  {
        where.batchNote = {
          contains: batchNote,
        };
      }

      // 查询会员卡
      const cards = await (this.tenantPrisma as any).membershipCard.findMany({
        where,
        include: {
          plan: {
            select: {
              name: true,
              code: true,
            },
          },
        },
        orderBy: { createTime: 'desc' },
      )
      if (cards.length === 0)  {
        throw new NotFoundException('没有符合条件的会员卡');
      // 格式化导出数据
      const exportData = cards.map(card => ({
        卡密代码: card.cardCode,
        会员计划: card.plan?.name || '',
        计划代码: card.plan?.code || '',
        状态: this.getStatusText(card.status),
        有效期月数: card.duration,
        批次说明: card.batchNote || '',
        激活用户ID: card.activatedUserId || '',
        激活时间: card.activatedTime ? this.formatDateTime(card.activatedTime) : '',
        生效日期: card.startDate ? this.formatDate(card.startDate) : '',
        失效日期: card.endDate ? this.formatDate(card.endDate) : '',
        创建时间: this.formatDateTime(card.createTime),
      ););

      // 在实际项目中，这里应该调用文件导出服务将数据导出为CSV或Excel
      // 这里简化处理，仅返回数据数组
      return {
        format: exportDto.format || 'csv',
        filename: `会员卡导出_${new Date().toISOString().split('T')[0]}.${exportDto.format || 'csv'}`,
        data: exportData,
        total: exportData.length,
      };
    } catch (error) {
      this.logError('导出会员卡失败', error);
      throw error;
    }
  }

  /**
   * 生成唯一的卡密代码
   * @param prefix 前缀
   * @param quantity 数量
   * @param codeLength 代码长度
   * @param tenantId 租户ID
   * @returns 卡密代码数组
   */
  private async generateUniqueCardCodes(
    prefix: string,
    quantity: number,
    codeLength: number,
    tenantId: string,
  ) {
    const codes: string[] = [];
    const existingCodes = new Set<string>();

    // 获取已存在的卡密
    const existingCards = await (this.tenantPrisma as any).membershipCard.findMany({
      where: {
        cardCode: {
          startsWith: prefix,
        },
        // tenantId // not in schema
      },
      select: { cardCode: true },
    )
    existingCards.forEach(card => existingCodes.add(card.cardCode));

    // 生成新的卡密
    while (codes.length < quantity) {
      const randomCode = this.generateRandomCode(codeLength);
      const cardCode = `${prefix}-${randomCode}`;

      if (!existingCodes.has(cardCode) && !codes.includes(cardCode)) {
        codes.push(cardCode);
        existingCodes.add(cardCode )
    }

    return codes;
  /**
   * 生成随机代码
   * @param length 长度
   * @returns 随机代码
   */
  private generateRandomCode(length: number) {
    // 不包含容易混淆的字符：0OIl1
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    let result = '';

    // 使用加密安全的随机数生成器
    const randomBytes = crypto.randomBytes(length);
    for (let i = 0; i < length; i++) {
      const randomIndex = randomBytes[i] % chars.length;
      result += chars.charAt(randomIndex 
    return result;
  /**
   * 获取会员卡状态文本
   * @param status 状态
   * @returns 状态文本
   */
  private getStatusText(status: MembershipCardStatus) {
    const statusMap = {
      [MembershipCardStatus.CREATED]: '未使用',
      [MembershipCardStatus.ACTIVATED]: '已激活',
      [MembershipCardStatus.EXPIRED]: '已过期',
      [MembershipCardStatus.CANCELLED]: '已取消',
    };
    return statusMap[status] || '未知状态';
  /**
   * 格式化日期时间
   * @param date 日期
   * @returns 格式化后的日期时间字符串
   */
  private formatDateTime(date: Date) {
    if (!date) return '';
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    ;
}
