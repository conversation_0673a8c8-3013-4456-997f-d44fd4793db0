import { Module } from '@nestjs/common';

import {
  MembershipCardController,
  UserMembershipCardController,
} from './membership-card.controller';
import { MembershipCardService } from './membership-card.service';

import { PrismaModule } from '@/core/database/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [MembershipCardController, UserMembershipCardController],
  providers: [MembershipCardService],
  exports: [MembershipCardService],
})
export class MembershipCardModule {}
