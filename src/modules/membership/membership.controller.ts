import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { MembershipService } from './membership.service';

import { BaseController } from '@/core/common/base/base.controller';

@Controller('membership')
@ApiTags('membership')
export class MembershipController extends BaseController {
  constructor(private readonly membershipService: MembershipService) {
    super();
  }

  // TODO: 实现会员相关接口
  // - 用户获取可用会员计划
  // - 用户订阅会员计划
  // - 用户查询会员信息
  // - 用户会员权益查询
}
