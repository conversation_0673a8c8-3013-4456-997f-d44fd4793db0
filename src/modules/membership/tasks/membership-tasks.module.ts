import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';

import { MembershipTasksService } from './membership-tasks.service';

import { TenantModule } from '@/modules/tenant/tenant.module';

@Module({
  imports: [ScheduleModule.forRoot(), TenantModule],
  providers: [MembershipTasksService],
  exports: [MembershipTasksService],
})
export class MembershipTasksModule {}
