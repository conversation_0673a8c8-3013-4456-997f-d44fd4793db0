/*
 * @Author: ya<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-27 22:41:59
 * @LastEditors: yangwenqiang <EMAIL>
 * @LastEditTime: 2025-06-01 00:49:53
 * @FilePath: /multi-tenant-nestjs/src/modules/membership/plan/membership-plan.module.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Module } from '@nestjs/common';

import { MembershipPlanController } from './membership-plan.controller';
import { MembershipPlanService } from './membership-plan.service';
import { TenantMembershipPlanStrategy } from './strategies/tenant-membership-plan.strategy';

import { PrismaModule } from '@/core/database/prisma/prisma.module';

/**
 * 会员计划模块
 */
@Module({
  imports: [PrismaModule],
  controllers: [MembershipPlanController],
  providers: [MembershipPlanService, TenantMembershipPlanStrategy],
  exports: [MembershipPlanService],
})
export class MembershipPlanModule {}
