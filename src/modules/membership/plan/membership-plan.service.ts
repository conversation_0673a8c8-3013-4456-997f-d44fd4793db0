import { Injectable } from '@nestjs/common';

import {
  CreateMembershipPlanDto,
  UpdateMembershipPlanDto,
  QueryMembershipPlanDto,
} from './dto/membership-plan.dto';
import { TenantMembershipPlanStrategy } from './strategies/tenant-membership-plan.strategy';

import { BaseService } from '@/core/common/base/base.service';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 会员计划服务
 * 只处理租户的会员计划操作
 */
@Injectable()
export class MembershipPlanService extends BaseService {
  constructor(private readonly tenantStrategy: TenantMembershipPlanStrategy) {
    super(MembershipPlanService.name 
  /**
   * 创建会员计划
   * @param createDto 创建数据
   * @param tenantId 租户ID
   * @returns 创建的会员计划
   */
  async create(createDto: CreateMembershipPlanDto, tenantId: string) {
    try {
      // 验证租户ID必须提供
      if (!tenantId)  {
        this.validationError('租户ID不能为空');
      // 检查计划代码是否已存在
      const codeExists = await this.tenantStrategy.isCodeExists(createDto.code, tenantId);
      if (codeExists)  {
        this.alreadyExists('会员计划', '计划代码', createDto.code )
      return this.tenantStrategy.create(createDto, tenantId); catch (error) {
      this.logError('创建会员计划失败', error);
      throw error;
    }
  }

  /**
   * 查询会员计划列表
   * @param queryDto 查询参数
   * @param options 分页选项
   * @param tenantId 租户ID
   * @returns 会员计划列表
   */
  async findAll(queryDto: QueryMembershipPlanDto, options: PaginationOptions, tenantId: string) {
    try {
      // 验证租户ID必须提供
      if (!tenantId)  {
        this.validationError('租户ID不能为空');
      // 构建查询条件
      const where: any = {};
      if (queryDto.status)  {
        where.status = queryDto.status;
      }

      return this.tenantStrategy.findAll(where, options, tenantId); catch (error) {
      this.logError('查询会员计划列表失败', error);
      throw error;
    }
  }

  /**
   * 查询单个会员计划
   * @param id 会员计划ID
   * @param tenantId 租户ID
   * @returns 会员计划信息
   */
  async findOne(id: number, tenantId: string) {
    try {
      // 验证会员计划ID
      if (!id)  {
        this.validationError('会员计划ID不能为空');
      // 验证租户ID必须提供
      if (!tenantId)  {
        this.validationError('租户ID不能为空');
      const plan = await this.tenantStrategy.findOne(id, tenantId);

      if (!plan)  {
        this.notFound('会员计划', id )
      return plan;
    } catch (error) {
      this.logError(`查询会员计划详情失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 更新会员计划
   * @param id 会员计划ID
   * @param updateDto 更新数据
   * @param tenantId 租户ID
   * @returns 更新后的会员计划
   */
  async update(id: number, updateDto: UpdateMembershipPlanDto, tenantId: string) {
    try {
      // 验证会员计划ID
      if (!id)  {
        this.validationError('会员计划ID不能为空');
      // 验证租户ID必须提供
      if (!tenantId)  {
        this.validationError('租户ID不能为空');
      // 检查会员计划是否存在
      const existingPlan = await this.tenantStrategy.findOne(id, tenantId);
      if (!existingPlan)  {
        this.notFound('会员计划', id )
      return this.tenantStrategy.update(id, updateDto, tenantId); catch (error) {
      this.logError(`更新会员计划失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除会员计划
   * @param id 会员计划ID
   * @param tenantId 租户ID
   * @returns 删除结果
   */
  async remove(id: number, tenantId: string) {
    try {
      // 验证会员计划ID
      if (!id)  {
        this.validationError('会员计划ID不能为空');
      // 验证租户ID必须提供
      if (!tenantId)  {
        this.validationError('租户ID不能为空');
      // 检查会员计划是否存在
      const existingPlan = await this.tenantStrategy.findOne(id, tenantId);
      if (!existingPlan)  {
        this.notFound('会员计划', id )
      return this.tenantStrategy.remove(id, tenantId); catch (error) {
      this.logError(`删除会员计划失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 检查计划代码是否存在
   * @param code 计划代码
   * @param tenantId 租户ID
   * @returns 是否存在
   */
  async isCodeExists(code: string, tenantId: string): Promise<boolean> {
    try {
      return this.tenantStrategy.isCodeExists(code, tenantId); catch (error) {
      this.logError(`检查会员计划代码是否存在失败，代码: ${code}`, error);
      return false;
  }
}
