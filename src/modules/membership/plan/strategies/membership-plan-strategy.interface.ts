import { CreateMembershipPlanDto, UpdateMembershipPlanDto } from '../dto/membership-plan.dto';

import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 会员计划策略接口
 */
export interface MembershipPlanStrategyInterface {
  /**
   * 创建会员计划
   * @param createDto 创建数据
   * @param tenantId 租户ID（可选）
   * @returns 创建的会员计划
   */
  create(createDto: CreateMembershipPlanDto, tenantId?: string): Promise<any>;

  /**
   * 查询会员计划列表
   * @param where 查询条件
   * @param options 分页选项
   * @param tenantId 租户ID（可选）
   * @returns 会员计划列表
   */
  findAll(where: Record<string, any>, options: PaginationOptions, tenantId?: string): Promise<any>;

  /**
   * 查询单个会员计划
   * @param id 会员计划ID
   * @param tenantId 租户ID（可选）
   * @returns 会员计划信息
   */
  findOne(id: number, tenantId?: string): Promise<any>;

  /**
   * 更新会员计划
   * @param id 会员计划ID
   * @param updateDto 更新数据
   * @param tenantId 租户ID（可选）
   * @returns 更新后的会员计划
   */
  update(id: number, updateDto: UpdateMembershipPlanDto, tenantId?: string): Promise<any>;

  /**
   * 删除会员计划
   * @param id 会员计划ID
   * @param tenantId 租户ID（可选）
   * @returns 删除结果
   */
  remove(id: number, tenantId?: string): Promise<any>;

  /**
   * 检查计划代码是否存在
   * @param code 计划代码
   * @param tenantId 租户ID（可选）
   * @param excludeId 排除的ID（可选）
   * @returns 是否存在
   */
  isCodeExists(code: string, tenantId?: string, excludeId?: number): Promise<boolean>;

  /**
   * 获取策略类型
   * @returns 策略类型
   */
  getType(): string;
}
