/*
 * @Author: ya<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-27 22:41:59
 * @LastEditors: yangwenqiang <EMAIL>
 * @LastEditTime: 2025-06-01 00:49:47
 * @FilePath: /multi-tenant-nestjs/src/modules/membership/membership.module.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Module } from '@nestjs/common';

import { MembershipPlanModule } from './plan/membership-plan.module';
import { TenantSubscriptionModule } from '../tenant-subscription/tenant-subscription.module';
import { MembershipBenefitModule } from './benefit/membership-benefit.module';
import { MembershipCardModule } from './card/membership-card.module';
import { MembershipPaymentIntegrationService } from './services/membership-payment-integration.service';
import { MembershipTasksModule } from './tasks/membership-tasks.module';
import { UserMembershipModule } from './user/user-membership.module';
import { VirtualCurrencyModule } from './virtual-currency/virtual-currency.module';
import { PaymentModule } from '../payment/payment.module';

/**
 * 会员模块
 * 整合所有会员相关功能
 */
@Module({
  imports: [
    // 会员计划模块
    MembershipPlanModule,
    // 租户订阅模块
    TenantSubscriptionModule,
    // 用户会员模块
    UserMembershipModule,
    // 会员权益模块
    MembershipBenefitModule,
    // 虚拟币模块
    VirtualCurrencyModule,
    // 会员卡模块
    MembershipCardModule,
    // 会员任务模块
    MembershipTasksModule,
    // 支付模块
    PaymentModule,
  ],
  providers: [
    // 会员支付集成服务
    MembershipPaymentIntegrationService,
  ],
  exports: [
    MembershipPlanModule,
    TenantSubscriptionModule,
    UserMembershipModule,
    MembershipBenefitModule,
    VirtualCurrencyModule,
    MembershipCardModule,
    MembershipTasksModule,
    MembershipPaymentIntegrationService,
  ],
})
export class MembershipModule {}
