import { Module } from '@nestjs/common';

import {
  MembershipBenefitController,
  PlanBenefitController,
  UserBenefitController,
} from './membership-benefit.controller';
import { MembershipBenefitService } from './membership-benefit.service';

import { PrismaModule } from '@/core/database/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [MembershipBenefitController, PlanBenefitController, UserBenefitController],
  providers: [MembershipBenefitService],
  exports: [MembershipBenefitService],
)
export class MembershipBenefitModule {}
