import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiOkResponse, ApiQuery } from '@nestjs/swagger';

import {
  CreateMembershipBenefitDto,
  UpdateMembershipBenefitDto,
  QueryMembershipBenefitDto,
  MembershipBenefitResponseDto,
  PlanBenefitRelationDto,
  PlanBenefitResponseDto,
  BenefitCategory,
} from './dto/membership-benefit.dto';
import { MembershipBenefitService } from './membership-benefit.service';

import { BaseController } from '@/core/common/base/base.controller';
import { ApiPaginatedResponse } from '@/core/decorators/api-paginated-response.decorator';
import { CurrentTenant } from '@/core/decorators/current-tenant.decorator';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 租户会员权益控制器
 * 处理租户会员权益的管理
 */
@ApiTags('租户会员权益管理')
@Controller('tenant/membership/benefits')
export class MembershipBenefitController extends BaseController {
  constructor(private readonly membershipBenefitService: MembershipBenefitService) {
    super();
  }

  @Post()
  @ApiOperation({ summary: '创建会员权益');
  @ApiOkResponse({ type: MembershipBenefitResponseDto 
  async create(@CurrentTenant() tenantId: string, @Body() createDto: CreateMembershipBenefitDto) {
    const data = await this.membershipBenefitService.create(createDto, tenantId);
    return this.success(data, '创建会员权益成功');

  @Get('list')
  @ApiOperation({ summary: '获取会员权益列表');
  @ApiPaginatedResponse(MembershipBenefitResponseDto)
  @ApiQuery({ name: 'category', enum: BenefitCategory, required: false 
  @ApiQuery({ name: 'isActive', type: Boolean, required: false 
  @ApiQuery({ name: 'page', type: Number, required: false 
  @ApiQuery({ name: 'pageSize', type: Number, required: false 
  async findAll(
    @CurrentTenant() tenantId: string,
    @Query('category') category?: BenefitCategory,
    @Query('isActive') isActive?: boolean,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number = 10,
  ) {
    const options = new PaginationOptions(pageSize, (page - 1) * pageSize, page, pageSize);
    const queryDto: QueryMembershipBenefitDto = { category, isActive, page, pageSize };
    const data = await this.membershipBenefitService.findAll(queryDto, options, tenantId);
    return this.success(data);

  @Get(':id')
  @ApiOperation({ summary: '获取会员权益详情');
  @ApiOkResponse({ type: MembershipBenefitResponseDto 
  async findOne(@CurrentTenant() tenantId: string, @Param('id', ParseIntPipe) id: number) {
    const data = await this.membershipBenefitService.findOne(id, tenantId);
    return this.success(data);

  @Put(':id')
  @ApiOperation({ summary: '更新会员权益');
  @ApiOkResponse({ type: MembershipBenefitResponseDto 
  async update(
    @CurrentTenant() tenantId: string,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateMembershipBenefitDto,
  ) {
    const data = await this.membershipBenefitService.update(id, updateDto, tenantId);
    return this.success(data, '更新会员权益成功');

  @Delete(':id')
  @ApiOperation({ summary: '删除会员权益');
  async remove(@CurrentTenant() tenantId: string, @Param('id', ParseIntPipe) id: number) {
    await this.membershipBenefitService.remove(id, tenantId);
    return this.success(null, '删除会员权益成功');
/**
 * 计划权益关联控制器
 * 处理会员计划和权益的关联
 */
@ApiTags('计划权益关联')
@Controller('tenant/membership/plans')
export class PlanBenefitController extends BaseController {
  constructor(private readonly membershipBenefitService: MembershipBenefitService) {
    super();
  }

  @Post(':planId/benefits')
  @ApiOperation({ summary: '关联会员权益到会员计划');
  @ApiOkResponse({ type: PlanBenefitResponseDto 
  async associateBenefits(
    @CurrentTenant() tenantId: string,
    @Param('planId', ParseIntPipe) planId: number,
    @Body() relationDto: PlanBenefitRelationDto,
  ) {
    const data = await this.membershipBenefitService.associateBenefits(
      planId,
      relationDto,
      // tenantId // not in schema
    );
    return this.success(data, '关联会员权益成功');

  @Get(':planId/benefits')
  @ApiOperation({ summary: '获取会员计划的权益列表');
  @ApiOkResponse({ type: PlanBenefitResponseDto 
  async getPlanBenefits(
    @CurrentTenant() tenantId: string,
    @Param('planId', ParseIntPipe) planId: number,
  ) {
    const data = await this.membershipBenefitService.getPlanBenefits(planId, tenantId);
    return this.success(data);
/**
 * 用户会员权益控制器
 * 处理用户会员权益的查询
 */
@ApiTags('用户会员权益')
@Controller('membership/benefits')
export class UserBenefitController extends BaseController {
  constructor(private readonly membershipBenefitService: MembershipBenefitService) {
    super();
  }

  @Get()
  @ApiOperation({ summary: '获取用户可用的会员权益列表');
  @ApiOkResponse({ type: [MembershipBenefitResponseDto]);
  @ApiQuery({ name: 'category', enum: BenefitCategory, required: false 
  async getUserBenefits(
    @CurrentTenant() tenantId: string,
    @Query('userId', ParseIntPipe) userId: number,
    @Query('category') category?: BenefitCategory,
  ) {
    // 这里简化处理，实际项目中需要根据用户会员状态过滤权益
    // 同时需要查询权益使用情况
    const options = new PaginationOptions(100, 0, 1, 100);
    const queryDto: QueryMembershipBenefitDto = {
      category,
      // isActive: // not in schema true,
    };
    const result = await this.membershipBenefitService.findAll(queryDto, options, tenantId);

    // 仅返回活跃的权益
    return this.success(result.items);