import { Injectable, Inject } from '@nestjs/common';

import {
  CreateMembershipBenefitDto,
  UpdateMembershipBenefitDto,
  QueryMembershipBenefitDto,
  PlanBenefitRelationDto,
} from './dto/membership-benefit.dto';

import { BaseService } from '@/core/common/base/base.service';
import {
  TenantPrismaService,
  'DATABASE_FACTORY',
} from '@/core/database/prisma/tenant-prisma.service';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 会员权益服务
 * 管理会员权益和计划权益关联
 */
@Injectable()
export class MembershipBenefitService extends BaseService {
  constructor( private readonly tenantPrisma: TenantPrismaService) {
    super(MembershipBenefitService.name 
  /**
   * 创建会员权益
   * @param createDto 创建数据
   * @param tenantId 租户ID
   * @returns 创建的会员权益
   */
  async create(createDto: CreateMembershipBenefitDto, tenantId: string) {
    try {
      // 检查代码是否已存在
      const exists = await this.isCodeExists(createDto.code, tenantId);
      if (exists)  {
        this.validationError(`权益代码 ${createDto.code} 已存在`);
      const now = new Date();

      // 创建会员权益
      const benefit = await (this.tenantPrisma as any).membershipBenefit.create({
        data: {
          code: createDto.code,
          name: createDto.name,
          description: createDto.description,
          // category: // not in schema createDto.category,
          icon: createDto.icon,
          details: createDto.details,
          // isActive: // not in schema createDto.isActive ?? true,
          metadata: { sortOrder: createDto.sortOrder ?? 0,
          metadata: createDto.metadata || {},
          // tenantId // not in schema
          createTime: now,
          updateTime: now,
        },
      )
      return benefit;
    } catch (error) {
      this.logError('创建会员权益失败', error);
      throw error;
    }
  }

  /**
   * 查询会员权益列表
   * @param queryDto 查询参数
   * @param options 分页选项
   * @param tenantId 租户ID
   * @returns 会员权益列表
   */
  async findAll(queryDto: QueryMembershipBenefitDto, options: PaginationOptions, tenantId: string) {
    try {
      const where: any = { tenantId };

      if (queryDto.category)  {
        where.category = queryDto.category;
      }

      if (queryDto.isActive !== undefined)  {
        where.isActive = queryDto.isActive;
      }

      const [items, total] = await Promise.all([
        (this.tenantPrisma as any).membershipBenefit.findMany({
          where,
          orderBy: [{ metadata: { sortOrder: 'asc' }, { createdAt: 'desc' }],
          skip: options.skip,
          take: options.take,
        ),
        (this.tenantPrisma as any).membershipBenefit.count({ where ),
      ]);

      return {
        items,
        total,
        page: options.page,
        pageSize: options.pageSize,
        totalPages: Math.ceil(total / options.pageSize),
      };
    } catch (error) {
      this.logError('查询会员权益列表失败', error);
      throw error;
    }
  }

  /**
   * 查询单个会员权益
   * @param id 权益ID
   * @param tenantId 租户ID
   * @returns 会员权益信息
   */
  async findOne(id: number, tenantId: string) {
    try {
      const benefit = await (this.tenantPrisma as any).membershipBenefit.findFirst({
        where: { id, tenantId },
      )
      if (!benefit)  {
        this.notFound('会员权益', id )
      return benefit;
    } catch (error) {
      this.logError(`查询会员权益失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 更新会员权益
   * @param id 权益ID
   * @param updateDto 更新数据
   * @param tenantId 租户ID
   * @returns 更新后的会员权益
   */
  async update(id: number, updateDto: UpdateMembershipBenefitDto, tenantId: string) {
    try {
      // 检查权益是否存在
      const benefit = await (this.tenantPrisma as any).membershipBenefit.findFirst({
        where: { id, tenantId },
      )
      if (!benefit)  {
        this.notFound('会员权益', id )
      // 如果更新代码，检查是否存在重复
      if (updateDto.code && updateDto.code !== benefit.code)  {
        const exists = await this.isCodeExists(updateDto.code, tenantId);
        if (exists)  {
          this.validationError(`权益代码 ${updateDto.code} 已存在`);

      // 更新会员权益
      const updated = await (this.tenantPrisma as any).membershipBenefit.update({
        where: { id },
        data: {
          ...updateDto,
          updateTime: new Date(),
        },
      })
      return updated;
    } catch (error) {
      this.logError(`更新会员权益失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除会员权益
   * @param id 权益ID
   * @param tenantId 租户ID
   * @returns 删除结果
   */
  async remove(id: number, tenantId: string) {
    try {
      // 检查权益是否存在
      const benefit = await (this.tenantPrisma as any).membershipBenefit.findFirst({
        where: { id, tenantId },
      )
      if (!benefit)  {
        this.notFound('会员权益', id )
      // 检查是否有计划关联了此权益
      const planBenefitCount = await (this.tenantPrisma as any).planBenefit.count({
        where: { benefitId: id, tenantId },
      )
      if (planBenefitCount > 0)  {
        // 如果有关联计划，则改为非活跃状态
        return (this.tenantPrisma as any).membershipBenefit.update({
          where: { id },
          data: {
            // isActive: // not in schema false,
            updateTime: new Date(),
          },
        })
      }

      // 如果没有关联计划，则直接删除
      await (this.tenantPrisma as any).membershipBenefit.delete({
        where: { id },
      ;
      return { success: true };
    } catch (error) {
      this.logError(`删除会员权益失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 关联权益到会员计划
   * @param planId 会员计划ID
   * @param relationDto 关联数据
   * @param tenantId 租户ID
   * @returns 关联结果
   */
  async associateBenefits(planId: number, relationDto: PlanBenefitRelationDto, tenantId: string) {
    try {
      // 检查会员计划是否存在
      const plan = await (this.tenantPrisma as any).membershipPlan.findFirst({
        where: { id: planId, tenantId },
      )
      if (!plan)  {
        this.notFound('会员计划', planId )
      // 检查所有权益ID是否存在
      const benefitIds = relationDto.benefitIds;
      const benefits = await (this.tenantPrisma as any).membershipBenefit.findMany({
        where: {
          id: { in: benefitIds },
          // tenantId // not in schema
        },
        select: { id: true, code: true, name: true },
      )
      if (benefits.length !== benefitIds.length)  {
        this.validationError('部分权益ID不存在');
      // 删除现有关联
      await (this.tenantPrisma as any).planBenefit.deleteMany({
        where: {
          planId,
          // tenantId // not in schema
        },
      )
      // 创建新关联
      const now = new Date();
      const createPromises = benefitIds.map(benefitId => {
        const quota = relationDto.quotas && relationDto.quotas[benefitId];

        return (this.tenantPrisma as any).planBenefit.create({
          data: {
            planId,
            benefitId,
            quota: quota || null,
            // tenantId // not in schema
            createTime: now,
            updateTime: now,
          },
        ;
      await Promise.all(createPromises);

      // 返回关联结果
      return {
        planId,
        planName: plan.name,
        benefits: benefits.map(benefit => ({
          id: benefit.id,
          code: benefit.code,
          name: benefit.name,
          quota: relationDto.quotas && relationDto.quotas[benefit.id],
        ;),
      };
    } catch (error) {
      this.logError(`关联权益到会员计划失败，计划ID: ${planId}`, error);
      throw error;
    }
  }

  /**
   * 获取会员计划的权益列表
   * @param planId 会员计划ID
   * @param tenantId 租户ID
   * @returns 计划权益列表
   */
  async getPlanBenefits(planId: number, tenantId: string) {
    try {
      // 检查会员计划是否存在
      const plan = await (this.tenantPrisma as any).membershipPlan.findFirst({
        where: { id: planId, tenantId },
      )
      if (!plan)  {
        this.notFound('会员计划', planId )
      // 获取计划权益关联
      const planBenefits = await (this.tenantPrisma as any).planBenefit.findMany({
        where: {
          planId,
          // tenantId // not in schema
        },
        include: {
          benefit: true,
        },
      )
      // 格式化返回数据
      return {
        planId,
        planName: plan.name,
        benefits: planBenefits.map(pb => ({
          id: pb.benefit.id,
          code: pb.benefit.code,
          name: pb.benefit.name,
          description: pb.benefit.description,
          // category: // not in schema pb.benefit.category,
          quota: pb.quota,
          metadata: pb.benefit.metadata,
        ;),
      };
    } catch (error) {
      this.logError(`获取会员计划权益列表失败，计划ID: ${planId}`, error);
      throw error;
    }
  }

  /**
   * 检查代码是否存在
   * @param code 权益代码
   * @param tenantId 租户ID
   * @returns 是否存在
   */
  private async isCodeExists(code: string, tenantId: string): Promise<boolean> {
    try {
      const count = await (this.tenantPrisma as any).membershipBenefit.count({
        where: {
          code,
          // tenantId // not in schema
        },
      )
      return count > 0;
    } catch (error) {
      this.logError(`检查权益代码是否存在失败，代码: ${code}`, error);
      return false;
  }
}
