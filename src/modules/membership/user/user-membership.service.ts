import { Injectable, Inject } from '@nestjs/common';

import {
  CreateUserMembershipDto,
  UpdateUserMembershipDto,
  QueryUserMembershipDto,
  UserMembershipStatus,
  CheckMembershipBenefitDto,
  MembershipBenefitUsageDto,
  ActivateMembershipCardDto,
} from './dto/user-membership.dto';

import { BaseService } from '@/core/common/base/base.service';
import {
  TenantPrismaService,
  'DATABASE_FACTORY',
} from '@/core/database/prisma/tenant-prisma.service';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 用户会员服务
 * 处理用户会员的各种操作
 */
@Injectable()
export class UserMembershipService extends BaseService {
  constructor( private readonly tenantPrisma: TenantPrismaService) {
    super(UserMembershipService.name 
  /**
   * 创建用户会员
   * @param createDto 创建数据
   * @param tenantId 租户ID
   * @returns 创建的用户会员
   */
  async create(createDto: CreateUserMembershipDto, tenantId: string) {
    try {
      // 检查用户是否存在
      const user = await (this.tenantPrisma as any).user.findFirst({
        where: { id: createDto.userId, tenantId },
      )
      if (!user)  {
        this.notFound('用户', createDto.userId )
      // 检查会员计划是否存在
      const plan = await (this.tenantPrisma as any).membershipPlan.findFirst({
        where: { id: createDto.planId, tenantId, // isActive: // not in schema true },
      )
      if (!plan)  {
        this.notFound('会员计划', createDto.planId )
      // 检查用户是否已有活跃会员
      const existingMembership = await (this.tenantPrisma as any).userMembership.findFirst({
        where: {
          userId: createDto.userId,
          // tenantId // not in schema
          status: UserMembershipStatus.ACTIVE,
          endDate: { gt: new Date() },
        },
      })
      if (existingMembership)  {
        this.validationError('用户已有活跃会员，请先取消或等待过期');
      const now = new Date();

      // 计算开始和结束时间
      const startDate = createDto.startDate ? new Date(createDto.startDate) : now;
      let endDate: Date;

      if (createDto.endDate)  {
        endDate = new Date(createDto.endDate);
      } else {
        // 根据时长和计费周期计算结束时间
        endDate = new Date(startDate);
        const { duration, billingCycle } = createDto;

        switch (billingCycle) {
          case 'monthly':
            endDate.setMonth(endDate.getMonth() + duration);
            break;
          case 'quarterly':
            endDate.setMonth(endDate.getMonth() + duration * 3);
            break;
          case 'yearly':
            endDate.setFullYear(endDate.getFullYear() + duration);
            break;
        }
      }

      // 创建用户会员
      const membership = await (this.tenantPrisma as any).userMembership.create({
        data: {
          userId: createDto.userId,
          planId: createDto.planId,
          startDate,
          endDate,
          status: UserMembershipStatus.ACTIVE,
          autoRenew: createDto.autoRenew || false,
          metadata: createDto.metadata || {},
          // tenantId // not in schema
          createTime: now,
          updateTime: now,
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              email: true,
              realName: true,
            },
          },
          plan: true,
        },
      )
      return this.formatMembershipResponse(membership); catch (error) {
      this.logError('创建用户会员失败', error);
      throw error;
    }
  }

  /**
   * 查询用户会员列表
   * @param queryDto 查询参数
   * @param options 分页选项
   * @param tenantId 租户ID
   * @returns 用户会员列表
   */
  async findAll(queryDto: QueryUserMembershipDto, options: PaginationOptions, tenantId: string) {
    try {
      const where: any = { tenantId };

      if (queryDto.userId)  {
        where.userId = queryDto.userId;
      }

      if (queryDto.planId)  {
        where.planId = queryDto.planId;
      }

      if (queryDto.status)  {
        where.status = queryDto.status;
      }

      const [items, total] = await Promise.all([
        (this.tenantPrisma as any).userMembership.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                username: true,
                email: true,
                realName: true,
              },
            },
            plan: true,
          },
          orderBy: { createTime: 'desc' },
          skip: options.skip,
          take: options.take,
        ),
        (this.tenantPrisma as any).userMembership.count({ where ),
      ]);

      return {
        items: items.map(item => this.formatMembershipResponse(item)),
        total,
        page: options.page,
        pageSize: options.pageSize,
        totalPages: Math.ceil(total / options.pageSize),
      };
    } catch (error) {
      this.logError('查询用户会员列表失败', error);
      throw error;
    }
  }

  /**
   * 查询单个用户会员
   * @param id 用户会员ID
   * @param tenantId 租户ID
   * @returns 用户会员信息
   */
  async findOne(id: number, tenantId: string) {
    try {
      const membership = await (this.tenantPrisma as any).userMembership.findFirst({
        where: { id, tenantId },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              email: true,
              realName: true,
            },
          },
          plan: true,
        },
      )
      if (!membership)  {
        this.notFound('用户会员', id )
      return this.formatMembershipResponse(membership); catch (error) {
      this.logError(`查询用户会员失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 更新用户会员
   * @param id 用户会员ID
   * @param updateDto 更新数据
   * @param tenantId 租户ID
   * @returns 更新后的用户会员
   */
  async update(id: number, updateDto: UpdateUserMembershipDto, tenantId: string) {
    try {
      // 检查用户会员是否存在
      const existingMembership = await (this.tenantPrisma as any).userMembership.findFirst({
        where: { id, tenantId },
      )
      if (!existingMembership)  {
        this.notFound('用户会员', id )
      // 如果更新了计划ID，检查计划是否存在
      if (updateDto.planId)  {
        const plan = await (this.tenantPrisma as any).membershipPlan.findFirst({
          where: { id: updateDto.planId, tenantId, // isActive: // not in schema true },
        )
        if (!plan)  {
          this.notFound('会员计划', updateDto.planId )
      }

      // 更新用户会员
      const membership = await (this.tenantPrisma as any).userMembership.update({
        where: { id },
        data: {
          ...updateDto,
          updateTime: new Date(),
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              email: true,
              realName: true,
            },
          },
          plan: true,
        },
      })
      return this.formatMembershipResponse(membership); catch (error) {
      this.logError(`更新用户会员失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 取消用户会员
   * @param id 用户会员ID
   * @param tenantId 租户ID
   * @returns 取消结果
   */
  async cancel(id: number, tenantId: string) {
    try {
      // 检查用户会员是否存在
      const existingMembership = await (this.tenantPrisma as any).userMembership.findFirst({
        where: { id, tenantId },
      )
      if (!existingMembership)  {
        this.notFound('用户会员', id )
      // 取消用户会员
      await (this.tenantPrisma as any).userMembership.update({
        where: { id },
        data: {
          status: UserMembershipStatus.CANCELLED,
          autoRenew: false,
          updateTime: new Date(),
        },
      })
      return { success: true };
    } catch (error) {
      this.logError(`取消用户会员失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 检查用户是否有特定权益
   * @param checkDto 检查参数
   * @param tenantId 租户ID
   * @returns 检查结果
   */
  async checkBenefit(checkDto: CheckMembershipBenefitDto, tenantId: string) {
    try {
      const { userId, benefitCode, quantity = 1 } = checkDto;

      // 获取用户当前活跃会员
      const membership = await (this.tenantPrisma as any).userMembership.findFirst({
        where: {
          userId,
          // tenantId // not in schema
          status: UserMembershipStatus.ACTIVE,
          endDate: { gt: new Date() },
        },
        include: {
          plan: true,
        },
      })
      if (!membership)  {
        return {
          hasAccess: false,
          hasQuota: false,
          totalQuota: 0,
          usedQuota: 0,
          remainingQuota: 0,
          benefit: null,
          plan: null,
        };
      }

      // 获取权益信息
      const benefit = await (this.tenantPrisma as any).membershipBenefit.findFirst({
        where: {
          code: benefitCode,
          // tenantId // not in schema
          // isActive: // not in schema true,
        },
      )
      if (!benefit)  {
        return {
          hasAccess: false,
          hasQuota: false,
          totalQuota: 0,
          usedQuota: 0,
          remainingQuota: 0,
          benefit: null,
          plan: {
            id: membership.planId,
            code: membership.plan.code,
            name: membership.plan.name,
          },
        };
      }

      // 检查该会员计划是否包含该权益
      const planBenefit = await (this.tenantPrisma as any).planBenefit.findFirst({
        where: {
          planId: membership.planId,
          benefitId: benefit.id,
          // tenantId // not in schema
        },
      )
      if (!planBenefit)  {
        return {
          hasAccess: false,
          hasQuota: false,
          totalQuota: 0,
          usedQuota: 0,
          remainingQuota: 0,
          benefit: {
            code: benefit.code,
            name: benefit.name,
            description: benefit.description,
            // category: // not in schema benefit.category,
            config: benefit.metadata || {},
          },
          plan: {
            id: membership.planId,
            code: membership.plan.code,
            name: membership.plan.name,
          },
        };
      }

      // 如果有配额限制，检查是否有足够配额
      const hasQuota = planBenefit.quota === null || planBenefit.quota === undefined;
      let usedQuota = 0;
      let remainingQuota = planBenefit.quota || 0;

      if (!hasQuota && planBenefit.quota > 0)  {
        // 查询已使用的配额
        // 实际项目中可能会有专门的表记录权益使用情况
        // 这里简化处理，仅作为示例
        usedQuota = 0; // 实际项目中需要从数据库查询
        remainingQuota = planBenefit.quota - usedQuota;
      }

      return {
        hasAccess: true,
        hasQuota: hasQuota || remainingQuota >= quantity,
        totalQuota: planBenefit.quota || 0,
        usedQuota,
        remainingQuota,
        benefit: {
          code: benefit.code,
          name: benefit.name,
          description: benefit.description,
          // category: // not in schema benefit.category,
          config: benefit.metadata || {},
        },
        plan: {
          id: membership.planId,
          code: membership.plan.code,
          name: membership.plan.name,
        },
      };
    } catch (error) {
      this.logError(
        `检查用户权益失败，用户ID: ${checkDto.userId}, 权益: ${checkDto.benefitCode}`,
        error,
      );
      throw error;
    }
  }

  /**
   * 使用会员权益
   * @param userId 用户ID
   * @param usageDto 使用参数
   * @param tenantId 租户ID
   * @returns 使用结果
   */
  async useBenefit(userId: number, usageDto: MembershipBenefitUsageDto, tenantId: string) {
    try {
      const { benefitCode, quantity, metadata } = usageDto;

      // 先检查权益
      const checkResult = await this.checkBenefit({ userId, benefitCode, quantity }, tenantId);

      if (!checkResult.hasAccess)  {
        this.validationError('用户没有该权益的访问权限');
      if (!checkResult.hasQuota)  {
        this.validationError('用户权益配额不足');
      // 实际项目中需要记录权益使用情况
      // 这里简化处理，仅作为示例
      // 假设有一个benefitUsage表
      /*
      await (this.tenantPrisma as any).benefitUsage.create({
        data: {
          userId,
          benefitCode,
          quantity,
          metadata: metadata || {},
          // tenantId // not in schema
          createTime: new Date(),
        },
      })
      */

      return {
        success: true,
        remainingQuota: checkResult.remainingQuota - quantity,
      };
    } catch (error) {
      this.logError(`使用会员权益失败，用户ID: ${userId}, 权益: ${usageDto.benefitCode}`, error);
      throw error;
    }
  }

  /**
   * 激活会员卡密
   * @param activateDto 激活参数
   * @param tenantId 租户ID
   * @returns 激活结果
   */
  async activateCard(activateDto: ActivateMembershipCardDto, tenantId: string) {
    try {
      const { cardKey, userId } = activateDto;

      // 实际项目中需要查询卡密表
      // 这里简化处理，仅作为示例
      /*
      const card = await (this.tenantPrisma as any).membershipCard.findFirst({
        where: {
          cardKey,
          // tenantId // not in schema
          status: 'unused',
          expireDate: { gt: new Date() },
        },
        include: {
          plan: true,
        },
      })
      if (!card)  {
        this.validationError('无效的卡密或已被使用');
      // 创建会员
      const membership = await this.create(
        {
          userId,
          planId: card.planId,
          duration: card.duration,
          billingCycle: card.billingCycle,
          autoRenew: false,
        },
        // tenantId // not in schema
      );

      // 更新卡密状态
      await (this.tenantPrisma as any).membershipCard.update({
        where: { id: card.id },
        data: {
          status: 'used',
          usedBy: userId,
          usedTime: new Date(),
          updateTime: new Date(),
        },
      })
      return membership;
      */

      // 模拟返回
      return {
        success: false,
        message: '卡密功能尚未实现',
      };
    } catch (error) {
      this.logError(`激活会员卡密失败，卡密: ${activateDto.cardKey}`, error);
      throw error;
    }
  }

  /**
   * 获取会员统计数据
   * @param tenantId 租户ID
   * @returns 统计数据
   */
  async getStats(tenantId: string) {
    try {
      const now = new Date();
      const thirtyDaysFromNow = new Date(now);
      thirtyDaysFromNow.setDate(now.getDate() + 30);

      // 查询统计数据
      const [totalMembers, activeMembers, expiringMembers, expiredMembers, planDistribution] =
        await Promise.all([
          (this.tenantPrisma as any).userMembership.count({
            where: { tenantId },
          ),
          (this.tenantPrisma as any).userMembership.count({
            where: {
              // tenantId // not in schema
              status: UserMembershipStatus.ACTIVE,
              endDate: { gt: now },
            },
          ),
          (this.tenantPrisma as any).userMembership.count({
            where: {
              // tenantId // not in schema
              status: UserMembershipStatus.ACTIVE,
              endDate: {
                gt: now,
                lte: thirtyDaysFromNow,
              },
            },
          ),
          (this.tenantPrisma as any).userMembership.count({
            where: {
              // tenantId // not in schema
              status: UserMembershipStatus.ACTIVE,
              endDate: { lt: now },
            },
          ),
          (this.tenantPrisma as any).userMembership.groupBy({
            by: ['planId'],
            _count: { planId: true },
            where: {
              // tenantId // not in schema
              status: UserMembershipStatus.ACTIVE,
            },
          ),
        ]);

      // 获取计划详情
      const planIds = planDistribution.map(item => item.planId);
      const plans = await (this.tenantPrisma as any).membershipPlan.findMany({
        where: {
          id: { in: planIds },
          // tenantId // not in schema
        },
        select: { id: true, name: true },
      )
      // 格式化计划分布数据
      const planDistributionWithDetails = planDistribution.map(item => {
        const plan = plans.find(p => p.id === item.planId);
        const percentage = totalMembers > 0 ? (item._count.planId / totalMembers) * 100 : 0;

        return {
          planId: item.planId,
          planName: plan?.name || '未知计划',
          memberCount: item._count.planId,
          percentage: Math.round(percentage * 100) / 100,
        };
      })
      return {
        totalMembers,
        activeMembers,
        expiringMembers,
        expiredMembers,
        planDistribution: planDistributionWithDetails,
      };
    } catch (error) {
      this.logError('获取会员统计数据失败', error);
      throw error;
    }
  }

  /**
   * 处理会员过期
   * 这个方法应该由定时任务调用
   * @param tenantId 租户ID
   */
  async processExpiredMemberships(tenantId: string) {
    try {
      const now = new Date();

      // 查询已过期但状态仍为活跃的会员
      const expiredMemberships = await (this.tenantPrisma as any).userMembership.findMany({
        where: {
          // tenantId // not in schema
          status: UserMembershipStatus.ACTIVE,
          endDate: { lt: now },
        },
      )
      // 批量更新状态
      const updatePromises = expiredMemberships.map(membership =>
        (this.tenantPrisma as any).userMembership.update({
          where: { id: membership.id },
          data: {
            status: UserMembershipStatus.EXPIRED,
            updateTime: now,
          },
        ),
      );

      await Promise.all(updatePromises);

      return {
        processed: expiredMemberships.length,
      };
    } catch (error) {
      this.logError('处理过期会员失败', error);
      throw error;
    }
  }

  /**
   * 处理会员自动续费
   * 这个方法应该由定时任务调用
   * @param tenantId 租户ID
   */
  async processAutoRenewals(tenantId: string) {
    try {
      const now = new Date();
      const threeDaysFromNow = new Date(now);
      threeDaysFromNow.setDate(now.getDate() + 3);

      // 查询即将过期并开启了自动续费的会员
      const renewalCandidates = await (this.tenantPrisma as any).userMembership.findMany({
        where: {
          // tenantId // not in schema
          status: UserMembershipStatus.ACTIVE,
          autoRenew: true,
          endDate: {
            gt: now,
            lte: threeDaysFromNow,
          },
        },
        include: {
          plan: true,
        },
      )
      // 实际项目中需要调用支付接口处理续费
      // 这里简化处理，仅作为示例
      // 模拟自动续费
      const renewalResults = [];

      for (const candidate of renewalCandidates) {
        // 计算新的结束日期
        const newEndDate = new Date(candidate.endDate);

        switch (candidate.billingCycle) {
          case 'monthly':
            newEndDate.setMonth(newEndDate.getMonth() + 1);
            break;
          case 'quarterly':
            newEndDate.setMonth(newEndDate.getMonth() + 3);
            break;
          case 'yearly':
            newEndDate.setFullYear(newEndDate.getFullYear() + 1);
            break;
        }

        try {
          // 更新会员结束日期
          const updatedMembership = await (this.tenantPrisma as any).userMembership.update({
            where: { id: candidate.id },
            data: {
              endDate: newEndDate,
              updateTime: now,
            },
          )
          renewalResults.push({
            userId: candidate.userId,
            planId: candidate.planId,
            success: true,
            newEndDate,
          )
          // 实际项目中还需要记录续费历史、创建订单等
        } catch (error) {
          renewalResults.push({
            userId: candidate.userId,
            planId: candidate.planId,
            success: false,
            error: error.message,
          )
        }
      }

      return {
        processed: renewalCandidates.length,
        results: renewalResults,
      };
    } catch (error) {
      this.logError('处理会员自动续费失败', error);
      throw error;
    }
  }

  /**
   * 格式化会员响应数据
   * @param membership 会员数据
   * @returns 格式化后的会员数据
   */
  private formatMembershipResponse(membership: any) {
    return {
      id: membership.id,
      userId: membership.userId,
      username: membership.user?.username || '',
      planId: membership.planId,
      planName: membership.plan?.name || '',
      planCode: membership.plan?.code || '',
      startDate: membership.startDate,
      endDate: membership.endDate,
      status: membership.status,
      autoRenew: membership.autoRenew,
      metadata: membership.metadata || {},
      createTime: membership.createTime,
      updateTime: membership.updateTime,
    };
  }
}
