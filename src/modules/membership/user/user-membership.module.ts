import { Module } from '@nestjs/common';

import {
  TenantUserMembershipController,
  UserMembershipController,
} from './user-membership.controller';
import { UserMembershipService } from './user-membership.service';

import { PrismaModule } from '@/core/database/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [TenantUserMembershipController, UserMembershipController],
  providers: [UserMembershipService],
  exports: [UserMembershipService],
})
export class UserMembershipModule {}
