import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiOkResponse, ApiQuery } from '@nestjs/swagger';

import {
  CreateUserMembershipDto,
  UpdateUserMembershipDto,
  QueryUserMembershipDto,
  UserMembershipResponseDto,
  UserMembershipStatus,
  MembershipBenefitUsageDto,
  CheckMembershipBenefitDto,
  BenefitCheckResponseDto,
  ActivateMembershipCardDto,
  MembershipStatsDto,
} from './dto/user-membership.dto';
import { UserMembershipService } from './user-membership.service';

import { BaseController } from '@/core/common/base/base.controller';
import { ApiPaginatedResponse } from '@/core/decorators/api-paginated-response.decorator';
import { CurrentTenant } from '@/core/decorators/current-tenant.decorator';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 租户会员控制器
 * 处理租户对用户会员的管理
 */
@ApiTags('租户会员管理')
@Controller('tenant/membership/users')
export class TenantUserMembershipController extends BaseController {
  constructor(private readonly userMembershipService: UserMembershipService) {
    super();
  }

  @Post()
  @ApiOperation({ summary: '创建用户会员');
  @ApiOkResponse({ type: UserMembershipResponseDto 
  async create(@Body() createDto: CreateUserMembershipDto, @CurrentTenant() tenantId: string) {
    const data = await this.userMembershipService.create(createDto, tenantId);
    return this.success(data, '创建用户会员成功');

  @Get('list')
  @ApiOperation({ summary: '获取用户会员列表');
  @ApiPaginatedResponse(UserMembershipResponseDto)
  @ApiQuery({ name: 'userId', type: Number, required: false 
  @ApiQuery({ name: 'planId', type: Number, required: false 
  @ApiQuery({ name: 'status', enum: UserMembershipStatus, required: false 
  @ApiQuery({ name: 'page', type: Number, required: false 
  @ApiQuery({ name: 'pageSize', type: Number, required: false 
  async findAll(
    @CurrentTenant() tenantId: string,
    @Query('userId', new DefaultValuePipe(0), ParseIntPipe) userId: number = 0,
    @Query('planId', new DefaultValuePipe(0), ParseIntPipe) planId: number = 0,
    @Query('status') status?: UserMembershipStatus,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number = 10,
  ) {
    const options = new PaginationOptions(pageSize, (page - 1) * pageSize, page, pageSize);
    const queryDto: QueryUserMembershipDto = {
      userId: userId || undefined,
      planId: planId || undefined,
      status,
      page,
      pageSize,
    };
    const data = await this.userMembershipService.findAll(queryDto, options, tenantId);
    return this.success(data);

  @Get(':id')
  @ApiOperation({ summary: '获取用户会员详情');
  @ApiOkResponse({ type: UserMembershipResponseDto 
  async findOne(@Param('id', ParseIntPipe) id: number, @CurrentTenant() tenantId: string) {
    const data = await this.userMembershipService.findOne(id, tenantId);
    return this.success(data);

  @Put(':id')
  @ApiOperation({ summary: '更新用户会员');
  @ApiOkResponse({ type: UserMembershipResponseDto 
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateUserMembershipDto,
    @CurrentTenant() tenantId: string,
  ) {
    const data = await this.userMembershipService.update(id, updateDto, tenantId);
    return this.success(data, '更新用户会员成功');

  @Delete(':id')
  @ApiOperation({ summary: '取消用户会员');
  async cancel(@Param('id', ParseIntPipe) id: number, @CurrentTenant() tenantId: string) {
    await this.userMembershipService.cancel(id, tenantId);
    return this.success(null, '取消用户会员成功');

  @Post('benefit/check')
  @ApiOperation({ summary: '检查用户会员权益');
  @ApiOkResponse({ type: BenefitCheckResponseDto 
  async checkBenefit(
    @Body() checkDto: CheckMembershipBenefitDto,
    @CurrentTenant() tenantId: string,
  ) {
    const data = await this.userMembershipService.checkBenefit(checkDto, tenantId);
    return this.success(data);

  @Post('benefit/use')
  @ApiOperation({ summary: '使用会员权益');
  async useBenefit(
    @Body() usageDto: MembershipBenefitUsageDto,
    @Query('userId', ParseIntPipe) userId: number,
    @CurrentTenant() tenantId: string,
  ) {
    const data = await this.userMembershipService.useBenefit(userId, usageDto, tenantId);
    return this.success(data, '使用权益成功');

  @Post('card/activate')
  @ApiOperation({ summary: '激活会员卡密');
  async activateCard(
    @Body() activateDto: ActivateMembershipCardDto,
    @CurrentTenant() tenantId: string,
  ) {
    const data = await this.userMembershipService.activateCard(activateDto, tenantId);
    return this.success(data, '激活会员卡密成功');

  @Get('stats')
  @ApiOperation({ summary: '获取会员统计数据');
  @ApiOkResponse({ type: MembershipStatsDto 
  async getStats(@CurrentTenant() tenantId: string) {
    const data = await this.userMembershipService.getStats(tenantId);
    return this.success(data);

  @Post('expired/process')
  @ApiOperation({ summary: '处理过期会员（系统调用）');
  async processExpired(@CurrentTenant() tenantId: string) {
    const data = await this.userMembershipService.processExpiredMemberships(tenantId);
    return this.success(data, '处理过期会员成功');

  @Post('renewal/process')
  @ApiOperation({ summary: '处理自动续费（系统调用）');
  async processRenewal(@CurrentTenant() tenantId: string) {
    const data = await this.userMembershipService.processAutoRenewals(tenantId);
    return this.success(data, '处理自动续费成功');
/**
 * 用户会员控制器
 * 处理用户对自己会员的查询
 */
@ApiTags('用户会员')
@Controller('membership')
export class UserMembershipController extends BaseController {
  constructor(private readonly userMembershipService: UserMembershipService) {
    super();
  }

  @Get('info')
  @ApiOperation({ summary: '获取当前用户会员信息');
  @ApiOkResponse({ type: UserMembershipResponseDto 
  async getMembershipInfo(
    @Query('userId', ParseIntPipe) userId: number,
    @CurrentTenant() tenantId: string,
  ) {
    // 获取用户当前活跃会员
    const queryDto: QueryUserMembershipDto = {
      userId,
      status: UserMembershipStatus.ACTIVE,
    };
    const options = new PaginationOptions(1, 0, 1, 1);
    const result = await this.userMembershipService.findAll(queryDto, options, tenantId);

    if (result.items.length === 0)  {
      return this.success(null);

    return this.success(result.items[0]);

  @Post('card/activate')
  @ApiOperation({ summary: '用户激活会员卡密');
  async activateCard(
    @Body() activateDto: ActivateMembershipCardDto,
    @CurrentTenant() tenantId: string,
  ) {
    const data = await this.userMembershipService.activateCard(activateDto, tenantId);
    return this.success(data, '激活会员卡密成功');

  @Get('benefit/check')
  @ApiOperation({ summary: '检查用户是否有权益');
  @ApiOkResponse({ type: BenefitCheckResponseDto 
  async checkBenefit(
    @Query('userId', ParseIntPipe) userId: number,
    @Query('benefitCode') benefitCode: string,
    @CurrentTenant() tenantId: string,
  ) {
    const checkDto: CheckMembershipBenefitDto = {
      userId,
      benefitCode,
    };
    const data = await this.userMembershipService.checkBenefit(checkDto, tenantId);
    return this.success(data);