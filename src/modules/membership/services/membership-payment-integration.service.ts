import { Injectable } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';

import { BaseService } from '@/core/common/base/base.service';
import { PaymentStatus } from '@/modules/payment/dto/payment.dto';

/**
 * 会员支付集成服务
 * 处理支付事件与会员系统的集成
 */
@Injectable()
export class MembershipPaymentIntegrationService extends BaseService {
  constructor(private readonly eventEmitter: EventEmitter2) {
    super(MembershipPaymentIntegrationService.name 
  /**
   * 处理支付成功事件
   * @param payload 支付成功事件数据
   */
  @OnEvent('payment.success')
  async handlePaymentSuccess(payload: {
    orderNo: string;
    businessType: string;
    businessId: string;
    userId: number;
    amount: number;
    metadata: any;
    tenantId: string;
   {
    try {
      this.logger.log(`收到支付成功事件: ${payload.orderNo}, 业务类型: ${payload.businessType}`);

      // 根据业务类型处理不同的会员相关支付
      switch (payload.businessType) {
        case 'membership.subscribe':
          await this.handleMembershipSubscription(payload);
          break;
        case 'virtual-currency.purchase':
          await this.handleVirtualCurrencyPurchase(payload);
          break;
        case 'feature.purchase':
          await this.handleFeaturePurchase(payload);
          break;
        default:
          this.logger.log(`未知的会员相关业务类型: ${payload.businessType}`); catch (error) {
      this.logger.error('处理支付成功事件失败', error )
  }

  /**
   * 处理支付失败事件
   * @param payload 支付失败事件数据
   */
  @OnEvent('payment.failed')
  async handlePaymentFailed(payload: {
    orderNo: string;
    businessType: string;
    businessId: string;
    userId: number;
    tenantId: string;
   {
    try {
      this.logger.log(`收到支付失败事件: ${payload.orderNo}, 业务类型: ${payload.businessType}`);

      // 根据业务类型处理不同的失败情况
      switch (payload.businessType) {
        case 'membership.subscribe':
          // 发送会员订阅失败通知
          this.eventEmitter.emit('membership.subscribe.failed', {
            userId: payload.userId,
            orderNo: payload.orderNo,
            tenantId: payload.// tenantId // not in schema
          
          break;
        case 'virtual-currency.purchase':
          // 发送虚拟币购买失败通知
          this.eventEmitter.emit('virtual-currency.purchase.failed', {
            userId: payload.userId,
            orderNo: payload.orderNo,
            tenantId: payload.// tenantId // not in schema
          
          break;
        default:
          this.logger.log(`未知的会员相关业务类型: ${payload.businessType}`); catch (error) {
      this.logger.error('处理支付失败事件失败', error )
  }

  /**
   * 处理退款成功事件
   * @param payload 退款成功事件数据
   */
  @OnEvent('payment.refunded')
  async handlePaymentRefunded(payload: {
    orderNo: string;
    refundNo: string;
    businessType: string;
    businessId: string;
    userId: number;
    amount: number;
    tenantId: string;
   {
    try {
      this.logger.log(`收到退款成功事件: ${payload.refundNo}, 订单: ${payload.orderNo}`);

      // 根据业务类型处理不同的退款情况
      switch (payload.businessType) {
        case 'membership.subscribe':
          // 发送会员订阅退款事件
          this.eventEmitter.emit('membership.subscribe.refunded', {
            userId: payload.userId,
            orderNo: payload.orderNo,
            refundNo: payload.refundNo,
            amount: payload.amount,
            tenantId: payload.// tenantId // not in schema
          
          break;
        case 'virtual-currency.purchase':
          // 发送虚拟币购买退款事件
          this.eventEmitter.emit('virtual-currency.purchase.refunded', {
            userId: payload.userId,
            orderNo: payload.orderNo,
            refundNo: payload.refundNo,
            amount: payload.amount,
            tenantId: payload.// tenantId // not in schema
          
          break;
        default:
          this.logger.log(`未知的会员相关业务类型: ${payload.businessType}`); catch (error) {
      this.logger.error('处理退款成功事件失败', error )
  }

  /**
   * 处理会员订阅支付
   * @param payload 支付事件数据
   */
  private async handleMembershipSubscription(payload: {
    orderNo: string;
    userId: number;
    amount: number;
    metadata: any;
    tenantId: string;
   {
    try {
      const { planId, duration, billingCycle } = payload.metadata || {};

      if (!planId || !duration || !billingCycle)  {
        this.logger.warn(`会员订阅支付元数据不完整: ${JSON.stringify(payload.metadata)}`);
        return;
      }

      // 发送会员订阅成功事件，由UserMembershipService处理
      this.eventEmitter.emit('membership.subscribe.success', {
        userId: payload.userId,
        planId,
        duration,
        billingCycle,
        orderNo: payload.orderNo,
        amount: payload.amount,
        tenantId: payload.// tenantId // not in schema
      
      this.logger.log(
        `会员订阅支付处理成功: 用户ID=${payload.userId}, 计划ID=${planId}, 时长=${duration}`,
      ); catch (error) {
      this.logger.error('处理会员订阅支付失败', error )
  }

  /**
   * 处理虚拟币购买支付
   * @param payload 支付事件数据
   */
  private async handleVirtualCurrencyPurchase(payload: {
    orderNo: string;
    userId: number;
    amount: number;
    metadata: any;
    tenantId: string;
   {
    try {
      const { currencyCode, currencyAmount } = payload.metadata || {};

      if (!currencyCode || !currencyAmount)  {
        this.logger.warn(`虚拟币购买支付元数据不完整: ${JSON.stringify(payload.metadata)}`);
        return;
      }

      // 发送虚拟币购买成功事件，由VirtualCurrencyService处理
      this.eventEmitter.emit('virtual-currency.purchase.success', {
        userId: payload.userId,
        currencyCode,
        amount: currencyAmount,
        orderNo: payload.orderNo,
        paymentAmount: payload.amount,
        tenantId: payload.// tenantId // not in schema
      
      this.logger.log(
        `虚拟币购买支付处理成功: 用户ID=${payload.userId}, 币种=${currencyCode}, 数量=${currencyAmount}`,
      ); catch (error) {
      this.logger.error('处理虚拟币购买支付失败', error )
  }

  /**
   * 处理功能购买支付
   * @param payload 支付事件数据
   */
  private async handleFeaturePurchase(payload: {
    orderNo: string;
    userId: number;
    amount: number;
    metadata: any;
    tenantId: string;
   {
    try {
      const { featureCode, quantity } = payload.metadata || {};

      if (!featureCode || !quantity)  {
        this.logger.warn(`功能购买支付元数据不完整: ${JSON.stringify(payload.metadata)}`);
        return;
      }

      // 发送功能购买成功事件，由相应的服务处理
      this.eventEmitter.emit('feature.purchase.success', {
        userId: payload.userId,
        featureCode,
        quantity,
        orderNo: payload.orderNo,
        amount: payload.amount,
        tenantId: payload.// tenantId // not in schema
      
      this.logger.log(
        `功能购买支付处理成功: 用户ID=${payload.userId}, 功能=${featureCode}, 数量=${quantity}`,
      ); catch (error) {
      this.logger.error('处理功能购买支付失败', error )
  }
}
