import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiOkResponse, ApiQuery } from '@nestjs/swagger';

import {
  CreateVirtualCurrencyTypeDto,
  UpdateVirtualCurrencyTypeDto,
  QueryVirtualCurrencyTypeDto,
  VirtualCurrencyTypeResponseDto,
  RechargeVirtualCurrencyDto,
  ConsumeVirtualCurrencyDto,
  QueryUserWalletDto,
  WalletBalanceResponseDto,
  QueryTransactionDto,
  TransactionResponseDto,
  TransactionType,
  FeatureCurrencyCostDto,
} from './dto/virtual-currency.dto';
import { VirtualCurrencyService } from './virtual-currency.service';

import { BaseController } from '@/core/common/base/base.controller';
import { ApiPaginatedResponse } from '@/core/decorators/api-paginated-response.decorator';
import { CurrentTenant } from '@/core/decorators/current-tenant.decorator';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 租户虚拟币类型控制器
 * 处理租户虚拟币类型的管理
 */
@ApiTags('租户虚拟币类型管理')
@Controller('tenant/virtual-currency/types')
export class VirtualCurrencyTypeController extends BaseController {
  constructor(private readonly virtualCurrencyService: VirtualCurrencyService) {
    super();
  }

  @Post()
  @ApiOperation({ summary: '创建虚拟币类型');
  @ApiOkResponse({ type: VirtualCurrencyTypeResponseDto 
  async create(@CurrentTenant() tenantId: string, @Body() createDto: CreateVirtualCurrencyTypeDto) {
    const data = await this.virtualCurrencyService.createCurrencyType(createDto, tenantId);
    return this.success(data, '创建虚拟币类型成功');

  @Get('list')
  @ApiOperation({ summary: '获取虚拟币类型列表');
  @ApiPaginatedResponse(VirtualCurrencyTypeResponseDto)
  @ApiQuery({ name: 'isActive', type: Boolean, required: false 
  @ApiQuery({ name: 'page', type: Number, required: false 
  @ApiQuery({ name: 'pageSize', type: Number, required: false 
  async findAll(
    @CurrentTenant() tenantId: string,
    @Query('isActive') isActive?: boolean,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number = 10,
  ) {
    const options = new PaginationOptions(pageSize, (page - 1) * pageSize, page, pageSize);
    const queryDto: QueryVirtualCurrencyTypeDto = { isActive, page, pageSize };
    const data = await this.virtualCurrencyService.findAllCurrencyTypes(
      queryDto,
      options,
      // tenantId // not in schema
    );
    return this.success(data);

  @Get(':id')
  @ApiOperation({ summary: '获取虚拟币类型详情');
  @ApiOkResponse({ type: VirtualCurrencyTypeResponseDto 
  async findOne(@CurrentTenant() tenantId: string, @Param('id', ParseIntPipe) id: number) {
    const data = await this.virtualCurrencyService.findOneCurrencyType(id, tenantId);
    return this.success(data);

  @Put(':id')
  @ApiOperation({ summary: '更新虚拟币类型');
  @ApiOkResponse({ type: VirtualCurrencyTypeResponseDto 
  async update(
    @CurrentTenant() tenantId: string,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateVirtualCurrencyTypeDto,
  ) {
    const data = await this.virtualCurrencyService.updateCurrencyType(id, updateDto, tenantId);
    return this.success(data, '更新虚拟币类型成功');
/**
 * 租户虚拟币钱包控制器
 * 处理租户对用户钱包的管理
 */
@ApiTags('租户虚拟币钱包管理')
@Controller('tenant/virtual-currency')
export class TenantVirtualCurrencyController extends BaseController {
  constructor(private readonly virtualCurrencyService: VirtualCurrencyService) {
    super();
  }

  @Post('recharge')
  @ApiOperation({ summary: '用户充值虚拟币');
  async recharge(
    @CurrentTenant() tenantId: string,
    @Body() rechargeDto: RechargeVirtualCurrencyDto,
  ) {
    const data = await this.virtualCurrencyService.rechargeUserWallet(rechargeDto, tenantId);
    return this.success(data, '充值虚拟币成功');

  @Post('consume')
  @ApiOperation({ summary: '用户消费虚拟币');
  async consume(@CurrentTenant() tenantId: string, @Body() consumeDto: ConsumeVirtualCurrencyDto) {
    const data = await this.virtualCurrencyService.consumeUserWallet(consumeDto, tenantId);
    return this.success(data, '消费虚拟币成功');

  @Get('wallets')
  @ApiOperation({ summary: '查询用户钱包余额');
  @ApiOkResponse({ type: [WalletBalanceResponseDto]);
  async getUserWallets(
    @CurrentTenant() tenantId: string,
    @Query('userId', ParseIntPipe) userId: number,
    @Query('currencyTypeId', new DefaultValuePipe(0), ParseIntPipe) currencyTypeId: number = 0,
  ) {
    const queryDto: QueryUserWalletDto = {
      userId,
      currencyTypeId: currencyTypeId || undefined,
    };
    const data = await this.virtualCurrencyService.getUserWallets(queryDto, tenantId);
    return this.success(data);

  @Get('transactions')
  @ApiOperation({ summary: '查询用户交易记录');
  @ApiPaginatedResponse(TransactionResponseDto)
  @ApiQuery({ name: 'userId', type: Number, required: true 
  @ApiQuery({ name: 'currencyTypeId', type: Number, required: false 
  @ApiQuery({ name: 'type', enum: TransactionType, required: false 
  @ApiQuery({ name: 'startTime', type: String, required: false 
  @ApiQuery({ name: 'endTime', type: String, required: false 
  @ApiQuery({ name: 'page', type: Number, required: false 
  @ApiQuery({ name: 'pageSize', type: Number, required: false 
  async getUserTransactions(
    @CurrentTenant() tenantId: string,
    @Query('userId', ParseIntPipe) userId: number,
    @Query('currencyTypeId', new DefaultValuePipe(0), ParseIntPipe) currencyTypeId: number = 0,
    @Query('type') type?: TransactionType,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number = 10,
  ) {
    const options = new PaginationOptions(pageSize, (page - 1) * pageSize, page, pageSize);
    const queryDto: QueryTransactionDto = {
      userId,
      currencyTypeId: currencyTypeId || undefined,
      type,
      startTime,
      endTime,
      page,
      pageSize,
    };
    const data = await this.virtualCurrencyService.getUserTransactions(queryDto, options, tenantId);
    return this.success(data);

  @Post('features/:featureCode/cost')
  @ApiOperation({ summary: '设置功能虚拟币消耗');
  async setFeatureCost(
    @CurrentTenant() tenantId: string,
    @Param('featureCode') featureCode: string,
    @Body() costDto: FeatureCurrencyCostDto,
  ) {
    const data = await this.virtualCurrencyService.setFeatureCurrencyCost(
      featureCode,
      costDto,
      // tenantId // not in schema
    );
    return this.success(data, '设置功能虚拟币消耗成功');
/**
 * 用户虚拟币控制器
 * 处理用户对自己虚拟币的查询
 */
@ApiTags('用户虚拟币')
@Controller('virtual-currency')
export class UserVirtualCurrencyController extends BaseController {
  constructor(private readonly virtualCurrencyService: VirtualCurrencyService) {
    super();
  }

  @Get('balance')
  @ApiOperation({ summary: '查询虚拟币余额');
  @ApiOkResponse({ type: [WalletBalanceResponseDto]);
  @ApiQuery({ name: 'userId', type: Number, required: true 
  @ApiQuery({ name: 'currencyTypeId', type: Number, required: false 
  async getBalance(
    @CurrentTenant() tenantId: string,
    @Query('userId', ParseIntPipe) userId: number,
    @Query('currencyTypeId', new DefaultValuePipe(0), ParseIntPipe) currencyTypeId: number = 0,
  ) {
    const queryDto: QueryUserWalletDto = {
      userId,
      currencyTypeId: currencyTypeId || undefined,
    };
    const data = await this.virtualCurrencyService.getUserWallets(queryDto, tenantId);
    return this.success(data);

  @Get('transactions/list')
  @ApiOperation({ summary: '查询虚拟币交易记录');
  @ApiPaginatedResponse(TransactionResponseDto)
  @ApiQuery({ name: 'userId', type: Number, required: true 
  @ApiQuery({ name: 'currencyTypeId', type: Number, required: false 
  @ApiQuery({ name: 'type', enum: TransactionType, required: false 
  @ApiQuery({ name: 'startTime', type: String, required: false 
  @ApiQuery({ name: 'endTime', type: String, required: false 
  @ApiQuery({ name: 'page', type: Number, required: false 
  @ApiQuery({ name: 'pageSize', type: Number, required: false 
  async getTransactions(
    @CurrentTenant() tenantId: string,
    @Query('userId', ParseIntPipe) userId: number,
    @Query('currencyTypeId', new DefaultValuePipe(0), ParseIntPipe) currencyTypeId: number = 0,
    @Query('type') type?: TransactionType,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number = 10,
  ) {
    const options = new PaginationOptions(pageSize, (page - 1) * pageSize, page, pageSize);
    const queryDto: QueryTransactionDto = {
      userId,
      currencyTypeId: currencyTypeId || undefined,
      type,
      startTime,
      endTime,
      page,
      pageSize,
    };
    const data = await this.virtualCurrencyService.getUserTransactions(queryDto, options, tenantId);
    return this.success(data);