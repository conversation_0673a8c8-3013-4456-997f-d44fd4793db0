import { Module } from '@nestjs/common';

import {
  VirtualCurrencyTypeController,
  TenantVirtualCurrencyController,
  UserVirtualCurrencyController,
} from './virtual-currency.controller';
import { VirtualCurrencyService } from './virtual-currency.service';

import { PrismaModule } from '@/core/database/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [
    VirtualCurrencyTypeController,
    TenantVirtualCurrencyController,
    UserVirtualCurrencyController,
  ],
  providers: [VirtualCurrencyService],
  exports: [VirtualCurrencyService],
)
export class VirtualCurrencyModule {}
