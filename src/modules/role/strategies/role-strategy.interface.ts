import { CreateRoleDto, RoleDto, RoleListDto, UpdateRoleDto } from '../dto/role.dto';

import { PaginationOptions } from '@/core/pagination-options/pagination-options';
import { IBaseStrategy } from '@/core/strategies/base-strategy.interface';

/**
 * 角色策略接口
 * 定义角色相关的操作
 */
export interface IRoleStrategy extends IBaseStrategy {
  /**
   * 创建角色
   * @param createRoleDto 创建角色数据
   * @param tenantId 租户ID（可选）
   * @returns 创建的角色
   */
  create(createRoleDto: CreateRoleDto, tenantId?: string): Promise<RoleDto>;

  /**
   * 查询所有角色
   * @param where 查询条件
   * @param options 分页选项
   * @param tenantId 租户ID（可选）
   * @returns 角色列表
   */
  findAll(where: any, options: PaginationOptions, tenantId?: string): Promise<RoleListDto>;

  /**
   * 查询单个角色
   * @param id 角色ID
   * @param tenantId 租户ID（可选）
   * @returns 角色信息
   */
  findOne(id: string, tenantId?: string): Promise<RoleDto>;

  /**
   * 更新角色
   * @param id 角色ID
   * @param updateRoleDto 更新角色数据
   * @param tenantId 租户ID（可选）
   * @returns 更新后的角色
   */
  update(id: string, updateRoleDto: UpdateRoleDto, tenantId?: string): Promise<RoleDto>;

  /**
   * 删除角色
   * @param id 角色ID
   * @param tenantId 租户ID（可选）
   * @returns 删除结果
   */
  remove(id: string, tenantId?: string): Promise<{ success: boolean }>;

  /**
   * 分配角色给用户
   * @param userId 用户ID
   * @param roleIds 角色ID列表
   * @param tenantId 租户ID（可选）
   * @returns 分配结果
   */
  assignRolesToUser(
    userId: number,
    roleIds: string[],
    tenantId?: string,
  ): Promise<{ success: boolean }>;

  /**
   * 移除用户角色
   * @param userId 用户ID
   * @param roleId 角色ID
   * @param tenantId 租户ID（可选）
   * @returns 移除结果
   */
  removeRoleFromUser(
    userId: number,
    roleId: number,
    tenantId?: string,
  ): Promise<{ success: boolean }>;

  /**
   * 获取角色权限
   * @param roleId 角色ID
   * @param tenantId 租户ID（可选）
   * @returns 权限列表
   */
  getRolePermissions(roleId: number, tenantId?: string): Promise<any[]>;
}
