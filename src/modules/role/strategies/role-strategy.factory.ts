import { Injectable, Scope } from '@nestjs/common';

import { IRoleStrategy } from './role-strategy.interface';

import { BaseStrategyFactory } from '@/core/strategies/base-strategy.factory';

/**
 * 角色策略类型
 */
export enum RoleStrategyType {
  SYSTEM = 'SYSTEM',
  TENANT = 'TENANT',
}

/**
 * 角色策略工厂
 * 用于创建和管理角色策略实例
 */
@Injectable({ scope: Scope.DEFAULT 
export class RoleStrategyFactory extends BaseStrategyFactory<IRoleStrategy> {
  /**
   * 根据用户类型获取策略
   * @param userType 用户类型
   * @returns 角色策略实例
   */
  getStrategyByUserType(userType: string): IRoleStrategy {
    console.log(`RoleStrategyFactory.getStrategyByUserType() 被调用，用户类型: ${userType}`);

    // 确定策略类型
    const type = userType === 'SYSTEM' ? RoleStrategyType.SYSTEM : RoleStrategyType.TENANT;
    console.log(`确定的策略类型: ${type}`);

    // 获取所有已注册的策略
    const allStrategies = this.getAllStrategies();
    console.log(`已注册的策略数量: ${allStrategies.length}`);

    if (allStrategies.length > 0)  {
      console.log(`已注册的策略类型: ${allStrategies.map(s => s.getType()).join(', ')}`);
    } else {
      console.log('没有注册任何策略');

    // 尝试从缓存中获取策略
    const strategy = this.getStrategy(type);

    if (strategy)  {
      console.log(`成功获取到策略: ${type}`);

      // 验证策略是否完整
      if (typeof strategy.findAll !== 'function')  {
        console.error(`策略 ${type} 不完整，缺少 findAll 方法`);
        throw new Error(`策略 ${type} 不完整，缺少 findAll 方法`);
      }

      return strategy;
    // 如果缓存中没有策略，记录错误并抛出异常
    const errorMessage = `未找到类型为 ${type} 的策略，请确保在模块初始化时正确注册了策略`;
    console.error(errorMessage);

    // 尝试手动创建策略
    console.log('尝试手动创建策略...');

    // 抛出异常
    throw new Error(errorMessage);
  }
}
