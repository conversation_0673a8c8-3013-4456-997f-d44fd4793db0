import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { IsArray, IsInt, IsNotEmpty, IsOptional, IsString, Min } from 'class-validator';

/**
 * 创建角色DTO
 */
export class CreateRoleDto {
  @ApiProperty({ description: '角色名称', example: '管理员');
  @IsString()
  @IsNotEmpty({ message: '角色名称不能为空');
  name: string;

  @ApiPropertyOptional({
    description: '角色编码（唯一标识符，不提供时自动生成）',
    example: 'admin',
  )
  @IsString()
  @IsOptional()
  code?: string;

  @ApiPropertyOptional({ description: '角色描述', example: '系统管理员角色');
  @IsString()
  @IsOptional()
  remark?: string;

  @ApiPropertyOptional({ description: '角色状态：0-禁用，1-启用', example: 1, default: 1 
  @IsInt()
  @Min(0)
  @IsOptional()
  status?: number;

  @ApiPropertyOptional({ description: '权限ID列表', type: [Number]);
  @IsArray()
  @IsOptional()
  permissionIds?: number[];

  @ApiPropertyOptional({ description: '菜单ID列表', type: [Number]);
  @IsArray()
  @IsOptional()
  menuIds?: number[];
}

/**
 * 更新角色DTO
 */
export class UpdateRoleDto extends PartialType(CreateRoleDto) {}

/**
 * 角色DTO
 */
export class RoleDto {
  @ApiProperty({ description: '角色ID');
  id: string;

  @ApiProperty({ description: '角色名称');
  name: string;

  @ApiProperty({ description: '角色编码');
  code: string;

  @ApiPropertyOptional({ description: '角色描述');
  remark?: string;

  @ApiProperty({ description: '角色状态：0-禁用，1-启用', default: 1 
  status: number;

  @ApiProperty({ description: '创建时间');
  createTime: string;

  @ApiProperty({ description: '更新时间');
  updateTime: string;

  @ApiPropertyOptional({ description: '权限列表', type: [Object]);
  permissions?: any[];

  @ApiPropertyOptional({ description: '菜单列表', type: [Object]);
  menus?: any[];
}

/**
 * 角色列表DTO
 */
export class RoleListDto {
  @ApiProperty({ description: '角色列表', type: [RoleDto]);
  items: RoleDto[];

  @ApiProperty({ description: '总数');
  total: number;

  @ApiProperty({ description: '页码');
  page: number;

  @ApiProperty({ description: '每页数量');
  pageSize: number;
}

/**
 * 分配角色DTO
 */
export class AssignRoleDto {
  @ApiProperty({ description: '用户ID');
  @IsInt()
  @Min(1)
  userId: number;

  @ApiProperty({ description: '角色ID列表', type: [String]);
  @IsArray()
  @IsNotEmpty({ message: '角色ID列表不能为空');
  roleIds: string[];
}
