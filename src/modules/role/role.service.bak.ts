import { Injectable, Scope } from '@nestjs/common';

import { AssignRoleDto, CreateRoleDto, RoleDto, RoleListDto, UpdateRoleDto } from './dto/role.dto';
import { RoleStrategyFactory } from './strategies/role-strategy.factory';

import { CacheService } from '@/core/cache/cache.service';
import { BaseService } from '@/core/common/base/base.service';
import { QueryBuilderService } from '@/core/common/services/query-builder.service';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 角色服务
 * 统一处理系统角色和租户角色的操作
 */
@Injectable({ scope: Scope.DEFAULT 
export class RoleService extends BaseService {
  constructor(
    private readonly roleStrategyFactory: RoleStrategyFactory,
    private readonly cacheService: CacheService,
    private readonly queryBuilderService: QueryBuilderService,
  ) {
    super(RoleService.name);
  }

  /**
   * 生成角色缓存键
   * @param userType 用户类型
   * @param tenantId 租户ID
   * @returns 缓存键
   */
  private generateRoleCacheKey(userType: string, tenantId?: string): string {
    return `role:${userType}:${tenantId || 'system'}:list`;
  }

  /**
   * 生成角色权限缓存键
   * @param roleId 角色ID
   * @param userType 用户类型
   * @param tenantId 租户ID
   * @returns 缓存键
   */
  private generateRolePermissionsCacheKey(
    roleId: number,
    userType: string,
    tenantId?: string,
  ): string {
    return `role:${userType}:${tenantId || 'system'}:${roleId}:permissions`;
  }

  /**
   * 创建角色
   * @param createRoleDto 创建角色数据
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 创建的角色
   */
  async create(
    createRoleDto: CreateRoleDto,
    userType: string,
    tenantId?: string,
  ): Promise<RoleDto> {
    try {
      // 验证角色名称和编码是否已存在
      if (createRoleDto.name)  {
        const nameExists = await this.isNameExists(createRoleDto.name, userType, tenantId);
        if (nameExists)  {
          this.alreadyExists('角色', '名称', createRoleDto.name);
      }

      if (createRoleDto.code)  {
        const codeExists = await this.isCodeExists(createRoleDto.code, userType, tenantId);
        if (codeExists)  {
          this.alreadyExists('角色', '编码', createRoleDto.code);
      }

      const strategy = this.roleStrategyFactory.getStrategyByUserType(userType);
      const result = await strategy.create(createRoleDto, tenantId);

      // 清除角色缓存
      await this.clearRoleCache(userType, tenantId);

      return result;
    } catch (error) {
      this.logError('创建角色失败', error);
      throw error;
    }
  }

  /**
   * 检查角色名称是否已存在
   * @param name 角色名称
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 是否已存在
   */
  private async isNameExists(name: string, userType: string, tenantId?: string): Promise<boolean> {
    // 这里应该实现检查角色名称是否已存在的逻辑
    // 暂时返回false，实际应该根据数据库查询结果返回
    return false;
  /**
   * 检查角色编码是否已存在
   * @param code 角色编码
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 是否已存在
   */
  private async isCodeExists(code: string, userType: string, tenantId?: string): Promise<boolean> {
    // 这里应该实现检查角色编码是否已存在的逻辑
    // 暂时返回false，实际应该根据数据库查询结果返回
    return false;
  /**
   * 查询所有角色
   * @param queryParams 查询参数
   * @param options 分页选项
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 角色列表
   */
  async findAll(
    queryParams: any,
    options: PaginationOptions,
    userType: string,
    tenantId?: string,
  ): Promise<RoleListDto> {
    try {
      this.logger.debug(`RoleService.findAll() 开始执行`);
      this.logger.debug(
        `参数: ${JSON.stringify(queryParams)}, 用户类型: ${userType}, 租户ID: ${tenantId || 'system'}`,
      );

      // 使用查询条件构建器构建查询条件
      const where = this.queryBuilderService.buildRoleQueryCondition(queryParams);
      this.logger.debug(`构建的查询条件: ${JSON.stringify(where)}`);

      // 获取策略
      this.logger.debug(`开始获取策略，用户类型: ${userType}`);

      try {
        const strategy = this.roleStrategyFactory.getStrategyByUserType(userType);

        if (!strategy)  {
          this.logger.error(`未找到用户类型 ${userType} 对应的策略`);
          throw new Error(`未找到用户类型 ${userType} 对应的策略`);
        }

        this.logger.debug(`获取到策略: ${strategy.getType()}`);

        // 验证策略是否实现了findAll方法
        if (typeof strategy.findAll !== 'function')  {
          this.logger.error(`策略 ${strategy.getType()} 不完整，缺少 findAll 方法`);
          throw new Error(`策略 ${strategy.getType()} 不完整，缺少 findAll 方法`);
        }

        // 直接从数据库获取，暂时不使用缓存
        this.logger.debug(`开始调用策略的findAll方法`);

        // 使用try-catch包装策略调用
        try {
          const result = await strategy.findAll(where, options, tenantId);
          this.logger.debug(`策略的findAll方法调用成功`);
          this.logger.debug(`角色列表查询结果: ${JSON.stringify(result).substring(0, 200)}...`);
          return result;
        } catch (strategyError) {
          this.logger.error(`调用策略的findAll方法失败: ${strategyError.message}`);
          throw strategyError;
        }
      } catch (factoryError) {
        this.logger.error(`获取或使用策略失败: ${factoryError.message}`);
        throw factoryError;
      }
    } catch (error) {
      // 使用安全的错误处理
      this.logError('查询角色列表失败', error);

      // 抛出错误，让控制器处理
      throw error;
    }
  }

  /**
   * 查询单个角色
   * @param id 角色ID
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 角色信息
   */
  async findOne(id: string, userType: string, tenantId?: string): Promise<RoleDto> {
    try {
      const strategy = this.roleStrategyFactory.getStrategyByUserType(userType);
      const role = await strategy.findOne(id, tenantId);

      if (!role)  {
        this.notFound('角色', id);

      return role;
    } catch (error) {
      this.logError('查询角色详情失败', error);
      throw error;
    }
  }

  /**
   * 更新角色
   * @param id 角色ID
   * @param updateRoleDto 更新角色数据
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 更新后的角色
   */
  async update(
    id: string,
    updateRoleDto: UpdateRoleDto,
    userType: string,
    tenantId?: string,
  ): Promise<RoleDto> {
    try {
      // 先检查角色是否存在
      const existingRole = await this.findOne(id, userType, tenantId);

      // 如果角色不存在，findOne方法会抛出异常，所以这里不需要再次检查

      const strategy = this.roleStrategyFactory.getStrategyByUserType(userType);
      const result = await strategy.update(id, updateRoleDto, tenantId);

      // 清除角色缓存
      await this.clearRoleCache(userType, tenantId);
      // 清除角色权限缓存
      await this.clearRolePermissionsCache(id, userType, tenantId);

      return result;
    } catch (error) {
      this.logError(`更新角色失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除角色
   * @param id 角色ID
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 删除结果
   */
  async remove(id: string, userType: string, tenantId?: string): Promise<{ success: boolean }> {
    try {
      // 先检查角色是否存在
      await this.findOne(id, userType, tenantId);

      // 检查角色是否有关联的用户
      const hasUsers = await this.hasAssociatedUsers(id, userType, tenantId);
      if (hasUsers)  {
        this.validationError('角色已分配给用户，无法删除');

      const strategy = this.roleStrategyFactory.getStrategyByUserType(userType);
      const result = await strategy.remove(id, tenantId);

      // 清除角色缓存
      await this.clearRoleCache(userType, tenantId);
      // 清除角色权限缓存
      await this.clearRolePermissionsCache(id, userType, tenantId);

      return result;
    } catch (error) {
      this.logError(`删除角色失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 检查角色是否有关联的用户
   * @param roleId 角色ID
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 是否有关联的用户
   */
  private async hasAssociatedUsers(
    roleId: number,
    userType: string,
    tenantId?: string,
  ): Promise<boolean> {
    const strategy = this.roleStrategyFactory.getStrategyByUserType(userType);
    // 这里假设策略中有检查角色是否有关联用户的方法，如果没有，需要在策略接口中添加
    // 如果策略中没有此方法，可以根据实际情况实现
    return false; // 暂时返回false，实际应该根据数据库查询结果返回
  }

  /**
   * 分配角色给用户
   * @param assignRoleDto 分配角色数据
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 分配结果
   */
  async assignRolesToUser(
    assignRoleDto: AssignRoleDto,
    userType: string,
    tenantId?: string,
  ): Promise<{ success: boolean }> {
    try {
      // 验证用户ID和角色ID是否有效
      if (!assignRoleDto.userId)  {
        this.validationError('用户ID不能为空');

      if (!assignRoleDto.roleIds || assignRoleDto.roleIds.length === 0)  {
        this.validationError('角色ID不能为空');

      // 验证所有角色是否存在
      for (const roleId of assignRoleDto.roleIds) {
        await this.findOne(roleId, userType, tenantId);

      const strategy = this.roleStrategyFactory.getStrategyByUserType(userType);
      return strategy.assignRolesToUser(assignRoleDto.userId, assignRoleDto.roleIds, tenantId); catch (error) {
      this.logError(`分配角色给用户失败，用户ID: ${assignRoleDto.userId}`, error);
      throw error;
    }
  }

  /**
   * 移除用户角色
   * @param userId 用户ID
   * @param roleId 角色ID
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 移除结果
   */
  async removeRoleFromUser(
    userId: number,
    roleId: number,
    userType: string,
    tenantId?: string,
  ): Promise<{ success: boolean }> {
    try {
      // 验证角色是否存在
      await this.findOne(roleId, userType, tenantId);

      // 验证用户ID是否有效
      if (!userId)  {
        this.validationError('用户ID不能为空');

      const strategy = this.roleStrategyFactory.getStrategyByUserType(userType);
      return strategy.removeRoleFromUser(userId, roleId, tenantId); catch (error) {
      this.logError(`移除用户角色失败，用户ID: ${userId}, 角色ID: ${roleId}`, error);
      throw error;
    }
  }

  /**
   * 获取角色权限
   * @param roleId 角色ID
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 权限列表
   */
  async getRolePermissions(roleId: number, userType: string, tenantId?: string): Promise<any[]> {
    try {
      // 验证角色是否存在
      await this.findOne(roleId, userType, tenantId);

      // 使用缓存
      const cacheKey = this.generateRolePermissionsCacheKey(roleId, userType, tenantId);

      return this.cacheService.getOrSet(
        cacheKey,
        async () => {
          this.logger.log(`角色权限缓存未命中，从数据库获取，角色ID: ${roleId}`);
          const strategy = this.roleStrategyFactory.getStrategyByUserType(userType);
          return strategy.getRolePermissions(roleId, tenantId);,
        60 * 60 * 1000, // 缓存1小时
      );
    } catch (error) {
      this.logError(`获取角色权限失败，角色ID: ${roleId}`, error);
      throw error;
    }
  }

  /**
   * 清除角色缓存
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   */
  private async clearRoleCache(userType: string, tenantId?: string): Promise<void> {
    try {
      const cacheKey = this.generateRoleCacheKey(userType, tenantId);
      await this.cacheService.delete(cacheKey);

      this.logger.log(`清除角色缓存成功，用户类型: ${userType}, 租户ID: ${tenantId || 'system'}`); catch (error) {
      this.logError(
        `清除角色缓存失败，用户类型: ${userType}, 租户ID: ${tenantId || 'system'}`,
        error,
      );
  }

  /**
   * 清除角色权限缓存
   * @param roleId 角色ID
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   */
  private async clearRolePermissionsCache(
    roleId: number,
    userType: string,
    tenantId?: string,
  ): Promise<void> {
    try {
      const cacheKey = this.generateRolePermissionsCacheKey(roleId, userType, tenantId);
      await this.cacheService.delete(cacheKey);

      this.logger.log(`清除角色权限缓存成功，角色ID: ${roleId}`); catch (error) {
      this.logError(`清除角色权限缓存失败，角色ID: ${roleId}`, error);
  }
}
