import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsBoolean, IsInt, IsObject, Min } from 'class-validator';

export class CreateWebsiteFormDto {
  @ApiProperty({ description: '表单名称');
  @IsString()
  name: string;

  @ApiProperty({ description: '表单标题');
  @IsString()
  title: string;

  @ApiProperty({ description: '表单描述', required: false 
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: '表单字段配置',
    type: 'object',
    example: {
      fields: [
        {
          id: 'name',
          type: 'text',
          label: '姓名',
          placeholder: '请输入您的姓名',
          required: true,
          validation: {
            minLength: 2,
            maxLength: 50,
          },
        },
        {
          id: 'email',
          type: 'email',
          label: '邮箱',
          placeholder: '请输入您的邮箱',
          required: true,
          validation: {
            pattern: '^[^@]+@[^@]+\\.[^@]+$',
          },
        },
        {
          id: 'phone',
          type: 'tel',
          label: '电话',
          placeholder: '请输入您的电话',
          required: false,
          validation: {
            pattern: '^1[3-9]\\d{9}$',
          },
        },
        {
          id: 'company',
          type: 'text',
          label: '公司名称',
          placeholder: '请输入您的公司名称',
          required: false,
          validation: {
            maxLength: 100,
          },
        },
        {
          id: 'message',
          type: 'textarea',
          label: '留言',
          placeholder: '请输入您的留言',
          required: true,
          validation: {
            minLength: 10,
            maxLength: 1000,
          },
        },
      ],
    },
  )
  @IsObject()
  fields: JsonValue;

  @ApiProperty({
    description: '表单设置',
    type: 'object',
    required: false,
    example: {
      submitText: '提交',
      successMessage: '感谢您的留言，我们会尽快回复！',
      errorMessage: '提交失败，请重试',
      redirectUrl: '/thank-you',
      emailNotification: {
        enabled: true,
        to: '<EMAIL>',
        subject: '新的表单提交',
        template: 'form-notification',
      },
      autoReply: {
        enabled: false,
        subject: '感谢您的留言',
        template: 'auto-reply',
      },
      captcha: {
        enabled: false,
        type: 'recaptcha',
      },
      rateLimit: {
        enabled: true,
        maxSubmissions: 5,
        timeWindow: 3600,
      },
      storage: {
        saveToDatabase: true,
        exportFormat: 'csv',
      },
    },
  )
  @IsOptional()
  @IsObject()
  settings?: JsonValue;

  @ApiProperty({ description: '是否激活', default: true 
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean = true;

  @ApiProperty({ description: '所属网站ID');
  @IsInt()
  @Min(1)
  @Type(() => Number)
  // websiteId: // check schema number;
}
