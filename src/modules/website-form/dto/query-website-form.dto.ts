import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString, IsInt, IsBoolean, Min, Max } from 'class-validator';

export class QueryWebsiteFormDto {
  @ApiProperty({ description: '页码', default: 1, required: false 
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({ description: '每页数量', default: 20, required: false 
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  limit?: number = 20;

  @ApiProperty({ description: '关键词搜索', required: false 
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({ description: '所属网站ID过滤', required: false 
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  websiteId?: number;

  @ApiProperty({ description: '是否激活状态过滤', required: false 
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean;

  @ApiProperty({
    description: '排序字段',
    enum: ['createTime', 'updateTime', 'name', 'title'],
    required: false,
  )
  @IsOptional()
  @IsString()
  sortBy?: string = 'createTime';

  @ApiProperty({
    description: '排序方向',
    enum: ['asc', 'desc'],
    required: false,
  )
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';

  @ApiProperty({ description: '是否包含关联数据', required: false 
  @IsOptional()
  @Type(() => Boolean)
  includeRelations?: boolean = false;

  @ApiProperty({ description: '是否包含提交统计', required: false 
  @IsOptional()
  @Type(() => Boolean)
  includeStats?: boolean = false;
}
