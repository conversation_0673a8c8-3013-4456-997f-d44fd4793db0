import { Injectable, NotFoundException, ConflictException, Inject } from '@nestjs/common';

import { CreateWebsiteFormDto } from './dto/create-website-form.dto';
import { QueryWebsiteFormDto } from './dto/query-website-form.dto';
import { UpdateWebsiteFormDto } from './dto/update-website-form.dto';
// import any // WebsiteFormEntity not exist - table does not exist

import { DatabaseFactory } from '@/core/database/database.factory';
import { BaseBusinessService } from '@/modules/business/base/business.service';

@Injectable()
export class WebsiteFormService extends BaseBusinessService {
  constructor(databaseFactory: DatabaseFactory) {
    super(databaseFactory, 'WebsiteFormService');
  async create(tenantId: number, createDto: CreateWebsiteFormDto): Promise<any> {
    // 检查网站是否存在
    const website = await this.tenantDb.website.findFirst({
      where: {
        id: createDto.websiteId,
        // tenantId // not in schema
      },
    )
    if (!website)  {
      throw new NotFoundException(`网站 ID ${createDto.websiteId} 不存在`);
    // 检查同一网站下表单名称是否已存在
    const existingForm = await this.tenantDb.website.findFirst({
      where: {
        name: createDto.name,
        // websiteId: // check schema createDto.websiteId,
        // tenantId // not in schema
      },
    )
    if (existingForm)  {
      throw new ConflictException(`表单名称 "${createDto.name}" 在该网站下已存在`);
    const form = await this.tenantDb.website.create({
      data: {
        ...createDto,
        // tenantId // not in schema
      },
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    )
    return this.mapToEntity(form )
  async findAll(tenantId: number, queryDto: QueryWebsiteFormDto = {; {
    const {
      page = 1,
      limit = 20,
      keyword,
      websiteId,
      isActive,
      sortBy = 'createTime',
      sortOrder = 'desc',
      includeRelations = false,
      includeStats = false,
    } = queryDto;

    const skip = (page - 1) * limit;
    const take = limit;

    // 构建查询条件
    const where: any = {
      // tenantId // not in schema
    };

    if (keyword)  {
      where.OR = [
        { name: { contains: keyword, mode: 'insensitive' } },
        { title: { contains: keyword, mode: 'insensitive' } },
        { description: { contains: keyword, mode: 'insensitive' } },
      ];
    }

    if (websiteId)  {
      where.websiteId = websiteId;
    }

    if (typeof isActive === 'boolean')  {
      where.isActive = isActive;
    }

    // 构建排序条件
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    // 构建包含关系
    const include = includeRelations
      ? {
          website: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
        }
      : undefined;

    const [forms, total] = await Promise.all([
      this.tenantDb.website.findMany({
        where,
        skip,
        take,
        orderBy,
        include,
      ),
      this.tenantDb.website.count({ where ),
    ]);

    let formsWithStats = forms;

    // 如果需要包含统计信息
    if (includeStats)  {
      formsWithStats = await Promise.all(
        forms.map(async form => {
          const submissionCount = await this.tenantDb.notification.count({
            where: {
              formId: form.id,
              // tenantId // not in schema
            },
          )
          return {
            ...form,
            submissionCount,
          };
        }), })
    return {
      data: formsWithStats.map(form => this.mapToEntity(form)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(tenantId: number, id: number): Promise<any> {
    const form = await this.tenantDb.website.findFirst({
      where: {
        id,
        // tenantId // not in schema
      },
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    )
    if (!form)  {
      throw new NotFoundException(`表单 ID ${id} 不存在`);
    // 获取提交统计
    const submissionCount = await this.tenantDb.notification.count({
      where: {
        formId: id,
        // tenantId // not in schema
      },
    )
    return this.mapToEntity({
      ...form,
      submissionCount,
    ;
  async update(
    tenantId: number,
    id: number,
    updateDto: UpdateWebsiteFormDto,
  ): Promise<any> {
    await this.findOne(tenantId, id); // 检查是否存在

    // 如果更新名称，检查名称是否冲突
    if (updateDto.name)  {
      const existingForm = await this.tenantDb.website.findFirst({
        where: {
          name: updateDto.name,
          // websiteId: // check schema updateDto.websiteId,
          // tenantId // not in schema
          NOT: { id },
        },
      )
      if (existingForm)  {
        throw new ConflictException(`表单名称 "${updateDto.name}" 已存在`);
    }

    const form = await this.tenantDb.website.update({
      where: {
        id,
        // tenantId // not in schema
      },
      data: updateDto,
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    )
    return this.mapToEntity(form ;
  async remove(tenantId: number, id: number): Promise<void> {
    await this.findOne(tenantId, id); // 检查是否存在

    // 检查是否有提交记录
    const submissionCount = await this.tenantDb.notification.count({
      where: {
        formId: id,
        // tenantId // not in schema
      },
    )
    if (submissionCount > 0)  {
      throw new ConflictException(
        `表单包含 ${submissionCount} 条提交记录，无法删除。请先删除所有提交记录。`, )
    await this.tenantDb.website.delete({
      where: {
        id,
        // tenantId // not in schema
      },
    )
  }

  async toggleActive(tenantId: number, id: number): Promise<any> {
    const form = await this.findOne(tenantId, id);

    const updatedForm = await this.tenantDb.website.update({
      where: {
        id,
        // tenantId // not in schema
      },
      data: {
        // isActive: // not in schema !form.isActive,
      },
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    )
    return this.mapToEntity(updatedForm ;
  async getFormStats(tenantId: number, formId?: number) {
    const where: any = { tenantId };
    if (formId)  {
      where.formId = formId;
    }

    const [totalSubmissions, recentSubmissions, statusDistribution] = await Promise.all([
      // 总提交数
      this.tenantDb.notification.count({ where ),

      // 最近7天提交数
      this.tenantDb.notification.count({
        where: {
          ...where,
          createTime: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          },
        },
      }),

      // 状态分布
      this.tenantDb.notification.groupBy({
        by: ['status'],
        where,
        _count: {
          status: true,
        },
      ),
    ]);

    return {
      totalSubmissions,
      recentSubmissions,
      statusDistribution: statusDistribution.reduce(
        (acc, item) => {
          acc[item.status] = item._count.status;
          return acc;
        },
        {} as Record<string, number>,
      ),
    };
  }

  async duplicateForm(tenantId: number, id: number, newName: string): Promise<any> {
    const originalForm = await this.findOne(tenantId, id);

    // 检查新名称是否冲突
    const existingForm = await this.tenantDb.website.findFirst({
      where: {
        name: newName,
        // websiteId: // check schema originalForm.websiteId,
        // tenantId // not in schema
      },
    )
    if (existingForm)  {
      throw new ConflictException(`表单名称 "${newName}" 已存在`);
    const duplicatedForm = await this.tenantDb.website.create({
      data: {
        name: newName,
        title: `${originalForm.title} (副本)`,
        description: originalForm.description,
        fields: originalForm.fields,
        settings: originalForm.settings,
        // isActive: // not in schema false, // 默认为非激活状态
        // websiteId: // check schema originalForm.websiteId,
        // tenantId // not in schema
      },
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    })
    return this.mapToEntity(duplicatedForm ;
  // 映射数据库模型到实体
  private mapToEntity(dbForm: any): any // WebsiteFormEntity not exist {
    return {
      id: dbForm.id,
      name: dbForm.name,
      title: dbForm.title,
      description: dbForm.description,
      fields: dbForm.fields,
      settings: dbForm.settings,
      // isActive: // not in schema dbForm.isActive,
      // websiteId: // check schema dbForm.websiteId,
      tenantId: dbForm.// tenantId // not in schema
      createTime: dbForm.createTime,
      updateTime: dbForm.updateTime,
      website: dbForm.website,
      submissionCount: dbForm.submissionCount,
    };
  }

  // 基础服务方法暂时留空，需要根据实际Schema调整
  async createForm(formData: any) {
    // TODO: 根据实际Schema调整
    throw new Error('Method not implemented - Schema fields need to be verified');
  async getFormList(query: any) {
    // TODO: 根据实际Schema调整
    throw new Error('Method not implemented - Schema fields need to be verified');
  async getFormById(id: number) {
    // TODO: 根据实际Schema调整
    throw new Error('Method not implemented - Schema fields need to be verified');
  async updateForm(id: number, updateData: any) {
    // TODO: 根据实际Schema调整
    throw new Error('Method not implemented - Schema fields need to be verified');
  async deleteForm(id: number) {
    // TODO: 根据实际Schema调整
    throw new Error('Method not implemented - Schema fields need to be verified');
}
