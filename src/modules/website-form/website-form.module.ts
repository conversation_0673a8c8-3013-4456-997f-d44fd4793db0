/*
 * @Author: ya<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-28 14:04:49
 * @LastEditors: yangwenqiang <EMAIL>
 * @LastEditTime: 2025-06-01 00:43:14
 * @FilePath: /multi-tenant-nestjs/src/modules/website-form/website-form.module.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { PrismaModule } from '@core/database/prisma/prisma.module';
import { Module } from '@nestjs/common';

import { WebsiteFormController } from './website-form.controller';
import { WebsiteFormService } from './website-form.service';

@Module({
  imports: [PrismaModule],
  controllers: [WebsiteFormController],
  providers: [WebsiteFormService],
  exports: [WebsiteFormService],
})
export class WebsiteFormModule {}
