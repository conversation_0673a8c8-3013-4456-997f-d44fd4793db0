import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as redisStore from 'cache-manager-redis-store';

import { FeatureCodeController } from './controllers/feature-code.controller';
import {
  FeatureMenuController,
  FeatureTemplateMenuController,
} from './controllers/feature-menu.controller';
import { FeatureTemplateController } from './controllers/feature-template.controller';
import { TenantConfigController } from './controllers/tenant-config.controller';
import { TenantFeatureMenuController } from './controllers/tenant-feature-menu.controller';
import { TenantFeatureController } from './controllers/tenant-feature.controller';
import { FeatureQuotaGuard } from './guards/feature-quota.guard';
import { FeatureGuard } from './guards/feature.guard';
import { FeatureCodeService } from './services/feature-code.service';
import { FeatureMenuService } from './services/feature-menu.service';
import { FeatureTemplateService } from './services/feature-template.service';
import { TenantConfigService } from './services/tenant-config.service';
import { TenantFeatureService } from './services/tenant-feature.service';
import { TenantMenuSyncService } from './services/tenant-menu-sync.service';
import { PrismaModule } from '../../core/database/prisma/prisma.module';
import { TenantModule } from '../tenant/tenant.module';

@Module({
  imports: [
    PrismaModule,
    TenantModule,
    CacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        store: redisStore,
        host: configService.get('REDIS_HOST', 'localhost'),
        port: configService.get('REDIS_PORT', 6379),
        ttl: 300, // 默认缓存5分钟
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [
    FeatureTemplateController,
    TenantFeatureController,
    TenantConfigController,
    FeatureCodeController,
    FeatureMenuController,
    FeatureTemplateMenuController,
    TenantFeatureMenuController,
  ],
  providers: [
    TenantFeatureService,
    FeatureTemplateService,
    TenantConfigService,
    FeatureCodeService,
    FeatureMenuService,
    TenantMenuSyncService,
    FeatureGuard,
    FeatureQuotaGuard,
  ],
  exports: [
    TenantFeatureService,
    FeatureTemplateService,
    TenantConfigService,
    FeatureCodeService,
    FeatureMenuService,
    TenantMenuSyncService,
    FeatureGuard,
    FeatureQuotaGuard,
  ],
})
export class TenantFeatureModule {}
