import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsInt, IsNotEmpty, IsOptional, IsString } from 'class-validator';

/**
 * 功能菜单DTO
 */
export class FeatureMenuDto {
  @ApiProperty({ description: '功能菜单ID', example: 1, required: false 
  @IsInt()
  @IsOptional()
  id?: number;

  @ApiProperty({ description: '功能代码', example: 'ai.ppt');
  @IsString()
  @IsNotEmpty()
  featureCode: string;

  @ApiProperty({ description: '菜单ID', example: 1 
  @IsInt()
  @IsNotEmpty()
  menuId: number;

  @ApiProperty({ description: '创建时间', required: false 
  @IsOptional()
  createdAt?: Date;

  @ApiProperty({ description: '更新时间', required: false 
  @IsOptional()
  updatedAt?: Date;
}

/**
 * 功能菜单关联DTO
 */
export class FeatureMenusDto {
  @ApiProperty({ description: '功能代码', example: 'ai.ppt');
  @IsString()
  @IsNotEmpty()
  featureCode: string;

  @ApiProperty({ description: '菜单ID列表', example: [1, 2, 3], type: [Number]);
  @IsArray()
  @IsInt({ each: true 
  menuIds: number[];
}

/**
 * 设置功能菜单关联DTO
 * 用于设置功能代码关联的菜单，功能代码从URL路径参数中获取
 */
export class SetFeatureMenusDto {
  @ApiProperty({ description: '菜单ID列表', example: [1, 2, 3], type: [Number]);
  @IsArray()
  @IsInt({ each: true 
  menuIds: number[];
}

/**
 * 批量设置功能菜单DTO
 */
export class BatchFeatureMenusDto {
  @ApiProperty({ description: '租户ID列表', example: [1, 2, 3], type: [Number]);
  @IsArray()
  @IsInt({ each: true 
  tenantIds: number[];

  @ApiProperty({ description: '菜单ID列表', example: [1, 2, 3], type: [Number]);
  @IsArray()
  @IsInt({ each: true 
  menuIds: number[];
}

/**
 * 同步租户菜单DTO
 */
export class SyncTenantMenusDto {
  @ApiProperty({ description: '租户ID', example: 1 
  @IsInt()
  @IsNotEmpty()
  tenantId: number;
}
