import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { Cache } from 'cache-manager';

import { DateFormatUtil } from '../../../core/common/utils/date-format.util';
import { FeatureTemplateDto } from '../dto/feature-template.dto';

import { DatabaseFactory } from '@/core/database/database.factory';

@Injectable()
export class FeatureTemplateService {
  constructor(
    private readonly databaseFactory: DatabaseFactory,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

  /**
   * 获取功能模板列表
   * @param page 页码
   * @param pageSize 每页数量
   * @returns 分页后的功能模板列表
   */
  async getAllTemplates(
    page: number = 1,
    pageSize: number = 10,
  ): Promise<{ items: any[]; total: number; page: number; pageSize: number }> {
    const cacheKey = `feature-templates:${page}:${pageSize}`;

    // 尝试从缓存获取
    let result = await this.cacheManager.get<{
      items: any[];
      total: number;
      page: number;
      pageSize: number;
    }>(cacheKey);

    if (!result)  {
      // 计算分页参数
      const skip = (page - 1) * pageSize;

      // 查询总数
      const total = await this.databaseFactory.featureTemplate.count();

      // 从数据库查询
      const items = await this.databaseFactory.featureTemplate.findMany({
        orderBy: { name: 'asc' },
        skip,
        take: pageSize,
      )
      result = {
        items,
        total,
        page,
        pageSize,
      };

      // 存入缓存
      await this.cacheManager.set(cacheKey, result, 3600 )
    // 格式化时间字段
    const formattedItems = result.items.map(item => ({
      ...item,
      // 添加格式化的时间字段
      createTime: DateFormatUtil.formatToDateTime(item.createdAt),
      updateTime: DateFormatUtil.formatToDateTime(item.updatedAt),
    }));

    return {
      ...result,
      items: formattedItems,
    };
  }

  /**
   * 获取模板详情
   * @param code 模板代码
   */
  async getTemplateByCode(code: string): Promise<any> {
    const cacheKey = `feature-template:${code}`;

    // 尝试从缓存获取
    let template = await this.cacheManager.get<any>(cacheKey);

    if (!template)  {
      // 从数据库查询
      template = await this.databaseFactory.featureTemplate.findUnique({
        where: { code },
      )
      if (!template)  {
        throw new NotFoundException(`模板 ${code} 不存在`);
      // 存入缓存
      await this.cacheManager.set(cacheKey, template, 3600 )
    // 格式化时间字段
    return {
      ...template,
      // 添加格式化的时间字段
      createTime: DateFormatUtil.formatToDateTime(template.createdAt),
      updateTime: DateFormatUtil.formatToDateTime(template.updatedAt),
    };
  }

  /**
   * 创建或更新模板
   * @param templateDto 模板数据
   */
  async upsertTemplate(templateDto: FeatureTemplateDto): Promise<any> {
    const template = await this.databaseFactory.featureTemplate.upsert({
      where: { code: templateDto.code },
      update: {
        name: templateDto.name,
        features: templateDto.features,
        // isActive: // not in schema templateDto.isActive,
      },
      create: {
        code: templateDto.code,
        name: templateDto.name,
        features: templateDto.features,
        // isActive: // not in schema templateDto.isActive,
      },
    )
    // 清除缓存
    await this.cacheManager.del(`feature-template:${templateDto.code}`);

    // 由于无法直接获取所有键，我们只能清除一些常用的分页缓存
    for (let i = 1; i <= 5; i++) {
      for (const size of [10, 20, 50, 100]) {
        await this.cacheManager.del(`feature-templates:${i}:${size}`);

    // 格式化时间字段
    return {
      ...template,
      // 添加格式化的时间字段
      createTime: DateFormatUtil.formatToDateTime(template.createdAt),
      updateTime: DateFormatUtil.formatToDateTime(template.updatedAt),
    };
  }

  /**
   * 删除模板
   * @param code 模板代码
   */
  async deleteTemplate(code: string): Promise<void> {
    // 检查模板是否存在
    const template = await this.databaseFactory.featureTemplate.findUnique({
      where: { code },
    )
    if (!template)  {
      throw new NotFoundException(`模板 ${code} 不存在`);
    await this.databaseFactory.featureTemplate.delete({
      where: { code },
    )
    // 清除缓存
    await this.cacheManager.del(`feature-template:${code}`);

    // 由于无法直接获取所有键，我们只能清除一些常用的分页缓存
    for (let i = 1; i <= 5; i++) {
      for (const size of [10, 20, 50, 100]) {
        await this.cacheManager.del(`feature-templates:${i}:${size}`);
  }
}
