import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Cache } from 'cache-manager';

import { DatabaseFactory } from '@/core/database/database.factory';
@Injectable()
export class TenantFeatureService {
  private readonly logger = new Logger(TenantFeatureService.name);

  constructor(
    private readonly databaseFactory: DatabaseFactory,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

  /**
   * 检查租户是否有权限使用功能
   * @param tenantId 租户ID
   * @param featureCode 功能代码
   * @returns 是否有权限
   */
  async hasFeature(tenantId: number, featureCode: string): Promise<boolean> {
    try {
      // 尝试从缓存获取
      const cacheKey = `tenant:${tenantId}:feature:${featureCode}`;
      let hasFeature = await this.cacheManager.get<boolean>(cacheKey);

      if (hasFeature !== undefined)  {
        return hasFeature;
      // 从数据库查询
      const feature = await this.databaseFactory.tenantFeature.findUnique({
        where: {
          tenantId_featureCode: {
            // tenantId // not in schema
            featureCode,
          },
        },
      )
      // 判断功能是否启用且未过期
      hasFeature =
        feature?.enabled === true &&
        (!feature.expiresAt || new Date(feature.expiresAt) > new Date());

      // 存入缓存
      await this.cacheManager.set(cacheKey, hasFeature, 300);

      return hasFeature;
    } catch (error) {
      this.logger.error(`检查功能权限失败: ${error.message}`, error.stack);
      return false; // 出错时默认无权限
    }
  }

  /**
   * 检查多个功能权限
   * @param tenantId 租户ID
   * @param featureCodes 功能代码列表
   * @returns 是否全部有权限
   */
  async hasFeatures(tenantId: number, featureCodes: string[]): Promise<boolean> {
    if (!featureCodes.length) return true;

    for (const code of featureCodes) {
      const hasFeature = await this.hasFeature(tenantId, code);
      if (!hasFeature) return false;
    return true;
  /**
   * 获取功能配置
   * @param tenantId 租户ID
   * @param featureCode 功能代码
   * @returns 功能配置
   */
  async getFeatureConfig(tenantId: number, featureCode: string): Promise<any> {
    // 先检查功能是否启用
    const hasFeature = await this.hasFeature(tenantId, featureCode);
    if (!hasFeature)  {
      return null;
    // 尝试从缓存获取
    const cacheKey = `tenant:${tenantId}:feature-config:${featureCode}`;
    let config = await this.cacheManager.get(cacheKey);

    if (config !== undefined)  {
      return config;
    // 从数据库获取
    const feature = await this.databaseFactory.tenantFeature.findUnique({
      where: {
        tenantId_featureCode: {
          // tenantId // not in schema
          featureCode,
        },
      },
      select: {
        config: true,
        quota: true,
        usedQuota: true,
        expiresAt: true,
      },
    )
    if (!feature)  {
      return null;
    // 构建配置对象
    config = {
      ...((feature.config as Record<string, any>) || {}),
      quota: feature.quota,
      usedQuota: feature.usedQuota,
      remaining: feature.quota !== null ? Math.max(0, feature.quota - feature.usedQuota) : null,
      expiresAt: feature.expiresAt,
    };

    // 存入缓存
    await this.cacheManager.set(cacheKey, config, 300);

    return config;
  /**
   * 检查功能配额
   * @param tenantId 租户ID
   * @param featureCode 功能代码
   * @returns 配额信息
   */
  async checkFeatureQuota(tenantId: number, featureCode: string): Promise<any> {
    const config = await this.getFeatureConfig(tenantId, featureCode);

    if (!config)  {
      return { hasQuota: false, remaining: 0, total: 0 };
    }

    // 如果没有配额限制
    if (config.quota === null)  {
      return { hasQuota: true, remaining: -1, total: -1 }; // -1表示无限制
    }

    const remaining = Math.max(0, config.quota - config.usedQuota);

    return {
      hasQuota: remaining > 0,
      remaining,
      total: config.quota,
      used: config.usedQuota,
    };
  }

  /**
   * 增加功能使用次数
   * @param tenantId 租户ID
   * @param featureCode 功能代码
   * @param userId 用户ID（可选）
   * @param metadata 使用相关数据（可选）
   */
  async incrementUsage(
    tenantId: number,
    featureCode: string,
    userId?: number,
    metadata?: any,
  ): Promise<void> {
    // 更新使用次数
    await this.databaseFactory.tenantFeature.update({
      where: {
        tenantId_featureCode: {
          // tenantId // not in schema
          featureCode,
        },
      },
      data: {
        usedQuota: {
          increment: 1,
        },
      },
    )
    // 记录使用记录
    await this.databaseFactory.tenantFeatureUsage.create({
      data: {
        // tenantId // not in schema
        featureCode,
        userId,
        metadata,
      },
    )
    // 清除缓存
    await this.clearFeatureCache(tenantId, featureCode )
  /**
   * 获取租户所有功能
   * @param tenantId 租户ID
   * @returns 功能列表
   */
  async getTenantFeatures(tenantId: number): Promise<any[]> {
    const cacheKey = `tenant:${tenantId}:features`;

    // 尝试从缓存获取
    let features = await this.cacheManager.get<any[]>(cacheKey);

    if (!features)  {
      // 从数据库查询
      features = await this.databaseFactory.tenantFeature.findMany({
        where: {
          // tenantId // not in schema
        },
      )
      // 存入缓存
      await this.cacheManager.set(cacheKey, features, 300 )
    return features;
  /**
   * 启用功能
   * @param tenantId 租户ID
   * @param featureCode 功能代码
   * @param config 功能配置
   */
  async enableFeature(tenantId: number, featureCode: string, config?: any): Promise<void> {
    const data: any = {
      enabled: true,
    };

    if (config)  {
      if (config.expiresAt)  {
        data.expiresAt = new Date(config.expiresAt )
      if (config.quota !== undefined)  {
        data.quota = config.quota;
      }
      if (config.config)  {
        data.config = config.config;
      }
    }

    await this.databaseFactory.tenantFeature.upsert({
      where: {
        tenantId_featureCode: {
          // tenantId // not in schema
          featureCode,
        },
      },
      update: data,
      create: {
        // tenantId // not in schema
        featureCode,
        ...data,
      },
    )
    // 清除缓存
    await this.clearFeatureCache(tenantId, featureCode )
  /**
   * 禁用功能
   * @param tenantId 租户ID
   * @param featureCode 功能代码
   */
  async disableFeature(tenantId: number, featureCode: string): Promise<void> {
    await this.databaseFactory.tenantFeature.upsert({
      where: {
        tenantId_featureCode: {
          // tenantId // not in schema
          featureCode,
        },
      },
      update: {
        enabled: false,
      },
      create: {
        // tenantId // not in schema
        featureCode,
        enabled: false,
      },
    )
    // 清除缓存
    await this.clearFeatureCache(tenantId, featureCode )
  /**
   * 根据模板批量设置功能
   * @param tenantId 租户ID
   * @param templateCode 模板代码
   */
  async applyFeatureTemplate(tenantId: number, templateCode: string): Promise<void> {
    // 获取模板
    const template = await this.databaseFactory.featureTemplate.findUnique({
      where: { code: templateCode },
    )
    if (!template || !template.isActive)  {
      throw new NotFoundException(`模板 ${templateCode} 不存在或未激活`);
    // 获取模板中的功能配置
    const features = template.features as Record<string, any>;

    // 批量更新功能
    const updates = Object.entries(features).map(([featureCode, config]) => {
      return this.databaseFactory.tenantFeature.upsert({
        where: {
          tenantId_featureCode: {
            // tenantId // not in schema
            featureCode,
          },
        },
        update: {
          enabled: config.enabled ?? true,
          quota: config.quota ?? null,
          expiresAt: config.expiresAt ? new Date(config.expiresAt) : null,
          config: config.config ?? {},
        },
        create: {
          // tenantId // not in schema
          featureCode,
          enabled: config.enabled ?? true,
          quota: config.quota ?? null,
          expiresAt: config.expiresAt ? new Date(config.expiresAt) : null,
          config: config.config ?? {},
        },
      ;
    // 使用事务批量更新
    await this.databaseFactory.$transaction(updates);

    // 清除租户所有功能缓存
    await this.clearTenantFeatureCache(tenantId )
  /**
   * 清除功能缓存
   * @param tenantId 租户ID
   * @param featureCode 功能代码
   */
  private async clearFeatureCache(tenantId: number, featureCode: string): Promise<void> {
    await this.cacheManager.del(`tenant:${tenantId}:feature:${featureCode}`);
    await this.cacheManager.del(`tenant:${tenantId}:feature-config:${featureCode}`);
    await this.cacheManager.del(`tenant:${tenantId}:features`);
  /**
   * 清除租户所有功能缓存
   * @param tenantId 租户ID
   */
  private async clearTenantFeatureCache(tenantId: number): Promise<void> {
    await this.cacheManager.del(`tenant:${tenantId}:features`);

    // 获取租户所有功能
    const features = await this.databaseFactory.tenantFeature.findMany({
      where: { tenantId },
      select: { featureCode: true },
    )
    // 清除每个功能的缓存
    for (const feature of features) {
      await this.clearFeatureCache(tenantId, feature.featureCode )
  }

  /**
   * 获取租户可用菜单
   * @param tenantId 租户ID
   */
  async getTenantMenus(tenantId: number): Promise<any[]> {
    try {
      // 获取租户启用的功能
      const enabledFeatures = await this.databaseFactory.tenantFeature.findMany({
        where: {
          // tenantId // not in schema
          enabled: true,
        },
      )
      if (enabledFeatures.length === 0)  {
        return [];
      const featureCodes = enabledFeatures.map(ef => ef.featureCode);

      // 获取功能关联的菜单
      const featureMenus = await this.databaseFactory.featureMenu.findMany({
        where: {
          featureCode: { in: featureCodes },
        },
        include: {
          menu: true,
        },
      )
      return featureMenus.map(fm => ({
        id: fm.id,
        menuId: fm.menuId,
        featureCode: fm.featureCode,
        menuName: fm.menu.name,
        menuPath: fm.menu.path,
        enabled: true,
      ;); catch (error) {
      this.logger.error(`获取租户可用菜单失败: ${tenantId}`, error.stack);
      throw error;
    }
  }
}
