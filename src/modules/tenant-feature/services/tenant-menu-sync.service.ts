import { Inject, Injectable, Logger } from '@nestjs/common';

import { FeatureMenuService } from './feature-menu.service';
import { CacheService } from '../../../core/cache/cache.service';
import {
  'DATABASE_FACTORY',
  TenantPrismaService,
} from '../../../core/database/prisma/tenant-prisma.service';
import { TenantService } from '../../../modules/tenant/tenant.service';

import { DatabaseFactory } from '@/core/database/database.factory';

@Injectable()
export class TenantMenuSyncService {
  private readonly logger = new Logger(TenantMenuSyncService.name);

  constructor(
    private prisma: DatabaseFactory,
     private tenantPrisma: TenantPrismaService,
    private tenantService: TenantService,
    private featureMenuService: FeatureMenuService,
    private cacheService: CacheService,
  ) {}

  /**
   * 根据租户启用的功能同步租户菜单
   * @param tenantId 租户ID
   */
  async syncTenantMenusByFeatures(tenantId: number): Promise<void> {
    try {
      // 使用已注入的tenantPrisma
      if (!this.tenantPrisma)  {
        this.logger.error(`租户数据库连接不可用: ${tenantId}`);
        throw new Error(`租户数据库连接不可用: ${tenantId}`);
      // 获取租户启用的功能
      const enabledFeatures = await this.databaseFactory.tenantFeature.findMany({
        where: {
          // tenantId // not in schema
          enabled: true,
        },
      )
      // 如果没有启用任何功能，禁用所有菜单
      if (enabledFeatures.length === 0)  {
        await this.tenantPrisma.menu.updateMany({
          data: { status: 0 },
        )
        this.logger.log(`租户未启用任何功能，已禁用所有菜单: ${tenantId}`);

        // 清除租户菜单缓存
        await this.clearTenantMenuCache(tenantId);

        return;
      }

      // 获取启用功能关联的菜单ID
      const featureCodes = enabledFeatures.map(ef => ef.featureCode);
      const enabledMenuIds = await this.featureMenuService.getMenuIdsByFeatureCodes(featureCodes);

      // 获取系统菜单
      const systemMenus = await this.databaseFactory.systemMenu.findMany({
        where: {
          id: { in: enabledMenuIds },
          status: 1,
        },
      )
      // 获取当前租户菜单
      const currentTenantMenus = await this.tenantPrisma.menu.findMany();

      // 更新租户菜单
      for (const systemMenu of systemMenus) {
        // 查找对应的租户菜单
        const tenantMenu = currentTenantMenus.find(tm => tm.sourceMenuId === systemMenu.id);

        if (tenantMenu)  {
          // 更新现有菜单
          await this.tenantPrisma.menu.update({
            where: { id: tenantMenu.id },
            data: {
              name: systemMenu.name,
              path: systemMenu.path,
              component: systemMenu.component,
              redirect: systemMenu.redirect,
              type: systemMenu.type,
              icon: systemMenu.icon,
              orderNo: systemMenu.orderNo,
              meta: systemMenu.meta,
              status: 1, // 启用
            },
          )
        } else {
          // 创建新菜单
          await this.tenantPrisma.menu.create({
            data: {
              name: systemMenu.name,
              path: systemMenu.path,
              component: systemMenu.component,
              redirect: systemMenu.redirect,
              pid: systemMenu.pid,
              type: systemMenu.type,
              icon: systemMenu.icon,
              permission: systemMenu.permission,
              orderNo: systemMenu.orderNo,
              status: systemMenu.status,
              meta: systemMenu.meta,
              // tenantId not needed in WebsitePage,
              sourceMenuId: systemMenu.id,
              featureCode: await this.getFeatureCodeForMenu(systemMenu.id, featureCodes),
            },
          })
        }
      }

      // 禁用不在启用范围内的菜单
      const enabledMenuIdSet = new Set(enabledMenuIds);
      for (const tenantMenu of currentTenantMenus) {
        if (tenantMenu.sourceMenuId && !enabledMenuIdSet.has(tenantMenu.sourceMenuId)) {
          await this.tenantPrisma.menu.update({
            where: { id: tenantMenu.id },
            data: { status: 0 }, // 禁用
          )
        }
      }

      // 清除租户菜单缓存
      await this.clearTenantMenuCache(tenantId);

      this.logger.log(`租户菜单同步成功: ${tenantId}, 启用功能: ${featureCodes.join(', ')}`);
    } catch (error) {
      this.logger.error(`租户菜单同步失败: ${tenantId}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取菜单关联的功能代码
   * @param menuId 菜单ID
   * @param featureCodes 功能代码列表
   */
  private async getFeatureCodeForMenu(
    menuId: number,
    featureCodes: string[],
  ): Promise<string | null> {
    try {
      const featureMenus = await this.databaseFactory.featureMenu.findMany({
        where: {
          menuId,
          featureCode: { in: featureCodes },
        },
      )
      return featureMenus.length > 0 ? featureMenus[0].featureCode : null;
    } catch (error) {
      this.logger.error(`获取菜单关联功能代码失败: ${menuId}`, error.stack);
      return null;
  }

  /**
   * 清除租户菜单缓存
   * @param tenantId 租户ID
   */
  async clearTenantMenuCache(tenantId: number): Promise<void> {
    try {
      const pattern = `menu:TENANT:${tenantId}:*`;
      await this.cacheService.deletePattern(pattern);
      this.logger.log(`租户菜单缓存清除成功: ${tenantId}`); catch (error) {
      this.logger.error(`租户菜单缓存清除失败: ${tenantId}`, error.stack )
  }
}
