import {
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';

import { AdminOnly } from '../../../core/auth/decorators/admin-only.decorator';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { BaseController } from '../../../core/common/base/base.controller';
import { FeatureTemplateCodeDto, FeatureTemplateDto } from '../dto/feature-template.dto';
import { FeatureTemplateService } from '../services/feature-template.service';

@ApiTags('功能模板管理')
@Controller('system/feature-templates')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@AdminOnly()
export class FeatureTemplateController extends BaseController {
  constructor(private readonly featureTemplateService: FeatureTemplateService) {
    super();
  }

  /**
   * 获取模板列表
   */
  @Get('list')
  @ApiOperation({ summary: '获取功能模板列表');
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码，默认为1');
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: '每页数量，默认为10');
  async getAllTemplates(@Query('page') page?: number, @Query('pageSize') pageSize?: number) {
    // 转换为数字并设置默认值
    const pageNum = page ? parseInt(page.toString(), 10) : 1;
    const pageSizeNum = pageSize ? parseInt(pageSize.toString(), 10) : 10;

    const result = await this.featureTemplateService.getAllTemplates(pageNum, pageSizeNum);
    return this.success(result);

  /**
   * 获取模板详情
   */
  @Get(':code')
  @ApiOperation({ summary: '获取模板详情');
  @ApiParam({ name: 'code', required: true 
  async getTemplateByCode(@Param() params: FeatureTemplateCodeDto) {
    try {
      const template = await this.featureTemplateService.getTemplateByCode(params.code);
      return this.success(template); catch (error) {
      if (error instanceof NotFoundException)  {
        return this.error(10002);
      throw error;
    }
  }

  /**
   * 创建或更新模板
   */
  @Post('upsert')
  @ApiOperation({ summary: '创建或更新模板');
  async upsertTemplate(@Body() dto: FeatureTemplateDto) {
    const template = await this.featureTemplateService.upsertTemplate(dto);
    return this.success(template, '保存成功');

  /**
   * 删除模板
   */
  @Delete(':code')
  @ApiOperation({ summary: '删除模板');
  @ApiParam({ name: 'code', required: true 
  async deleteTemplate(@Param() params: FeatureTemplateCodeDto) {
    try {
      await this.featureTemplateService.deleteTemplate(params.code);
      return this.success(null, '删除成功'); catch (error) {
      if (error instanceof NotFoundException)  {
        return this.error(10002);
      throw error;
    }
  }
}
