import { Body, Controller, Get, Param, ParseIntPipe, Post, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';

import { AdminOnly } from '../../../core/auth/decorators/admin-only.decorator';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { BaseController } from '../../../core/common/base/base.controller';
import { FeatureMenusDto, SetFeatureMenusDto } from '../dto/feature-menu.dto';
import { FeatureMenuService } from '../services/feature-menu.service';

@ApiTags('功能菜单管理')
@Controller('system/feature-codes')
@UseGuards(JwtAuthGuard)
@AdminOnly()
export class FeatureMenuController extends BaseController {
  constructor(private featureMenuService: FeatureMenuService) {
    super();
  }

  @Get(':code/menus')
  @ApiOperation({ summary: '获取功能代码关联的菜单');
  @ApiParam({ name: 'code', description: '功能代码');
  @ApiResponse({ status: 200, description: '获取成功');
  async getFeatureMenus(@Param('code') code: string) {
    const data = await this.featureMenuService.getFeatureMenus(code);
    return this.success(data);

  @Post(':code/menus')
  @ApiOperation({ summary: '设置功能代码关联的菜单');
  @ApiParam({ name: 'code', description: '功能代码');
  @ApiResponse({ status: 200, description: '设置成功');
  async setFeatureMenus(@Param('code') code: string, @Body() dto: SetFeatureMenusDto) {
    await this.featureMenuService.setFeatureMenus(code, dto.menuIds);
    return this.success(true, '功能菜单关联设置成功');
@ApiTags('功能模板菜单管理')
@Controller('system/feature-templates')
@UseGuards(JwtAuthGuard)
@AdminOnly()
export class FeatureTemplateMenuController extends BaseController {
  constructor(private featureMenuService: FeatureMenuService) {
    super();
  }

  @Get(':code/menus')
  @ApiOperation({ summary: '获取功能模板关联的菜单');
  @ApiParam({ name: 'code', description: '功能模板代码');
  @ApiResponse({ status: 200, description: '获取成功');
  async getFeatureTemplateMenus(@Param('code') code: string) {
    const data = await this.featureMenuService.getFeatureTemplateMenus(code);
    return this.success(data);