import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ModuleRef } from '@nestjs/core';

import { MenuStrategyType } from './strategies/menu-strategy.factory';
import { SystemMenuStrategy } from './strategies/system-menu.strategy';
import { TenantMenuStrategy } from './strategies/tenant-menu.strategy';

import { DatabaseFactory } from '@/core/database/database.factory';
/**
 * 菜单策略代理
 * 用于解决策略模式中的作用域问题和租户数据库连接问题
 */
@Injectable()
export class MenuStrategyProxy {
  private readonly logger = new Logger(MenuStrategyProxy.name);

  constructor(
    private readonly moduleRef: ModuleRef,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 执行策略方法
   * @param userType 用户类型
   * @param methodName 方法名
   * @param args 方法参数
   * @param tenantId 租户ID（可选）
   * @returns 方法执行结果
   */
  async execute<T>(
    userType: string,
    methodName: string,
    args: any[],
    tenantId?: string,
  ): Promise<T> {
    try {
      console.log(
        `菜单策略代理 - 执行策略方法: ${methodName}, 用户类型: ${userType}, 租户ID: ${tenantId || '无'}, 参数:`,
        args,
      );
      this.logger.log(
        `执行策略方法: ${methodName}, 用户类型: ${userType}, 租户ID: ${tenantId || '无'}, 参数: ${JSON.stringify(args)}`,
      );

      // 获取正确的策略类型
      const strategyType =
        userType === 'SYSTEM' ? MenuStrategyType.SYSTEM : MenuStrategyType.TENANT;
      console.log(`菜单策略代理 - 策略类型: ${strategyType}`);
      this.logger.log(`策略类型: ${strategyType}`);

      // 根据策略类型获取策略类
      const StrategyClass =
        strategyType === MenuStrategyType.SYSTEM ? SystemMenuStrategy : TenantMenuStrategy;
      console.log(`菜单策略代理 - 策略类: ${StrategyClass.name}`);
      this.logger.log(`策略类: ${StrategyClass.name}`);

      try {
        // 创建策略实例
        console.log(`菜单策略代理 - 开始创建策略实例: ${StrategyClass.name}`);
        this.logger.log(`开始创建策略实例: ${StrategyClass.name}`);
        const strategy = await this.createStrategy(StrategyClass, tenantId);
        console.log(
          `菜单策略代理 - 成功创建策略实例: ${strategy ? strategy.constructor.name : '未知'}`,
        );
        this.logger.log(`成功创建策略实例: ${strategy ? strategy.constructor.name : '未知'}`);

        // 确保策略实例有指定的方法
        if (typeof strategy[methodName] !== 'function')  {
          console.warn(
            `菜单策略代理 - 策略 ${strategy.getType ? strategy.getType() : '未知'} 没有方法: ${methodName}，尝试使用系统策略`,
          );
          this.logger.warn(
            `策略 ${strategy.getType ? strategy.getType() : '未知'} 没有方法: ${methodName}，尝试使用系统策略`,
          );

          // 如果策略没有指定的方法，尝试使用系统策略
          console.log(`菜单策略代理 - 尝试解析系统策略`);
          this.logger.log(`尝试解析系统策略`);
          const systemStrategy = await this.moduleRef.resolve(SystemMenuStrategy);
          console.log(`菜单策略代理 - 成功解析系统策略`);
          this.logger.log(`成功解析系统策略`);

          if (typeof systemStrategy[methodName] !== 'function')  {
            console.error(`菜单策略代理 - 系统策略也没有方法: ${methodName}`);
            this.logger.error(`系统策略也没有方法: ${methodName}`);
            throw new Error(`Both strategies do not have method: ${methodName}`);
          }

          // 执行系统策略方法
          console.log(`菜单策略代理 - 使用系统策略执行方法: ${methodName}`);
          this.logger.log(`使用系统策略执行方法: ${methodName}`);
          const result = await systemStrategy[methodName](...args);
          console.log(
            `菜单策略代理 - 系统策略方法执行结果: ${result ? '成功' : '为空'}, 类型: ${typeof result}, 是否数组: ${Array.isArray(result)}`,
          );
          this.logger.log(`系统策略方法执行结果: ${result ? '成功' : '为空'}`);
          return result;
        // 执行策略方法
        console.log(`菜单策略代理 - 使用策略 ${strategy.constructor.name} 执行方法: ${methodName}`);
        this.logger.log(`使用策略 ${strategy.constructor.name} 执行方法: ${methodName}`);
        const result = await strategy[methodName](...args);
        console.log(
          `菜单策略代理 - 策略方法执行结果: ${result ? '成功' : '为空'}, 结果类型: ${Array.isArray(result) ? 'Array' : typeof result}`,
        );
        this.logger.log(
          `策略方法执行结果: ${result ? '成功' : '为空'}, 结果类型: ${Array.isArray(result) ? 'Array' : typeof result}`,
        );
        if (Array.isArray(result)) {
          console.log(`菜单策略代理 - 结果数组长度: ${result.length}`);
          this.logger.log(`结果数组长度: ${result.length}`);
        return result;
      } catch (strategyError) {
        // 如果创建策略实例失败，尝试使用系统策略
        console.warn(`菜单策略代理 - 创建策略实例失败: ${strategyError.message}，尝试使用系统策略`);
        this.logger.warn(`创建策略实例失败: ${strategyError.message}，尝试使用系统策略`);

        console.log(`菜单策略代理 - 尝试解析系统策略（备用）`);
        this.logger.log(`尝试解析系统策略（备用）`);
        const systemStrategy = await this.moduleRef.resolve(SystemMenuStrategy);
        console.log(`菜单策略代理 - 成功解析系统策略（备用）`);
        this.logger.log(`成功解析系统策略（备用）`);

        if (typeof systemStrategy[methodName] !== 'function')  {
          console.error(`菜单策略代理 - 系统策略没有方法: ${methodName}`);
          this.logger.error(`系统策略没有方法: ${methodName}`);
          throw new Error(`System strategy does not have method: ${methodName}`);
        }

        // 执行系统策略方法
        console.log(`菜单策略代理 - 使用系统策略（备用）执行方法: ${methodName}`);
        this.logger.log(`使用系统策略（备用）执行方法: ${methodName}`);
        const result = await systemStrategy[methodName](...args);
        console.log(
          `菜单策略代理 - 系统策略（备用）方法执行结果: ${result ? '成功' : '为空'}, 类型: ${typeof result}, 是否数组: ${Array.isArray(result)}`,
        );
        this.logger.log(`系统策略（备用）方法执行结果: ${result ? '成功' : '为空'}`);
        return result;
    } catch (error) {
      console.error(`菜单策略代理 - 执行策略方法失败: ${error.message}`, error);
      this.logger.error(`执行策略方法失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 创建策略实例
   * @param StrategyClass 策略类
   * @param tenantId 租户ID（可选）
   * @returns 策略实例
   */
  private async createStrategy(StrategyClass: any, tenantId?: string): Promise<any> {
    try {
      console.log(
        `菜单策略代理 - 创建策略实例: ${StrategyClass.name}, 租户ID: ${tenantId || '无'}`,
      );

      // 如果是系统策略，直接从模块引用中解析
      if (StrategyClass === SystemMenuStrategy)  {
        console.log(`菜单策略代理 - 创建系统策略实例`);
        const strategy = await this.moduleRef.resolve(SystemMenuStrategy);
        console.log(`菜单策略代理 - 成功创建系统策略实例`);
        return strategy;
      // 如果是租户策略，需要特殊处理
      if (StrategyClass === TenantMenuStrategy)  {
        console.log(`菜单策略代理 - 创建租户策略实例, 租户ID: ${tenantId || '无'}`);

        // 如果没有提供租户ID，可能是系统用户访问租户资源，应该使用系统策略
        if (!tenantId)  {
          console.log(`菜单策略代理 - 未提供租户ID，但请求的是租户策略，切换到系统策略`);
          this.logger.log('未提供租户ID，但请求的是租户策略，切换到系统策略');
          return await this.moduleRef.resolve(SystemMenuStrategy);

        try {
          // 获取租户数据库连接
          console.log(`菜单策略代理 - 尝试获取租户数据库连接, 租户ID: ${tenantId}`);
          const tenantPrisma = await this.getTenantPrisma(tenantId);
          console.log(`菜单策略代理 - 租户数据库连接获取结果: ${tenantPrisma ? '成功' : '失败'}`);

          // 如果租户数据库连接不可用，但没有抛出异常（因为我们在getTenantPrisma中处理了），使用系统策略
          if (!tenantPrisma)  {
            console.log(`菜单策略代理 - 租户数据库连接不可用，切换到系统策略`);
            this.logger.log('租户数据库连接不可用，切换到系统策略');
            return await this.moduleRef.resolve(SystemMenuStrategy);

          // 创建租户策略实例，并手动注入依赖
          console.log(`菜单策略代理 - 创建租户策略实例，并注入租户数据库连接`);
          const strategy = new TenantMenuStrategy(tenantPrisma);
          console.log(`菜单策略代理 - 成功创建租户策略实例`);

          return strategy;
        } catch (error) {
          // 如果获取租户数据库连接失败，使用系统策略
          console.warn(`菜单策略代理 - 获取租户数据库连接失败，切换到系统策略: ${error.message}`);
          this.logger.warn(`获取租户数据库连接失败，切换到系统策略: ${error.message}`);
          return await this.moduleRef.resolve(SystemMenuStrategy);
      console.error(`菜单策略代理 - 未知的策略类: ${StrategyClass.name}`);
      throw new Error(`Unknown strategy class: ${StrategyClass.name}`);
    } catch (error) {
      console.error(`菜单策略代理 - 创建策略实例失败: ${error.message}`, error);
      this.logger.error(`创建策略实例失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取租户数据库连接
   * @param tenantId 租户ID（可选）
   * @returns 租户数据库连接
   */
  private async getTenantPrisma(tenantId?: string): Promise<any> {
    try {
      console.log(`菜单策略代理 - 获取租户数据库连接, 租户ID: ${tenantId || '无'}`);

      // 尝试从请求中获取租户数据库连接
      console.log(`菜单策略代理 - 尝试解析 TENANT_PRISMA_SERVICE`);
      const prisma = await this.moduleRef.resolve(TENANT_PRISMA_SERVICE, undefined, {
        strict: false,
      );
      console.log(`菜单策略代理 - TENANT_PRISMA_SERVICE 解析结果: ${prisma ? '成功' : '失败'}`);

      if (!prisma)  {
        console.error(
          `菜单策略代理 - 无法解析 TENANT_PRISMA_SERVICE，可能当前请求上下文中没有租户信息`,
        );
        this.logger.error('无法解析 TENANT_PRISMA_SERVICE，可能当前请求上下文中没有租户信息');

        // 如果是系统用户访问，可以返回null，由调用者处理
        if (!tenantId)  {
          console.log(`菜单策略代理 - 未提供租户ID，可能是系统用户访问，返回null`);
          this.logger.log('未提供租户ID，可能是系统用户访问，返回null');
          return null;
        throw new Error('租户数据库连接不可用');
      }

      console.log(`菜单策略代理 - 成功获取租户数据库连接`);
      return prisma;
    } catch (error) {
      console.error(`菜单策略代理 - 获取租户数据库连接失败: ${error.message}`, error);
      this.logger.error(`获取租户数据库连接失败: ${error.message}`);

      // 如果是系统用户访问，可以返回null，由调用者处理
      if (!tenantId)  {
        console.log(`菜单策略代理 - 未提供租户ID，可能是系统用户访问，返回null`);
        this.logger.log('未提供租户ID，可能是系统用户访问，返回null');
        return null;
      throw new Error('租户数据库连接不可用');
    }
  }
}
