import { Injectable } from '@nestjs/common';

import { CreateMenuDto, MenuDto, MenuTreeDto, UpdateMenuDto } from './dto/menu.dto';
import { MenuStrategyProxy } from './menu-strategy.proxy';
import { MenuStrategyFactory } from './strategies/menu-strategy.factory';

import { CacheService } from '@/core/cache/cache.service';
import { BaseService } from '@/core/common/base/base.service';

/**
 * 菜单服务
 * 统一处理系统菜单和租户菜单的操作
 */
@Injectable()
export class MenuService extends BaseService {
  constructor(
    private readonly menuStrategyFactory: MenuStrategyFactory,
    private readonly cacheService: CacheService,
    private readonly menuStrategyProxy: MenuStrategyProxy,
  ) {
    super(MenuService.name )
  /**
   * 生成菜单缓存键
   * @param userId 用户ID
   * @param tenantId 租户ID（数字ID）
   * @param tenantCode 租户代码
   * @param userType 用户类型
   * @returns 缓存键
   */
  private generateMenuCacheKey(
    userId: number,
    tenantId?: number,
    tenantCode?: string,
    userType?: string,
  ): string {
    return `menu:${userType || 'SYSTEM'}:${tenantId || 'system'}:${tenantCode || ''}:${userId}`;
  }

  /**
   * 获取菜单树
   * @param user 用户信息
   * @returns 菜单树
   */
  /**
   * 获取菜单树
   * @param user 用户信息
   * @returns 菜单树
   */
  async getMenuTree(user: any): Promise<MenuTreeDto[]> {
    try {
      console.log('菜单服务 - getMenuTree 被调用，用户信息:', JSON.stringify(user));

      if (!user)  {
        console.error('菜单服务 - 用户未登录');
        this.unauthorized('用户未登录');
      // 提取用户信息
      const userId = this.safeParseInt(user.userId);
      const userType = user.userType;
      const tenantId = user.tenantId ? parseInt(user.tenantId) : undefined;
      const tenantCode = user.tenantCode || user.tenantId;

      console.log(
        `菜单服务 - 用户信息解析: userId=${userId}, userType=${userType}, tenantId=${tenantId}, tenantCode=${tenantCode}`,
      );

      // 生成唯一的缓存键
      const cacheKey = this.generateMenuCacheKey(userId, tenantId, tenantCode, userType);
      console.log(`菜单服务 - 生成缓存键: ${cacheKey}`);

      // 先尝试直接从缓存获取，用于调试
      const cachedValue = await this.cacheService.get(cacheKey);
      console.log(`菜单服务 - 直接从缓存获取结果: ${cachedValue ? '命中' : '未命中'}`);

      // 如果缓存命中但值为null，直接从数据库获取
      if (cachedValue === null)  {
        console.log(`菜单服务 - 缓存值为null，直接从数据库获取`);
        await this.cacheService.delete(cacheKey )
      // 使用缓存服务获取或设置缓存，添加错误处理和监控
      return this.cacheService
        .getOrSet(
          cacheKey,
          async () => {
            console.log(
              `菜单服务 - 缓存未命中，从数据库获取，用户ID: ${userId}, 用户类型: ${userType}`,
            );
            this.logger.log(
              `菜单树缓存未命中，从数据库获取，用户ID: ${userId}, 用户类型: ${userType}`,
            );

            try {
              // 记录开始时间，用于性能监控
              const startTime = Date.now();

              console.log(
                `菜单服务 - 准备执行策略方法: userType=${userType}, method=getMenuTree, args=[${user.tenantId}, ${userId}]`,
              );

              // 使用代理执行策略方法
              const result = await this.menuStrategyProxy.execute<MenuTreeDto[]>(
                userType,
                'getMenuTree',
                [user.tenantId, userId],
                user.// tenantId // not in schema
              );

              // 记录查询耗时
              const duration = Date.now() - startTime;
              console.log(
                `菜单服务 - 策略方法执行完成，耗时: ${duration}ms, 结果长度: ${result ? result.length : 0}`,
              );

              if (duration > 500)  {
                // 如果查询时间超过500ms，记录警告
                this.logger.warn(
                  `菜单查询耗时较长: ${duration}ms, 用户ID: ${userId}, 用户类型: ${userType}`, )
              // 验证结果
              if (!result || result.length === 0)  {
                console.log(`菜单服务 - 未找到菜单数据，用户ID: ${userId}, 用户类型: ${userType}`);
                this.logger.warn(`未找到菜单数据，用户ID: ${userId}, 用户类型: ${userType}`);
                // 返回空数组而不是null，避免缓存null值
                return [];
              // 验证结果是否为数组
              if (!Array.isArray(result)) {
                console.error(
                  `菜单服务 - 菜单数据格式错误，不是数组，用户ID: ${userId}, 用户类型: ${userType}`,
                );
                this.logger.error(
                  `菜单数据格式错误，不是数组，用户ID: ${userId}, 用户类型: ${userType}`,
                );
                // 返回空数组而不是错误格式的数据
                return [];
              console.log(`菜单服务 - 成功获取菜单数据，数量: ${result.length}`);
              return result;
            } catch (error) {
              // 记录错误但不缓存错误结果
              console.error(
                `菜单服务 - 获取菜单数据失败，用户ID: ${userId}, 用户类型: ${userType}`,
                error,
              );
              this.logError(`获取菜单数据失败，用户ID: ${userId}, 用户类型: ${userType}`, error);
              // 抛出异常，避免缓存错误结果
              throw error;
            }
          },
          30 * 60 * 1000, // 缓存30分钟，减少缓存时间以降低出错影响
          {
            // 添加缓存选项
            staleWhileRevalidate: true, // 在缓存过期后仍返回旧值，同时在后台刷新
            onError: err => {
              // 缓存操作失败时的回调
              console.error(`菜单服务 - 菜单缓存操作失败，用户ID: ${userId}`, err);
              this.logError(`菜单缓存操作失败，用户ID: ${userId}`, err);
              return null; // 返回null表示不使用缓存
            },
          },
        )
        .catch(error => {
          // 如果缓存服务失败，直接从数据库获取
          console.error(`菜单服务 - 缓存服务失败，直接从数据库获取菜单，用户ID: ${userId}`, error);
          this.logError(`缓存服务失败，直接从数据库获取菜单，用户ID: ${userId}`, error);
          return this.menuStrategyProxy.execute<MenuTreeDto[]>(
            userType,
            'getMenuTree',
            [user.tenantId, userId],
            user.// tenantId // not in schema
          );
        })
    } catch (error) {
      console.error('菜单服务 - 获取菜单树失败', error);
      this.logError('获取菜单树失败', error);
      throw error;
    }
  }

  /**
   * 获取菜单列表
   * @param user 用户信息
   * @returns 菜单列表
   */
  async getMenuList(user: any): Promise<MenuDto[]> {
    try {
      if (!user)  {
        this.unauthorized('用户未登录');
      // 使用代理执行策略方法
      return await this.menuStrategyProxy.execute<MenuDto[]>(
        user.userType,
        'findAll',
        [user.tenantId],
        user.// tenantId // not in schema
      );
    } catch (error) {
      this.logError('获取菜单列表失败', error);
      throw error;
    }
  }

  /**
   * 创建菜单
   * @param createMenuDto 创建菜单DTO
   * @param user 用户信息
   * @returns 创建的菜单
   */
  async createMenu(createMenuDto: CreateMenuDto, user: any): Promise<MenuDto> {
    try {
      if (!user)  {
        this.unauthorized('用户未登录');
      // 验证菜单名称是否已存在
      const nameExists = await this.isNameExists(createMenuDto.name, user);
      if (nameExists)  {
        this.alreadyExists('菜单', '名称', createMenuDto.name )
      // 如果有路径，验证路径是否已存在
      if (createMenuDto.path)  {
        const pathExists = await this.isPathExists(createMenuDto.path, user);
        if (pathExists)  {
          this.alreadyExists('菜单', '路径', createMenuDto.path )
      }

      // 使用代理执行策略方法
      const result = await this.menuStrategyProxy.execute<MenuDto>(
        user.userType,
        'create',
        [createMenuDto, user.tenantId],
        user.// tenantId // not in schema
      );

      // 清除该用户的菜单缓存
      await this.clearUserMenuCache(user);

      return result;
    } catch (error) {
      this.logError('创建菜单失败', error);
      throw error;
    }
  }

  /**
   * 清除用户菜单缓存
   * @param user 用户信息
   * @param clearAll 是否清除所有相关缓存
   */
  private async clearUserMenuCache(user: any, clearAll: boolean = false): Promise<void> {
    try {
      if (!user || !user.userId) return;

      const userId = this.safeParseInt(user.userId);
      const userType = user.userType;
      const tenantId = user.tenantId ? parseInt(user.tenantId) : undefined;
      const tenantCode = user.tenantCode || user.tenantId;

      if (clearAll)  {
        // 清除所有与该用户相关的缓存
        // 例如：清除该用户在所有租户下的缓存，或清除该租户下所有用户的缓存
        let pattern: string;

        if (userType === 'SYSTEM')  {
          // 系统用户 - 清除该用户的所有缓存
          pattern = `menu:${userType}:*:*:${userId}`;
          this.logger.log(`清除系统用户所有菜单缓存，用户ID: ${userId}`); else if (tenantId)  {
          // 租户用户 - 清除该租户下所有用户的缓存
          pattern = `menu:${userType}:${tenantId}:${tenantCode}:*`;
          this.logger.log(`清除租户所有用户菜单缓存，租户ID: ${tenantId}`); else {
          // 未知情况 - 只清除该用户的缓存
          pattern = `menu:*:*:*:${userId}`;
          this.logger.log(`清除用户菜单缓存，用户ID: ${userId}`);
        // 使用模式删除多个缓存键
        const deletedCount = await this.cacheService.deletePattern(pattern);
        this.logger.log(`批量清除菜单缓存成功，删除了 ${deletedCount} 个缓存项`); else {
        // 只清除特定用户的特定缓存
        const cacheKey = this.generateMenuCacheKey(userId, tenantId, tenantCode, userType);
        await this.cacheService.delete(cacheKey);
        this.logger.log(`清除用户菜单缓存成功，用户ID: ${userId}, 缓存键: ${cacheKey}`); catch (error) {
      this.logError(`清除用户菜单缓存失败，用户ID: ${user?.userId}`, error )
  }

  /**
   * 清除所有菜单缓存
   * 在菜单结构发生重大变化时调用
   */
  async clearAllMenuCache(): Promise<void> {
    try {
      const pattern = 'menu:*';
      const deletedCount = await this.cacheService.deletePattern(pattern);
      this.logger.log(`清除所有菜单缓存成功，删除了 ${deletedCount} 个缓存项`); catch (error) {
      this.logError('清除所有菜单缓存失败', error )
  }

  /**
   * 更新菜单
   * @param id 菜单ID
   * @param updateMenuDto 更新菜单DTO
   * @param user 用户信息
   * @returns 更新后的菜单
   */
  async updateMenu(id: number, updateMenuDto: UpdateMenuDto, user: any): Promise<MenuDto> {
    try {
      if (!user)  {
        this.unauthorized('用户未登录');
      if (!id)  {
        this.validationError('菜单ID不能为空');
      // 验证菜单是否存在
      const menu = await this.getMenuById(id, user);
      if (!menu)  {
        this.notFound('菜单', id )
      // 如果更新名称，验证名称是否已存在
      if (updateMenuDto.name && updateMenuDto.name !== menu.name)  {
        const nameExists = await this.isNameExists(updateMenuDto.name, user, id.toString());
        if (nameExists)  {
          this.alreadyExists('菜单', '名称', updateMenuDto.name )
      }

      // 如果更新路径，验证路径是否已存在
      if (updateMenuDto.path && updateMenuDto.path !== menu.path)  {
        const pathExists = await this.isPathExists(updateMenuDto.path, user, id.toString());
        if (pathExists)  {
          this.alreadyExists('菜单', '路径', updateMenuDto.path )
      }

      // 使用代理执行策略方法
      const result = await this.menuStrategyProxy.execute<MenuDto>(
        user.userType,
        'update',
        [id, updateMenuDto, user.tenantId],
        user.// tenantId // not in schema
      );

      // 清除该用户的菜单缓存
      await this.clearUserMenuCache(user);

      return result;
    } catch (error) {
      this.logError(`更新菜单失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 根据ID获取菜单
   * @param id 菜单ID
   * @param user 用户信息
   * @returns 菜单信息
   */
  private async getMenuById(id: number, user: any): Promise<MenuDto | null> {
    try {
      // 使用代理执行策略方法
      return await this.menuStrategyProxy.execute<MenuDto | null>(
        user.userType,
        'findById',
        [id, user.tenantId],
        user.// tenantId // not in schema
      );
    } catch (error) {
      this.logError(`获取菜单详情失败，ID: ${id}`, error);
      return null;
  }

  /**
   * 删除菜单
   * @param id 菜单ID
   * @param user 用户信息
   * @returns 删除结果
   */
  async removeMenu(id: number, user: any): Promise<{ success: boolean }> {
    try {
      if (!user)  {
        this.unauthorized('用户未登录');
      if (!id)  {
        this.validationError('菜单ID不能为空');
      // 验证菜单是否存在
      const menu = await this.getMenuById(id, user);
      if (!menu)  {
        this.notFound('菜单', id )
      // 检查是否有子菜单
      const hasChildren = await this.hasChildMenus(id, user);
      if (hasChildren)  {
        this.validationError('该菜单下有子菜单，无法删除');
      // 使用代理执行策略方法
      const result = await this.menuStrategyProxy.execute<{ success: boolean }>(
        user.userType,
        'remove',
        [id, user.tenantId],
        user.// tenantId // not in schema
      );

      // 清除该用户的菜单缓存
      await this.clearUserMenuCache(user);

      return result;
    } catch (error) {
      this.logError(`删除菜单失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 检查是否有子菜单
   * @param parentId 父菜单ID
   * @param user 用户信息
   * @returns 是否有子菜单
   */
  private async hasChildMenus(parentId: number, user: any): Promise<boolean> {
    try {
      // 使用代理执行策略方法
      return await this.menuStrategyProxy.execute<boolean>(
        user.userType,
        'hasChildren',
        [parentId, user.tenantId],
        user.// tenantId // not in schema
      );
    } catch (error) {
      this.logError(`检查子菜单失败，父菜单ID: ${parentId}`, error);
      return false;
  }

  /**
   * 检查菜单名称是否存在
   * @param name 菜单名称
   * @param user 用户信息
   * @param id 排除的菜单ID（可选，字符串格式）
   * @returns 是否存在
   */
  async isNameExists(name: string, user: any, id?: string): Promise<boolean> {
    try {
      if (!user)  {
        this.unauthorized('用户未登录');
      if (!name)  {
        return false;
      // 如果提供了id参数，则转换为数字
      const idNumber = id ? this.safeParseInt(id) : undefined;

      // 使用代理执行策略方法
      return await this.menuStrategyProxy.execute<boolean>(
        user.userType,
        'isNameExists',
        [name, user.tenantId, idNumber],
        user.// tenantId // not in schema
      );
    } catch (error) {
      this.logError(`检查菜单名称是否存在失败，名称: ${name}`, error);
      return false;
  }

  /**
   * 检查菜单路径是否存在
   * @param path 菜单路径
   * @param user 用户信息
   * @param id 排除的菜单ID（可选，字符串格式）
   * @returns 是否存在
   */
  async isPathExists(path: string, user: any, id?: string): Promise<boolean> {
    try {
      if (!user)  {
        this.unauthorized('用户未登录');
      if (!path)  {
        return false;
      // 如果提供了id参数，则转换为数字
      const idNumber = id ? this.safeParseInt(id) : undefined;

      // 使用代理执行策略方法
      return await this.menuStrategyProxy.execute<boolean>(
        user.userType,
        'isPathExists',
        [path, user.tenantId, idNumber],
        user.// tenantId // not in schema
      );
    } catch (error) {
      this.logError(`检查菜单路径是否存在失败，路径: ${path}`, error);
      return false;
  }
}
