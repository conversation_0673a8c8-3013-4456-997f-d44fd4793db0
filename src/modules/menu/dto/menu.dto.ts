import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsInt,
  IsBoolean,
  IsEnum,
  IsObject,
  ValidateNested,
  IsNotEmpty,
} from 'class-validator';

/**
 * 菜单类型枚举
 * menu: 菜单
 * button: 按钮
 * directory: 目录
 * catalog: 目录
 * embedded: 嵌入式
 * link: 链接
 */
export enum MenuType {
  MENU = 'menu',
  BUTTON = 'button',
  DIRECTORY = 'directory',
  CATALOG = 'catalog',
  EMBEDDED = 'embedded',
  LINK = 'link',
}

/**
 * 菜单元数据DTO
 */
export class MenuMetaDto {
  @ApiProperty({ description: '菜单标题', example: '系统管理');
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: '菜单图标', example: 'setting', required: false 
  @IsString()
  @IsOptional()
  icon?: string;

  @ApiProperty({ description: '排序号', example: 1, required: false 
  @IsInt()
  @IsOptional()
  orderNo?: number;

  @ApiProperty({ description: '是否隐藏菜单', example: false, required: false 
  @IsBoolean()
  @IsOptional()
  hideMenu?: boolean;

  @ApiProperty({ description: '是否忽略权限', example: false, required: false 
  @IsBoolean()
  @IsOptional()
  ignoreAuth?: boolean;

  @ApiProperty({ description: '是否隐藏面包屑', example: false, required: false 
  @IsBoolean()
  @IsOptional()
  hideBreadcrumb?: boolean;

  @ApiProperty({ description: '是否隐藏子菜单', example: false, required: false 
  @IsBoolean()
  @IsOptional()
  hideChildrenInMenu?: boolean;

  @ApiProperty({ description: '当前激活的菜单', example: '/dashboard', required: false 
  @IsString()
  @IsOptional()
  currentActiveMenu?: string;
}

/**
 * 创建菜单DTO
 */
export class CreateMenuDto {
  @ApiProperty({ description: '菜单名称', example: '系统管理');
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '路由路径', example: '/system');
  @IsString()
  @IsNotEmpty()
  path: string;

  @ApiProperty({ description: '父级ID', example: 0, required: false 
  @IsInt()
  @IsOptional()
  pid?: number;

  @ApiProperty({ description: '重定向路径', example: '/system/user', required: false 
  @IsString()
  @IsOptional()
  redirect?: string;

  @ApiProperty({ description: '菜单类型', enum: MenuType, example: MenuType.MENU 
  @IsEnum(MenuType)
  @IsNotEmpty()
  type: MenuType;

  @ApiProperty({ description: '图标', example: 'setting', required: false 
  @IsString()
  @IsOptional()
  icon?: string;

  @ApiProperty({ description: '组件路径', example: 'LAYOUT', required: false 
  @IsString()
  @IsOptional()
  component?: string;

  @ApiProperty({ description: '权限标识', example: 'system:menu:list', required: false 
  @IsString()
  @IsOptional()
  permission?: string;

  @ApiProperty({ description: '排序号', example: 1 
  @IsInt()
  @IsOptional()
  orderNo: number = 0;

  @ApiProperty({ description: '状态：0-禁用，1-启用', example: 1 
  @IsInt()
  @IsOptional()
  status: number = 1;

  @ApiProperty({ description: '元数据', type: MenuMetaDto, required: false 
  @IsObject()
  @ValidateNested()
  @Type(() => MenuMetaDto)
  @IsOptional()
  meta?: MenuMetaDto;
}

/**
 * 更新菜单DTO
 */
export class UpdateMenuDto extends CreateMenuDto {}

/**
 * 菜单DTO
 */
export class MenuDto extends CreateMenuDto {
  @ApiProperty({ description: '菜单ID', example: 1 
  id: number;

  @ApiProperty({ description: '创建时间');
  createdAt: Date;

  @ApiProperty({ description: '更新时间');
  updatedAt: Date;

  @ApiProperty({ description: '子菜单', type: [MenuDto], required: false 
  children?: MenuDto[];
}

/**
 * 菜单树DTO
 */
export class MenuTreeDto {
  @ApiProperty({ description: '菜单ID', example: 1 
  id: string;

  @ApiProperty({ description: '菜单名称', example: '系统管理');
  name: string;

  @ApiProperty({ description: '路由路径', example: '/system');
  path: string;

  @ApiProperty({ description: '组件路径', example: 'LAYOUT', required: false 
  component?: string;

  @ApiProperty({ description: '重定向路径', example: '/system/user', required: false 
  redirect?: string;

  @ApiProperty({ description: '元数据', type: MenuMetaDto 
  meta: MenuMetaDto;

  @ApiProperty({ description: '子菜单', type: [MenuTreeDto], required: false 
  children?: MenuTreeDto[];
}
