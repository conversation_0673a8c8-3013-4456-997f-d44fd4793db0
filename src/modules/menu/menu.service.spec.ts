import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { MenuType } from './dto/menu.dto';
import { MenuService } from './menu.service';
import { MenuStrategyFactory } from './strategies/menu-strategy.factory';

import { CacheService } from '@/core/cache/cache.service';

describe('MenuService', () => {
  let service: MenuService;
  let cacheService: any;
  let mockStrategy: any;

  beforeEach(async () => {
    // 创建模拟的菜单策略
    mockStrategy = {
      getMenuTree: jest.fn(),
      findAll: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
      findById: jest.fn(),
      hasChildren: jest.fn(),
      isNameExists: jest.fn(),
      isPathExists: jest.fn(),
      getType: jest.fn().mockReturnValue('SYSTEM'),
    };

    // 创建模拟的缓存服务
    const mockCacheService = {
      get: jest.fn(),
      set: jest.fn(),
      delete: jest.fn(),
      getOrSet: jest.fn(),
      reset: jest.fn(),
    };

    // 创建模拟的策略工厂
    const mockMenuStrategyFactory = {
      getStrategyByUserType: jest.fn().mockReturnValue(mockStrategy),
      getStrategy: jest.fn().mockReturnValue(mockStrategy),
      register: jest.fn(),
      getAllStrategies: jest.fn().mockReturnValue([mockStrategy]),
    };

    // 使用模拟的依赖创建服务实例
    service = new MenuService(mockMenuStrategyFactory as any, mockCacheService as any);

    cacheService = mockCacheService;

    // 禁用日志输出
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getMenuTree', () => {
    it('should use cache service to get menu tree', async () => {
      const user = { userId: '1', userType: 'SYSTEM', tenantId: 'tenant1' };
      const menuTree = [{ id: 1, name: 'Menu 1' }];

      cacheService.getOrSet.mockImplementation(async (key, factory) => {
        return menuTree;
      });

      const result = await service.getMenuTree(user);

      expect(cacheService.getOrSet).toHaveBeenCalled();
      expect(result).toEqual(menuTree);
    });
  });

  describe('createMenu', () => {
    it('should create menu and clear cache', async () => {
      const user = { userId: '1', userType: 'SYSTEM', tenantId: 'tenant1' };
      const createMenuDto = {
        name: 'New Menu',
        path: '/new-menu',
        type: MenuType.MENU,
        orderNo: 1,
        status: 1,
      };
      const createdMenu = { id: 1, name: 'New Menu', path: '/new-menu' };

      mockStrategy.isNameExists.mockResolvedValue(false);
      mockStrategy.isPathExists.mockResolvedValue(false);
      mockStrategy.create.mockResolvedValue(createdMenu);
      cacheService.delete.mockResolvedValue(undefined);

      const result = await service.createMenu(createMenuDto, user);

      expect(cacheService.delete).toHaveBeenCalled();
      expect(result).toEqual(createdMenu);
    });
  });
});
