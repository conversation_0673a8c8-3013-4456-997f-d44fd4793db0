/*
 * @Author: ya<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-08 14:16:44
 * @LastEditors: yangwenqiang <EMAIL>
 * @LastEditTime: 2025-06-01 00:50:04
 * @FilePath: /multi-tenant-nestjs/src/modules/menu/strategies/menu-strategy.factory.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Injectable } from '@nestjs/common';

import { IMenuStrategy } from './menu-strategy.interface';

import { BaseStrategyFactory } from '@/core/strategies/base-strategy.factory';

/**
 * 菜单策略类型
 */
export enum MenuStrategyType {
  SYSTEM = 'SYSTEM',
  TENANT = 'TENANT',
}

/**
 * 菜单策略工厂
 * 用于创建和管理菜单策略实例
 */
@Injectable()
export class MenuStrategyFactory extends BaseStrategyFactory<IMenuStrategy> {
  /**
   * 根据用户类型获取策略
   * @param userType 用户类型
   * @returns 菜单策略实例
   */
  getStrategyByUserType(userType: string): IMenuStrategy {
    const type = userType === 'SYSTEM' ? MenuStrategyType.SYSTEM : MenuStrategyType.TENANT;
    const strategy = this.getStrategy(type);

    if (!strategy)  {
      throw new Error(`Strategy not found for type: ${type}`);
    }

    return strategy;
  }
}
