import { CreateMenuDto, MenuDto, MenuTreeDto, UpdateMenuDto } from '../dto/menu.dto';

import { IBaseStrategy } from '@/core/strategies/base-strategy.interface';

/**
 * 菜单策略接口
 * 定义菜单相关的操作
 */
export interface IMenuStrategy extends IBaseStrategy {
  /**
   * 创建菜单
   * @param createMenuDto 创建菜单数据
   * @param tenantId 租户ID（可选）
   * @returns 创建的菜单
   */
  create(createMenuDto: CreateMenuDto, tenantId?: string): Promise<MenuDto>;

  /**
   * 查询所有菜单
   * @param tenantId 租户ID（可选）
   * @returns 菜单列表
   */
  findAll(tenantId?: string): Promise<MenuDto[]>;

  /**
   * 获取菜单树
   * @param tenantId 租户ID（可选）
   * @param userId 用户ID（可选，用于过滤权限）
   * @returns 菜单树
   */
  getMenuTree(tenantId?: string, userId?: number): Promise<MenuTreeDto[]>;

  /**
   * 查询单个菜单
   * @param id 菜单ID
   * @param tenantId 租户ID（可选）
   * @returns 菜单信息
   */
  findOne(id: number, tenantId?: string): Promise<MenuDto>;

  /**
   * 更新菜单
   * @param id 菜单ID
   * @param updateMenuDto 更新菜单数据
   * @param tenantId 租户ID（可选）
   * @returns 更新后的菜单
   */
  update(id: number, updateMenuDto: UpdateMenuDto, tenantId?: string): Promise<MenuDto>;

  /**
   * 删除菜单
   * @param id 菜单ID
   * @param tenantId 租户ID（可选）
   * @returns 删除结果
   */
  remove(id: number, tenantId?: string): Promise<{ success: boolean }>;

  /**
   * 检查菜单名称是否存在
   * @param name 菜单名称
   * @param tenantId 租户ID（可选）
   * @param id 排除的菜单ID（可选）
   * @returns 是否存在
   */
  isNameExists(name: string, tenantId?: string, id?: number): Promise<boolean>;

  /**
   * 检查菜单路径是否存在
   * @param path 菜单路径
   * @param tenantId 租户ID（可选）
   * @param id 排除的菜单ID（可选）
   * @returns 是否存在
   */
  isPathExists(path: string, tenantId?: string, id?: number): Promise<boolean>;

  /**
   * 根据ID查询菜单
   * @param id 菜单ID
   * @param tenantId 租户ID（可选）
   * @returns 菜单信息
   */
  findById(id: number, tenantId?: string): Promise<MenuDto | null>;

  /**
   * 检查是否有子菜单
   * @param parentId 父菜单ID
   * @param tenantId 租户ID（可选）
   * @returns 是否有子菜单
   */
  hasChildren(parentId: number, tenantId?: string): Promise<boolean>;
}
