import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { IRequestWithProps } from '@/core/common/interfaces/request-with-props.interface';
import { RolesAllowed } from '@/decorators/roles-allowed.decorator';
import { IUserRole } from '@/types/IUserRole';

function matchRoles(rolesRequired: IUserRole[], userRole: IUserRole) {
  return rolesRequired.includes(userRole);

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(protected reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublicRoute = this.reflector.get<boolean>('allowPublicAccess', context.getHandler());
    if (isPublicRoute) return true;

    const request: IRequestWithProps = context.switchToHttp().getRequest();

    if (!request.user) return false;

    // 从用户信息中获取角色
    // 注意：我们需要根据实际情况修改这里的逻辑
    const userRole = request.user.role || 'GUEST';

    const allowedRoles = this.reflector.get(RolesAllowed, context.getHandler());
    if (!allowedRoles || allowedRoles.length === 0) return true;

    return matchRoles(allowedRoles, userRole ?? 'GUEST');
}
