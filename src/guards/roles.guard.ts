/*
 * @Author: ya<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-08 14:16:44
 * @LastEditors: yangwenqiang <EMAIL>
 * @LastEditTime: 2025-06-01 00:49:35
 * @FilePath: /multi-tenant-nestjs/src/guards/roles.guard.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { IRequestWithProps } from '@/core/common/interfaces/request-with-props.interface';
import { RolesAllowed } from '@/decorators/roles-allowed.decorator';
import { IUserRole } from '@/types/IUserRole';

function matchRoles(rolesRequired: IUserRole[], userRole: IUserRole) {
  return rolesRequired.includes(userRole);
}

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(protected reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublicRoute = this.reflector.get<boolean>('allowPublicAccess', context.getHandler());
    if (isPublicRoute) return true;

    const request: IRequestWithProps = context.switchToHttp().getRequest();

    if (!request.user) return false;

    // 从用户信息中获取角色
    // 注意：我们需要根据实际情况修改这里的逻辑
    const userRole = request.user.role || 'GUEST';

    const allowedRoles = this.reflector.get(RolesAllowed, context.getHandler());
    if (!allowedRoles || allowedRoles.length === 0) return true;

    return matchRoles(allowedRoles, userRole ?? 'GUEST');
  }
}
