/*
 * @Author: ya<PERSON><PERSON>qi<PERSON> <EMAIL>
 * @Date: 2025-05-09 11:56:21
 * @LastEditors: yangwenqiang <EMAIL>
 * @LastEditTime: 2025-06-01 00:48:27
 * @FilePath: /multi-tenant-nestjs/src/core/health/health.module.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';

import { HealthController } from './health.controller';
import { PrismaHealthIndicator } from './indicators/prisma.health';
import { PrismaModule } from '../database/prisma/prisma.module';

/**
 * 健康检查模块
 * 提供应用程序的健康状态监控
 */
@Module({
  imports: [TerminusModule, HttpModule, PrismaModule],
  controllers: [HealthController],
  providers: [PrismaHealthIndicator],
})
export class HealthModule {}
