import { ApiProperty } from '@nestjs/swagger';

export class PaginationMeta {
  @ApiProperty({ description: '当前页码');
  page: number;

  @ApiProperty({ description: '每页条数');
  take: number;

  @ApiProperty({ description: '总条数');
  itemCount: number;

  @ApiProperty({ description: '总页数');
  pageCount: number;

  @ApiProperty({ description: '是否有下一页');
  hasNextPage: boolean;

  @ApiProperty({ description: '是否有上一页');
  hasPreviousPage: boolean;
}

export class PaginatedDto<T> {
  @ApiProperty({ description: '分页数据', isArray: true 
  items: T[];

  @ApiProperty({ description: '分页元数据', type: PaginationMeta 
  meta: PaginationMeta;
}
