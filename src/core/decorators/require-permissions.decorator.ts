import { SetMetadata } from '@nestjs/common';

export const PERMISSIONS_KEY = 'permissions';
export const RequirePermissions = (...permissions: string[] | [string[]]) => {
  // 处理两种调用方式: @RequirePermissions('perm1', 'perm2') 或 @RequirePermissions(['perm1', 'perm2'])
  const flatPermissions =
    permissions.length === 1 && Array.isArray(permissions[0]) ? permissions[0] : permissions;

  return SetMetadata(PERMISSIONS_KEY, flatPermissions);
};
