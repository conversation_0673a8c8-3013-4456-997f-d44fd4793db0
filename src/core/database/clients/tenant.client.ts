import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

/**
 * Tenant Schema数据库客户端
 *
 * 负责管理业务数据：
 * - 用户管理
 * - 网站管理
 * - 内容管理
 * - 通知和支付
 */
@Injectable()
export class TenantDatabaseClient implements OnModuleInit, OnModuleDestroy {
  private client: PrismaClient;

  constructor() {
    this.client = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
      errorFormat: 'pretty',
    );
  }

  async onModuleInit() {
    await this.client.$connect();
    console.log('✅ Tenant Database connected');

  async onModuleDestroy() {
    await this.client.$disconnect();
    console.log('🔌 Tenant Database disconnected');

  // 用户管理
  get user() {
    return this.client.user;
  get role() {
    return this.client.role;
  get userRole() {
    return this.client.userRole;
  // 网站管理
  get website() {
    return this.client.website;
  get websitePage() {
    return this.client.websitePage;
  // 通知系统
  get notification() {
    return this.client.notification;
  // 统计分析
  get usageStats() {
    return this.client.usageStats;
  // 支付系统
  get payment() {
    return this.client.payment;
  // 原始客户端访问（用于事务等高级操作）
  get $client() {
    return this.client;
  // 事务支持
  async $transaction<T>(
    fn: (prisma: PrismaClient) => Promise<T>,
    options?: {
      maxWait?: number;
      timeout?: number;
    },
  ): Promise<T> {
    return this.client.$transaction(fn, options);
  // 租户隔离查询助手
  async findManyWithTenant<T>(model: any, tenantId: number | null, args?: any): Promise<T[]> {
    const whereClause = tenantId
      ? { ...args?.where, tenantId }
      : { ...args?.where, tenantId: null };

    return model.findMany({
      ...args,
      where: whereClause,
    ;;
  }

  async findUniqueWithTenant<T>(model: any, tenantId: number | null, args: any): Promise<T | null> {
    return model.findFirst({
      ...args,
      where: {
        ...args.where,
        // tenantId not needed in WebsitePage,
      },
    ;;
  }

  // 健康检查
  async healthCheck(): Promise<boolean> {
    try {
      await this.client.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error('Tenant Database health check failed:', error);
      return false;
  }
}
