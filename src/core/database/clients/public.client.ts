import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient as PublicPrismaClient } from '@prisma-public/prisma/client';

/**
 * Public Schema数据库客户端
 *
 * 负责管理平台基础设施数据：
 * - 租户管理
 * - 订阅计划
 * - 系统配置
 * - 审计日志
 */
@Injectable()
export class PublicDatabaseClient implements OnModuleInit, OnModuleDestroy {
  private client: PublicPrismaClient;

  constructor() {
    this.client = new PublicPrismaClient({
      log: ['query', 'info', 'warn', 'error'],
      errorFormat: 'pretty',
    );
  }

  async onModuleInit() {
    await this.client.$connect();
    console.log('✅ Public Database connected');

  async onModuleDestroy() {
    await this.client.$disconnect();
    console.log('🔌 Public Database disconnected');

  // 租户管理
  get tenant() {
    return this.client.tenant;
  get tenantDatasource() {
    return this.client.tenantDatasource;
  // 订阅系统
  get subscriptionPlan() {
    return this.client.subscriptionPlan;
  get tenantSubscription() {
    return this.client.tenantSubscription;
  // 功能和配置
  get tenantFeature() {
    return this.client.tenantFeature;
  get tenantConfig() {
    return this.client.tenantConfig;
  get systemConfig() {
    return this.client.systemConfig;
  // 审计和日志
  get auditLog() {
    return this.client.auditLog;
  // 原始客户端访问（用于事务等高级操作）
  get $client() {
    return this.client;
  // 事务支持
  async $transaction<T>(
    fn: (prisma: PublicPrismaClient) => Promise<T>,
    options?: {
      maxWait?: number;
      timeout?: number;
    },
  ): Promise<T> {
    return this.client.$transaction(fn, options);
  // 健康检查
  async healthCheck(): Promise<boolean> {
    try {
      await this.client.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error('Public Database health check failed:', error);
      return false;
  }
}
