/*
 * @Author: ya<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-31 22:07:27
 * @LastEditors: yangwenqiang <EMAIL>
 * @LastEditTime: 2025-06-01 00:47:51
 * @FilePath: /multi-tenant-nestjs/src/core/database/database.module.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Module, Global } from '@nestjs/common';

import { PublicDatabaseClient } from './clients/public.client';
import { TenantDatabaseClient } from './clients/tenant.client';
import { DatabaseFactory } from './database.factory';

/**
 * 数据库模块
 *
 * 提供Dual-Schema架构的数据库访问能力：
 * - Public Schema客户端（平台数据）
 * - Tenant Schema客户端（业务数据）
 * - 数据库工厂（统一管理）
 */
@Global()
@Module({
  providers: [PublicDatabaseClient, TenantDatabaseClient, DatabaseFactory],
  exports: [PublicDatabaseClient, TenantDatabaseClient, DatabaseFactory],
})
export class DatabaseModule {}
