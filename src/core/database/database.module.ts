import { Module, Global } from '@nestjs/common';

import { PublicDatabaseClient } from './clients/public.client';
import { TenantDatabaseClient } from './clients/tenant.client';
import { DatabaseFactory } from './database.factory';

/**
 * 数据库模块
 *
 * 提供Dual-Schema架构的数据库访问能力：
 * - Public Schema客户端（平台数据）
 * - Tenant Schema客户端（业务数据）
 * - 数据库工厂（统一管理）
 */
@Global()
@Module({
  providers: [PublicDatabaseClient, TenantDatabaseClient, DatabaseFactory],
  exports: [PublicDatabaseClient, TenantDatabaseClient, DatabaseFactory],
)
export class DatabaseModule {}
