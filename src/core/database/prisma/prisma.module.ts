import * as process from 'process';

import {
  BadRequestException,
  Global,
  Module,
  NotFoundException,
  Scope,
  Logger,
} from '@nestjs/common';
import { REQUEST } from '@nestjs/core';

// 注释掉已删除的服务，使用新的数据库架构
// import { DatabaseFactory } from '@/core/database/database.factory';
// import { TENANT_PRISMA_SERVICE, TenantPrismaService } from './tenant-prisma.service';

import { isSystemPath } from '@/core/common/constants/system-paths.constant';
import { IRequestWithProps } from '@/core/common/interfaces/request-with-props.interface';

/**
 * 旧的Prisma模块 - 保持向后兼容
 *
 * 注意：新的项目应该使用DatabaseModule，这个模块将在Phase 2中移除
 *
 * @deprecated 使用 DatabaseModule 替代
 */
@Global()
@Module({
  exports: [], // 暂时清空exports，避免引用已删除的服务
  providers: [], // 暂时清空providers，避免引用已删除的服务
)
export class PrismaModule {}
