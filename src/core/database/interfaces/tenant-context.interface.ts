/**
 * 租户上下文接口
 *
 * 定义租户相关的所有上下文信息
 */
export interface TenantContext {
  /** 租户ID */
  tenantId: number;

  /** 租户代码 */
  tenantCode: string;

  /** 租户名称 */
  tenantName: string;

  /** 租户状态 */
  tenantStatus: number;

  /** 是否为共享数据库模式 */
  isSharedDatabase: boolean;

  /** 当前订阅计划 */
  currentSubscription?: {
    id: number;
    planId: number;
    plan: {
      code: string;
      name: string;
      features: any[];
      limits: any;
    };
    status: string;
    endDate: Date;
  };

  /** 启用的功能列表 */
  enabledFeatures: Array<{
    featureCode: string;
    enabled: boolean;
    quota?: number;
    usedQuota: number;
  }>;

  /** 租户配置 */
  configs: Array<{
    category: string;
    key: string;
    value: string;
    dataType: string;
  }>;
}

/**
 * 数据库连接上下文
 */
export interface DatabaseContext {
  /** 是否为独立数据库 */
  isDedicated: boolean;

  /** 数据库连接配置（仅独立模式） */
  connectionConfig?: {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    ssl: boolean;
  };
}

/**
 * 请求上下文中的租户信息
 */
export interface RequestTenantContext {
  /** 租户ID */
  tenantId?: number;

  /** 租户代码 */
  tenantCode?: string;

  /** 用户ID */
  userId?: number;

  /** 用户角色 */
  userRoles?: string[];

  /** 是否为系统管理员 */
  isSystemAdmin?: boolean;
}
