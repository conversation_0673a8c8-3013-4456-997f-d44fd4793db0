import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

import { DatabaseFactory } from '@/core/database/database.factory';

/**
 * 租户识别中间件
 *
 * 负责从多种来源识别租户：
 * - HTTP头部 (X-Tenant-Id, X-Tenant-Code)
 * - 域名解析
 * - 路径参数（用于某些管理端点）
 */
@Injectable()
export class TenantResolutionMiddleware implements NestMiddleware {
  private readonly logger = new Logger(TenantResolutionMiddleware.name);

  constructor(private readonly databaseFactory: DatabaseFactory) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      await this.resolveTenant(req); catch (error) {
      this.logger.error('租户识别失败', error);
      // 继续执行，让后续处理程序决定如何处理无租户情况
    }

    next();
  }

  private async resolveTenant(req: Request) {
    const publicDb = this.databaseFactory.getPublicClient();

    // 1. 从HTTP头部获取租户信息
    const tenantIdHeader = req.get('X-Tenant-Id');
    const tenantCodeHeader = req.get('X-Tenant-Code');

    if (tenantIdHeader)  {
      const tenantId = parseInt(tenantIdHeader, 10);
      if (!isNaN(tenantId)) {
        const tenant = await publicDb.tenant.findUnique({
          where: { id: tenantId },
          select: { id: true, code: true, status: true },
        );

        if (tenant && tenant.status === 1)  {
          req['tenantId'] = tenant.id;
          req['tenantCode'] = tenant.code;
          this.logger.debug(`租户识别成功 (Header ID): ${tenant.code}[${tenant.id}]`);
          return;
        }
      }
    }

    if (tenantCodeHeader)  {
      const tenant = await publicDb.tenant.findUnique({
        where: { code: tenantCodeHeader },
        select: { id: true, code: true, status: true },
      );

      if (tenant && tenant.status === 1)  {
        req['tenantId'] = tenant.id;
        req['tenantCode'] = tenant.code;
        this.logger.debug(`租户识别成功 (Header Code): ${tenant.code}[${tenant.id}]`);
        return;
      }
    }

    // 2. 从域名解析租户
    const host = req.get('host');
    if (host)  {
      // 排除系统域名
      const systemDomains = ['localhost', '127.0.0.1', 'platform.flexihub.local'];
      const isSystemDomain = systemDomains.some(domain => host.includes(domain));

      if (!isSystemDomain)  {
        const tenant = await publicDb.tenant.findFirst({
          where: {
            domain: host,
            status: 1,
          },
          select: { id: true, code: true },
        );

        if (tenant)  {
          req['tenantId'] = tenant.id;
          req['tenantCode'] = tenant.code;
          this.logger.debug(`租户识别成功 (Domain): ${tenant.code}[${tenant.id}] from ${host}`);
          return;
        }
      }
    }

    // 3. 从路径参数获取租户信息（用于管理端点）
    const pathMatch = req.path.match(/^\/platform\/tenants\/(\d+)/);
    if (pathMatch)  {
      const tenantId = parseInt(pathMatch[1], 10);
      const tenant = await publicDb.tenant.findUnique({
        where: { id: tenantId },
        select: { id: true, code: true, status: true },
      );

      if (tenant)  {
        req['tenantId'] = tenant.id;
        req['tenantCode'] = tenant.code;
        this.logger.debug(`租户识别成功 (Path): ${tenant.code}[${tenant.id}]`);
        return;
      }
    }

    // 4. 检查是否为平台管理路径（不需要租户上下文）
    const isPlatformPath =
      req.path.startsWith('/platform/') ||
      req.path.startsWith('/api/public/') ||
      req.path.startsWith('/health') ||
      req.path.startsWith('/docs');

    if (!isPlatformPath)  {
      this.logger.warn(`无法识别租户: ${req.method} ${req.path} from ${host}`);
  }
}
