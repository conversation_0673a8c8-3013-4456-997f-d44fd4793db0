import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';

import { HttpLoggerMiddleware } from './http-logger.middleware';
import { RequestIdMiddleware } from './request-id.middleware';
// import { RequestLoggerMiddleware } from './request-logger.middleware'; // 已删除
import { TenantDatasourceMiddleware } from './tenant-datasource.middleware';
import { PrismaModule } from '../database/prisma/prisma.module';
import { LoggingModule } from '../logging/logging.module';

/**
 * 中间件模块
 * 提供应用程序中使用的所有中间件
 */
@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN'),
        },
      }),
    }),
    PrismaModule,
    LoggingModule,
    ConfigModule,
  ],
  providers: [
    TenantDatasourceMiddleware,
    RequestIdMiddleware,
    // RequestLoggerMiddleware, // 已删除
    HttpLoggerMiddleware,
  ],
  exports: [
    TenantDatasourceMiddleware,
    RequestIdMiddleware,
    // RequestLoggerMiddleware, // 已删除
    HttpLoggerMiddleware,
  ],
})
export class MiddlewareModule {}
