import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class TenantRoutingMiddleware implements NestMiddleware {
  private readonly logger = new Logger(TenantRoutingMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    // 获取租户标识
    const tenantId = req.headers['x-tenant-id'] as string;

    if (tenantId)  {
      // 记录租户路由信息
      this.logger.debug(`Tenant Routing: ${tenantId} - ${req.method} ${req.url}`);

      // 可以在这里添加租户特定的路由逻辑
      // 例如，根据租户ID确定应该路由到哪个服务或处理程序
    }

    next();
  }
}
