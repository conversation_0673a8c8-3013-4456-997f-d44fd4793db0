/*
 * @Author: ya<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-09 11:54:22
 * @LastEditors: yangwenqiang <EMAIL>
 * @LastEditTime: 2025-06-01 00:48:46
 * @FilePath: /multi-tenant-nestjs/src/core/middleware/http-logger.middleware.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

import { LoggingService } from '../logging/logging.service';

/**
 * HTTP请求日志中间件
 * 记录所有HTTP请求的详细信息
 */
@Injectable()
export class HttpLoggerMiddleware implements NestMiddleware {
  constructor(private readonly loggingService: LoggingService) {}

  use(req: Request, res: Response, next: NextFunction): void {
    // 记录请求开始时间
    const startTime = Date.now();

    // 请求结束时记录日志
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      this.loggingService.logHttpRequest(req, res, duration);
    });

    next();
  }
}
