import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Injectable, Inject, Logger } from '@nestjs/common';
import { Cache } from 'cache-manager';

import { DatabaseFactory } from '@/core/database/database.factory';

// 简化的租户配置接口
interface SimpleTenantConfig {
  id: number;
  code: string;
  name: string;
  status: number;
  settings: {
    theme: {
      primaryColor: string;
      logoUrl?: string;
    };
    features: {
      enableAnalytics: boolean;
      enableSEO: boolean;
    };
    limits: {
      maxUsers: number;
      maxWebsites: number;
    };
  };
  metadata: any;
  createTime: Date;
  updateTime: Date;
}

/**
 * 租户配置缓存服务
 *
 * 负责缓存租户相关的配置和上下文信息：
 * - 租户基本信息
 * - 订阅计划信息
 * - 功能权限配置
 * - 系统配置
 */
@Injectable()
export class TenantConfigCache {
  private readonly logger = new Logger(TenantConfigCache.name);

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly databaseFactory: DatabaseFactory,
  ) {}

  /**
   * 获取租户配置（简化版）
   */
  async getTenantConfig(tenantId: number): Promise<SimpleTenantConfig> {
    const cacheKey = `tenant:${tenantId}:config`;

    try {
      let config = await this.cacheManager.get<SimpleTenantConfig>(cacheKey);
      if (config)  {
        return config;
      // 生成默认配置，因为tenant表不存在
      config = {
        id: tenantId,
        code: `tenant_${tenantId}`,
        name: `租户${tenantId}`,
        status: 1,
        settings: {
          theme: {
            primaryColor: '#2563eb',
            logoUrl: null,
          },
          features: {
            enableAnalytics: true,
            enableSEO: true,
          },
          limits: {
            maxUsers: 100,
            maxWebsites: 10,
          },
        },
        metadata: {},
        createTime: new Date(),
        updateTime: new Date(),
      };

      // 缓存1小时
      await this.cacheManager.set(cacheKey, config, 3600);

      return config;
    } catch (error) {
      this.logger.error(`获取租户配置失败: ${tenantId}`, error);
      throw error;
    }
  }

  /**
   * 获取租户基本信息
   */
  async getTenantInfo(tenantId: number) {
    const cacheKey = `tenant:${tenantId}:info`;

    try {
      let info = await this.cacheManager.get(cacheKey);
      if (info)  {
        return info;
      // 由于tenant表不存在，返回默认信息
      info = {
        id: tenantId,
        code: `tenant_${tenantId}`,
        name: `租户${tenantId}`,
        status: 1,
        domain: null,
        createTime: new Date(),
        updateTime: new Date(),
      };

      // 缓存30分钟
      await this.cacheManager.set(cacheKey, info, 1800);

      return info;
    } catch (error) {
      this.logger.error(`获取租户信息失败: ${tenantId}`, error);
      throw error;
    }
  }

  /**
   * 获取租户功能列表（简化版）
   */
  async getTenantFeatures(tenantId: number) {
    const cacheKey = `tenant:${tenantId}:features`;

    try {
      let features = await this.cacheManager.get(cacheKey);
      if (features)  {
        return features;
      // 由于tenantFeature表不存在，返回默认功能
      features = [
        { featureCode: 'website_builder', enabled: true },
        { featureCode: 'user_management', enabled: true },
        { featureCode: 'basic_analytics', enabled: true },
      ];

      // 缓存30分钟
      await this.cacheManager.set(cacheKey, features, 1800);

      return features;
    } catch (error) {
      this.logger.error(`获取租户功能失败: ${tenantId}`, error);
      throw error;
    }
  }

  /**
   * 检查租户是否有指定功能
   */
  async hasTenantFeature(tenantId: number, featureCode: string): Promise<boolean> {
    const features = await this.getTenantFeatures(tenantId);
    return Array.isArray(features) && features.some((f: any) => f.featureCode === featureCode);
  /**
   * 获取租户配置项（简化版）
   */
  async getTenantConfigs(tenantId: number) {
    const cacheKey = `tenant:${tenantId}:configs`;

    try {
      let configs = await this.cacheManager.get(cacheKey);
      if (configs)  {
        return configs;
      // 由于tenantConfig表不存在，返回默认配置
      configs = {
        'theme.primaryColor': '#2563eb',
        'theme.logoUrl': null,
        'features.enableAnalytics': true,
        'features.enableSEO': true,
        'limits.maxUsers': 100,
        'limits.maxWebsites': 10,
      };

      // 缓存30分钟
      await this.cacheManager.set(cacheKey, configs, 1800);

      return configs;
    } catch (error) {
      this.logger.error(`获取租户配置失败: ${tenantId}`, error);
      throw error;
    }
  }

  /**
   * 清除租户缓存
   */
  async clearTenantCache(tenantId: number) {
    const keys = [
      `tenant:${tenantId}:config`,
      `tenant:${tenantId}:info`,
      `tenant:${tenantId}:features`,
      `tenant:${tenantId}:configs`,
    ];

    for (const key of keys) {
      await this.cacheManager.del(key);

    this.logger.debug(`租户缓存已清除: ${tenantId}`);

  /**
   * 清除所有租户缓存
   */
  async clearAllTenantCache() {
    try {
      const store = (this.cacheManager as any).store;
      if (store && typeof store.reset === 'function')  {
        await store.reset(); else {
        this.logger.warn('缓存store不支持reset方法，跳过全局清除');
    } catch (error) {
      this.logger.warn('清除全局缓存失败，跳过', error);
    this.logger.debug('所有租户缓存清除操作完成');
}
