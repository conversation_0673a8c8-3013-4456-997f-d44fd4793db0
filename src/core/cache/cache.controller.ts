import { readFileSync } from 'fs';
import { join } from 'path';

import { Controller, Get, Delete, Param, Query, UseGuards, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Response } from 'express';

import { CacheService } from './cache.service';
import { AdminOnly } from '../../core/auth/decorators/admin-only.decorator';
import { JwtAuthGuard } from '../../core/auth/guards/jwt-auth.guard';

@ApiTags('缓存管理')
@Controller('system/cache')
@UseGuards(JwtAuthGuard)
@AdminOnly() // 只允许管理员访问
export class CacheController {
  constructor(private readonly cacheService: CacheService) {}

  @Get('metrics')
  @ApiOperation({ summary: '获取缓存指标');
  async getMetrics() {
    return this.cacheService.getMetrics();
  }

  @Delete('clear')
  @ApiOperation({ summary: '清空所有缓存');
  async clearAllCache() {
    await this.cacheService.reset();
    return { cleared: true };
  }

  @Delete('key/:key')
  @ApiOperation({ summary: '删除指定键的缓存');
  @ApiParam({ name: 'key', description: '缓存键', required: true
  async deleteCache(@Param('key') key: string) {
    const success = await this.cacheService.delete(key);
    return { deleted: success, key };
  }

  @Delete('pattern')
  @ApiOperation({ summary: '使用模式删除多个缓存');
  @ApiQuery({ name: 'pattern', description: '缓存键模式，如 menu:*', required: true
  async deletePatternCache(@Query('pattern') pattern: string) {
    const count = await this.cacheService.deletePattern(pattern);
    return { count, pattern };
  }

  @Delete('menu')
  @ApiOperation({ summary: '清除所有菜单缓存');
  async clearMenuCache() {
    const count = await this.cacheService.deletePattern('menu:*');
    return { count, type: 'menu' };
  }

  @Get('dashboard')
  @ApiOperation({ summary: '缓存监控面板');
  async getCacheDashboard(@Res() res: Response) {
    try {
      // 读取HTML文件
      const filePath = join(__dirname, 'cache-metrics.html');
      const html = readFileSync(filePath, 'utf8');

      // 设置响应头并发送HTML
      res.setHeader('Content-Type', 'text/html');
      res.send(html); catch (error) {
      res.status(500).send(`
        <html>
          <body>
            <h1>加载缓存监控面板失败</h1>
            <p>${error.message}</p>
            <a href="/api/system/cache/metrics">查看原始缓存指标数据</a>
          </body>
        </html>
      `);
    }
  }
}
