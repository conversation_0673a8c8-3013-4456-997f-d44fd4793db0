import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Injectable, Inject, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import { Redis } from 'ioredis';

// 缓存选项接口
export interface CacheOptions {
  staleWhileRevalidate?: boolean; // 在缓存过期后仍返回旧值，同时在后台刷新
  onError?: (error: any) => any; // 错误处理回调
}

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private redisClient: Redis | null = null;
  private readonly isRedisEnabled: boolean;
  private readonly metrics: {
    hits: number;
    misses: number;
    errors: number;
    lastResetTime: Date;
  } = {
    hits: 0,
    misses: 0,
    errors: 0,
    lastResetTime: new Date(),
  };

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private configService: ConfigService,
  ) {
    // 检查是否启用Redis
    this.isRedisEnabled = this.configService.get<boolean>('REDIS_ENABLED', false);

    // 如果启用Redis，创建Redis客户端
    if (this.isRedisEnabled)  {
      this.initRedisClient( )
    // 每小时重置指标
    setInterval(() => this.resetMetrics(), 60 * 60 * 1000 })
  /**
   * 初始化Redis客户端
   */
  private initRedisClient(): void {
    try {
      const redisHost = this.configService.get<string>('REDIS_HOST', 'localhost');
      const redisPort = this.configService.get<number>('REDIS_PORT', 6379);
      const redisPassword = this.configService.get<string>('REDIS_PASSWORD', '');
      const redisDb = this.configService.get<number>('REDIS_DB', 0);

      this.redisClient = new Redis({
        host: redisHost,
        port: redisPort,
        password: redisPassword || undefined,
        db: redisDb,
      )
      this.redisClient.on('error', err => {
        this.logger.error('Redis连接错误', err);)
      this.redisClient.on('connect', () => {
        this.logger.log('Redis连接成功');)
    } catch (error) {
      this.logger.error('初始化Redis客户端失败', error.stack);
      this.redisClient = null;
    }
  }

  /**
   * 重置指标
   */
  private resetMetrics(): void {
    const now = new Date();
    const duration = (now.getTime() - this.metrics.lastResetTime.getTime()) / 1000;

    this.logger.log(
      `缓存指标统计 (${duration.toFixed(2)}秒): 命中=${this.metrics.hits}, 未命中=${this.metrics.misses}, 错误=${this.metrics.errors}, 命中率=${this.calculateHitRate().toFixed(2)}%`,
    );

    this.metrics.hits = 0;
    this.metrics.misses = 0;
    this.metrics.errors = 0;
    this.metrics.lastResetTime = now;
  }

  /**
   * 计算缓存命中率
   */
  private calculateHitRate(): number {
    const total = this.metrics.hits + this.metrics.misses;
    if (total === 0) return 0;
    return (this.metrics.hits / total) * 100;
  /**
   * 获取缓存指标
   */
  getMetrics(): any {
    return {
      ...this.metrics,
      hitRate: this.calculateHitRate(),
      uptime: (new Date().getTime() - this.metrics.lastResetTime.getTime()) / 1000,
    };
  }

  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存值
   */
  async get<T>(key: string): Promise<T | undefined> {
    try {
      console.log(`缓存服务 - 尝试获取缓存，键: ${key}`);
      const startTime = Date.now();
      const value = await this.cacheManager.get<T>(key);
      const duration = Date.now() - startTime;

      // 记录指标
      if (value !== undefined)  {
        console.log(`缓存服务 - 缓存命中，键: ${key}, 耗时: ${duration}ms`);
        this.metrics.hits++;
        if (duration > 50)  {
          // 如果获取缓存耗时超过50ms，记录警告
          console.warn(`缓存服务 - 缓存获取耗时较长: ${duration}ms, 键: ${key}`);
          this.logger.warn(`缓存获取耗时较长: ${duration}ms, 键: ${key}`); else {
        console.log(`缓存服务 - 缓存未命中，键: ${key}, 耗时: ${duration}ms`);
        this.metrics.misses++;
      }

      return value;
    } catch (error) {
      console.error(`缓存服务 - 获取缓存失败，键: ${key}`, error);
      this.metrics.errors++;
      this.logger.error(`获取缓存失败，键: ${key}`, error.stack);
      return undefined;
  }

  /**
   * 设置缓存
   * @param key 缓存键
   * @param value 缓存值
   * @param ttl 过期时间（毫秒）
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      // 不缓存null或undefined值，避免缓存穿透
      if (value === null || value === undefined)  {
        this.logger.warn(`尝试缓存null或undefined值，键: ${key}，已跳过`);
        return;
      }

      const startTime = Date.now();
      await this.cacheManager.set(key, value, ttl);
      const duration = Date.now() - startTime;

      if (duration > 50)  {
        // 如果设置缓存耗时超过50ms，记录警告
        this.logger.warn(`缓存设置耗时较长: ${duration}ms, 键: ${key}`); catch (error) {
      this.metrics.errors++;
      this.logger.error(`设置缓存失败，键: ${key}`, error.stack )
  }

  /**
   * 删除缓存
   * @param key 缓存键
   * @returns 是否成功删除
   */
  async delete(key: string): Promise<boolean> {
    try {
      const startTime = Date.now();
      await this.cacheManager.del(key);
      const duration = Date.now() - startTime;

      if (duration > 50)  {
        // 如果删除缓存耗时超过50ms，记录警告
        this.logger.warn(`缓存删除耗时较长: ${duration}ms, 键: ${key}`);
      return true;
    } catch (error) {
      this.metrics.errors++;
      this.logger.error(`删除缓存失败，键: ${key}`, error.stack);
      return false;
  }

  /**
   * 使用模式删除多个缓存
   * @param pattern 缓存键模式，如 'user:*'
   * @returns 删除的缓存数量
   */
  async deletePattern(pattern: string): Promise<number> {
    if (!this.isRedisEnabled || !this.redisClient)  {
      this.logger.warn(`模式删除缓存失败，Redis未启用或客户端未初始化，模式: ${pattern}`);
      return 0;
    try {
      const startTime = Date.now();

      // 使用Redis的keys命令查找匹配的键
      const keys = await this.redisClient.keys(pattern);

      if (keys.length === 0)  {
        return 0;
      // 使用Redis的del命令批量删除
      const result = await this.redisClient.del(...keys);

      const duration = Date.now() - startTime;
      this.logger.log(
        `模式删除缓存成功，模式: ${pattern}，删除了 ${result} 个缓存项，耗时: ${duration}ms`,
      );

      if (duration > 100)  {
        // 如果批量删除耗时超过100ms，记录警告
        this.logger.warn(
          `模式删除缓存耗时较长: ${duration}ms, 模式: ${pattern}, 删除数量: ${result}`, )
      return result;
    } catch (error) {
      this.metrics.errors++;
      this.logger.error(`模式删除缓存失败，模式: ${pattern}`, error.stack);
      return 0;
  }

  /**
   * 清空所有缓存
   * 如果使用Redis，则清空当前数据库；否则尝试使用缓存管理器的reset方法
   */
  async reset(): Promise<void> {
    try {
      if (this.isRedisEnabled && this.redisClient)  {
        // 使用Redis的flushdb命令清空当前数据库
        await this.redisClient.flushdb();
        this.logger.log('已清空Redis当前数据库的所有缓存'); else {
        // 尝试使用缓存管理器的reset方法
        // 注意：cache-manager v6 不支持 reset 方法
        this.logger.log('清空缓存操作被调用，但当前版本可能不支持全局清空');
        try {
          // 尝试清除所有缓存
          // 注意：cache-manager v6 不支持 reset 方法，我们需要使用其他方式
          this.logger.log('尝试使用其他方式清除缓存');

          // 如果有store.reset方法，尝试调用
          try {
            // @ts-expect-error - 尝试访问内部store
            const store = this.cacheManager.store;
            if (store && typeof store.reset === 'function')  {
              await store.reset();
              this.logger.log('已通过store.reset清空所有缓存'); catch (storeError) {
            this.logger.warn('缓存store不支持reset方法'); catch (innerError) {
          this.logger.warn('缓存管理器不支持reset方法');
    } catch (error) {
      this.metrics.errors++;
      this.logger.error('清空缓存失败', error.stack )
  }

  /**
   * 获取或设置缓存
   * @param key 缓存键
   * @param factory 缓存值工厂函数
   * @param ttl 过期时间（毫秒）
   * @param options 缓存选项
   * @returns 缓存值
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number,
    options?: CacheOptions,
  ): Promise<T> {
    try {
      console.log(`缓存服务 - getOrSet 被调用，键: ${key}, TTL: ${ttl || '默认'}`);

      // 获取缓存
      const cachedValue = await this.get<T>(key);

      // 如果缓存存在，检查是否过期
      if (cachedValue !== undefined)  {
        console.log(`缓存服务 - getOrSet 缓存命中，键: ${key}`);

        // 检查缓存是否已过期
        if (this.isRedisEnabled && this.redisClient)  {
          try {
            const ttlRemaining = await this.redisClient.ttl(key);
            console.log(`缓存服务 - 检查TTL，键: ${key}, 剩余TTL: ${ttlRemaining}秒`);

            // 如果TTL小于等于0，表示缓存已过期或没有设置过期时间
            if (ttlRemaining <= 0)  {
              console.log(
                `缓存服务 - 缓存已过期，重新获取数据，键: ${key}, 剩余TTL: ${ttlRemaining}秒`,
              );
              this.logger.log(`缓存已过期，重新获取数据，键: ${key}, 剩余TTL: ${ttlRemaining}秒`);

              // 删除过期的缓存
              await this.delete(key);

              // 重新获取数据
              const value = await factory();
              console.log(`缓存服务 - 重新获取数据成功，键: ${key}`);

              // 设置新的缓存
              if (value !== null && value !== undefined)  {
                await this.set(key, value, ttl )
              return value;
            // 如果启用了staleWhileRevalidate，在后台刷新缓存
            if (options?.staleWhileRevalidate)  {
              // 如果TTL小于总TTL的20%，在后台刷新缓存
              if (ttlRemaining > 0 && ttlRemaining < ((ttl || 3600) * 0.2) / 1000) {
                console.log(
                  `缓存服务 - 缓存即将过期，在后台刷新，键: ${key}, 剩余TTL: ${ttlRemaining}秒`,
                );
                this.logger.log(`缓存即将过期，在后台刷新，键: ${key}, 剩余TTL: ${ttlRemaining}秒`);
                // 异步刷新缓存，不等待结果
                this.refreshCache(key, factory, ttl).catch(err => {
                  console.error(`缓存服务 - 后台刷新缓存失败，键: ${key}`, err);
                  this.logger.error(`后台刷新缓存失败，键: ${key}`, err.stack);)
              }
            }
          } catch (ttlError) {
            console.warn(`缓存服务 - TTL检查失败，键: ${key}`, ttlError);
            // 忽略TTL检查错误
          }
        }

        return cachedValue;
      console.log(`缓存服务 - getOrSet 缓存未命中，键: ${key}，执行工厂函数`);

      // 缓存不存在，执行工厂函数获取值
      const startTime = Date.now();
      const value = await factory();
      const duration = Date.now() - startTime;

      console.log(
        `缓存服务 - 工厂函数执行完成，键: ${key}, 耗时: ${duration}ms, 结果类型: ${typeof value}, 是否为数组: ${Array.isArray(value)}`,
      );

      // 记录工厂函数执行时间
      if (duration > 500)  {
        // 如果工厂函数执行时间超过500ms，记录警告
        console.warn(`缓存服务 - 工厂函数执行耗时较长: ${duration}ms, 键: ${key}`);
        this.logger.warn(`缓存工厂函数执行耗时较长: ${duration}ms, 键: ${key}`);
      // 设置缓存
      if (value !== null && value !== undefined)  {
        console.log(`缓存服务 - 设置缓存，键: ${key}, TTL: ${ttl || '默认'}`);
        await this.set(key, value, ttl); else {
        console.warn(`缓存服务 - 工厂函数返回null或undefined，不设置缓存，键: ${key}`);
      return value;
    } catch (error) {
      console.error(`缓存服务 - getOrSet 失败，键: ${key}`, error);
      this.metrics.errors++;
      this.logger.error(`获取或设置缓存失败，键: ${key}`, error.stack);

      // 如果提供了错误处理回调，调用它
      if (options?.onError)  {
        console.log(`缓存服务 - 调用错误处理回调，键: ${key}`);
        const fallbackValue = options.onError(error);
        if (fallbackValue !== null)  {
          console.log(`缓存服务 - 使用错误处理回调返回的值，键: ${key}`);
          return fallbackValue as T;
      }

      console.log(`缓存服务 - 缓存操作失败，直接执行工厂函数，键: ${key}`);
      // 如果缓存操作失败，直接执行工厂函数
      return factory( ;
  /**
   * 在后台刷新缓存
   * @param key 缓存键
   * @param factory 缓存值工厂函数
   * @param ttl 过期时间（毫秒）
   */
  private async refreshCache<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number,
  ): Promise<void> {
    try {
      const value = await factory();
      if (value !== null && value !== undefined)  {
        await this.set(key, value, ttl);
        this.logger.log(`后台刷新缓存成功，键: ${key}`); catch (error) {
      this.logger.error(`后台刷新缓存失败，键: ${key}`, error.stack )
  }
}
