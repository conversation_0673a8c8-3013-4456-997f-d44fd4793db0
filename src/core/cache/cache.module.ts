import { CacheModule as NestCacheModule } from '@nestjs/cache-manager';
import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { redisStore } from 'cache-manager-redis-store';

import { CacheController } from './cache.controller';
import { CacheService } from './cache.service';

@Global()
@Module({
  imports: [
    NestCacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const isRedisEnabled = configService.get<boolean>('REDIS_ENABLED', false);

        if (isRedisEnabled)  {
          const redisHost = configService.get<string>('REDIS_HOST', 'localhost');
          const redisPort = configService.get<number>('REDIS_PORT', 6379);
          const redisPassword = configService.get<string>('REDIS_PASSWORD', '');
          const redisDb = configService.get<number>('REDIS_DB', 0);
          const ttl = configService.get<number>('CACHE_TTL', 60 * 60 * 1000); // 默认1小时

          const redisConfig = {
            socket: {
              host: redisHost,
              port: redisPort,
            },
            password: redisPassword || undefined,
            database: redisDb,
            store: redisStore,
          };

          return {
            store: redisStore,
            ...redisConfig,
            ttl,
          };
        } else {
          // 使用内存缓存
          return {
            ttl: configService.get<number>('CACHE_TTL', 60 * 60 * 1000), // 默认1小时
          };
        }
      },
    }),
  ],
  controllers: [CacheController],
  providers: [CacheService],
  exports: [CacheService],
})
export class CacheModule {}
