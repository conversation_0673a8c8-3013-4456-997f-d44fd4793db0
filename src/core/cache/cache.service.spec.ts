import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { CacheService } from './cache.service';

describe('CacheService', () => {
  let service: CacheService;
  let cacheManager: any;

  beforeEach(async () => {
    // 创建模拟的缓存管理器
    const mockCacheManager = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      reset: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CacheService,
        {
          provide: CACHE_MANAGER,
          useValue: mockCacheManager,
        },
      ],
  }).compile();

    service = module.get<CacheService>(CacheService);
    cacheManager = mockCacheManager;

    // 禁用日志输出
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('get', () => {
    it('should call cacheManager.get with the correct key', async () => {
      const key = 'test-key';
      const value = { data: 'test-data' };
      cacheManager.get.mockResolvedValue(value);

      const result = await service.get(key);

      expect(cacheManager.get).toHaveBeenCalledWith(key);
      expect(result).toEqual(value);
    });

    it('should return undefined when an error occurs', async () => {
      const key = 'test-key';
      cacheManager.get.mockRejectedValue(new Error('Test error'));

      const result = await service.get(key);

      expect(result).toBeUndefined();
    });
  });

  describe('set', () => {
    it('should call cacheManager.set with the correct parameters', async () => {
      const key = 'test-key';
      const value = { data: 'test-data' };
      const ttl = 1000;

      await service.set(key, value, ttl);

      expect(cacheManager.set).toHaveBeenCalledWith(key, value, ttl);
    });

    it('should not throw when an error occurs', async () => {
      const key = 'test-key';
      const value = { data: 'test-data' };
      cacheManager.set.mockRejectedValue(new Error('Test error'));

      await expect(service.set(key, value)).resolves.not.toThrow(););
  });

  describe('delete', () => {
    it('should call cacheManager.del with the correct key', async () => {
      const key = 'test-key';

      await service.delete(key);

      expect(cacheManager.del).toHaveBeenCalledWith(key);
    });

    it('should not throw when an error occurs', async () => {
      const key = 'test-key';
      cacheManager.del.mockRejectedValue(new Error('Test error'));

      await expect(service.delete(key)).resolves.not.toThrow(););
  });

  describe('reset', () => {
    it('should not throw when called', async () => {
      await expect(service.reset()).resolves.not.toThrow(););
  });

  describe('getOrSet', () => {
    it('should return cached value when it exists', async () => {
      const key = 'test-key';
      const value = { data: 'cached-data' };
      const factory = jest.fn().mockResolvedValue({ data: 'new-data');;
      cacheManager.get.mockResolvedValue(value);

      const result = await service.getOrSet(key, factory);

      expect(cacheManager.get).toHaveBeenCalledWith(key);
      expect(factory).not.toHaveBeenCalled();
      expect(result).toEqual(value);
    });

    it('should call factory and set cache when cached value does not exist', async () => {
      const key = 'test-key';
      const value = { data: 'new-data' };
      const factory = jest.fn().mockResolvedValue(value);
      const ttl = 1000;
      cacheManager.get.mockResolvedValue(undefined);

      const result = await service.getOrSet(key, factory, ttl);

      expect(cacheManager.get).toHaveBeenCalledWith(key);
      expect(factory).toHaveBeenCalled();
      expect(cacheManager.set).toHaveBeenCalledWith(key, value, ttl);
      expect(result).toEqual(value);
    });

    it('should call factory when cache operation fails', async () => {
      const key = 'test-key';
      const value = { data: 'new-data' };
      const factory = jest.fn().mockResolvedValue(value);
      cacheManager.get.mockRejectedValue(new Error('Test error'));

      const result = await service.getOrSet(key, factory);

      expect(cacheManager.get).toHaveBeenCalledWith(key);
      expect(factory).toHaveBeenCalled();
      expect(result).toEqual(value);
    });
  });
});
