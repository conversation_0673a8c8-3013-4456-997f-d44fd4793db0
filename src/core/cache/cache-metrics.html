<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>缓存监控面板</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      body {
        padding: 20px;
        background-color: #f8f9fa;
      }
      .card {
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      .metric-value {
        font-size: 2rem;
        font-weight: bold;
      }
      .metric-label {
        font-size: 1rem;
        color: #6c757d;
      }
      .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1 class="mb-4">缓存监控面板</h1>

      <div class="row">
        <div class="col-md-3">
          <div class="card">
            <div class="card-body text-center">
              <div class="metric-value" id="hitRate">0%</div>
              <div class="metric-label">命中率</div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card">
            <div class="card-body text-center">
              <div class="metric-value" id="hits">0</div>
              <div class="metric-label">命中次数</div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card">
            <div class="card-body text-center">
              <div class="metric-value" id="misses">0</div>
              <div class="metric-label">未命中次数</div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card">
            <div class="card-body text-center">
              <div class="metric-value" id="errors">0</div>
              <div class="metric-label">错误次数</div>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-4">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title">缓存性能</h5>
            </div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="cacheChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-4">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title">缓存管理</h5>
            </div>
            <div class="card-body">
              <div class="mb-3">
                <button id="clearAllCache" class="btn btn-danger">清空所有缓存</button>
                <button id="clearMenuCache" class="btn btn-warning ms-2">清空菜单缓存</button>
              </div>
              <div class="mb-3">
                <label for="patternInput" class="form-label">模式删除缓存</label>
                <div class="input-group">
                  <input
                    type="text"
                    class="form-control"
                    id="patternInput"
                    placeholder="例如: menu:*"
                  />
                  <button class="btn btn-primary" id="deletePattern">删除</button>
                </div>
              </div>
              <div class="mb-3">
                <label for="keyInput" class="form-label">删除指定键的缓存</label>
                <div class="input-group">
                  <input type="text" class="form-control" id="keyInput" placeholder="缓存键" />
                  <button class="btn btn-primary" id="deleteKey">删除</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title">操作日志</h5>
            </div>
            <div class="card-body">
              <div class="log-container" style="height: 300px; overflow-y: auto">
                <ul class="list-group" id="logList"></ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // 图表初始化
      const ctx = document.getElementById('cacheChart').getContext('2d');
      const cacheChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: [],
          datasets: [
            {
              label: '命中率',
              data: [],
              borderColor: 'rgba(75, 192, 192, 1)',
              backgroundColor: 'rgba(75, 192, 192, 0.2)',
              tension: 0.4,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '百分比 (%)',
              },
            },
            x: {
              title: {
                display: true,
                text: '时间',
              },
            },
          },
        },
      });

      // 添加日志
      function addLog(message, type = 'info') {
        const logList = document.getElementById('logList');
        const logItem = document.createElement('li');
        logItem.className = `list-group-item list-group-item-${type}`;

        const timestamp = new Date().toLocaleTimeString();
        logItem.textContent = `[${timestamp}] ${message}`;

        logList.prepend(logItem);
      }

      // 更新指标
      function updateMetrics() {
        fetch('/api/system/cache/metrics')
          .then(response => response.json())
          .then(data => {
            document.getElementById('hitRate').textContent = `${data.hitRate.toFixed(2)}%`;
            document.getElementById('hits').textContent = data.hits;
            document.getElementById('misses').textContent = data.misses;
            document.getElementById('errors').textContent = data.errors;

            // 更新图表
            const now = new Date().toLocaleTimeString();
            cacheChart.data.labels.push(now);
            cacheChart.data.datasets[0].data.push(data.hitRate);

            // 保持最近10个数据点
            if (cacheChart.data.labels.length > 10) {
              cacheChart.data.labels.shift();
              cacheChart.data.datasets[0].data.shift();
            }

            cacheChart.update();
          })
          .catch(error => {
            console.error('获取指标失败:', error);
            addLog(`获取指标失败: ${error.message}`, 'danger');
          });
      }

      // 初始化事件监听
      document.addEventListener('DOMContentLoaded', function () {
        // 清空所有缓存
        document.getElementById('clearAllCache').addEventListener('click', function () {
          if (confirm('确定要清空所有缓存吗？')) {
            fetch('/api/system/cache/clear', { method: 'DELETE' })
              .then(response => response.json())
              .then(data => {
                addLog(data.message, 'success');
              })
              .catch(error => {
                addLog(`清空缓存失败: ${error.message}`, 'danger');
              });
          }
        });

        // 清空菜单缓存
        document.getElementById('clearMenuCache').addEventListener('click', function () {
          fetch('/api/system/cache/menu', { method: 'DELETE' })
            .then(response => response.json())
            .then(data => {
              addLog(data.message, 'success');
            })
            .catch(error => {
              addLog(`清空菜单缓存失败: ${error.message}`, 'danger');
            });
        });

        // 模式删除缓存
        document.getElementById('deletePattern').addEventListener('click', function () {
          const pattern = document.getElementById('patternInput').value.trim();
          if (!pattern) {
            addLog('请输入缓存键模式', 'warning');
            return;
          }

          fetch(`/api/system/cache/pattern?pattern=${encodeURIComponent(pattern)}`, {
            method: 'DELETE',
          })
            .then(response => response.json())
            .then(data => {
              addLog(data.message, 'success');
            })
            .catch(error => {
              addLog(`模式删除缓存失败: ${error.message}`, 'danger');
            });
        });

        // 删除指定键的缓存
        document.getElementById('deleteKey').addEventListener('click', function () {
          const key = document.getElementById('keyInput').value.trim();
          if (!key) {
            addLog('请输入缓存键', 'warning');
            return;
          }

          fetch(`/api/system/cache/key/${encodeURIComponent(key)}`, { method: 'DELETE' })
            .then(response => response.json())
            .then(data => {
              addLog(data.message, data.success ? 'success' : 'warning');
            })
            .catch(error => {
              addLog(`删除缓存失败: ${error.message}`, 'danger');
            });
        });

        // 初始化
        updateMetrics();
        addLog('缓存监控面板已加载', 'info');

        // 定时更新指标
        setInterval(updateMetrics, 5000);
      });
    </script>
  </body>
</html>
