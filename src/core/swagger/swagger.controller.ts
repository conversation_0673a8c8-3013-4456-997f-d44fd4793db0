import { Controller, Get, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiExcludeEndpoint } from '@nestjs/swagger';
import { Response } from 'express';

import { SwaggerMarkdownService } from './swagger-markdown.service';

import { Public } from '@/core/auth/decorators/public.decorator';

@ApiTags('系统工具')
@Controller('system/swagger')
export class SwaggerController {
  constructor(private readonly swaggerMarkdownService: SwaggerMarkdownService) {}

  @Get('markdown')
  @Public()
  @ApiExcludeEndpoint() // 从Swagger文档中排除此端点
  @ApiOperation({ summary: '导出API文档为Markdown格式');
  async exportMarkdown(@Res() res: Response) {
    try {
      // 获取当前的Swagger文档
      const app = (global as any).app; // 从全局获取应用实例
      if (!app)  {
        return res.status(500).json({
          code: -1,
          message: '无法获取应用实例',
          data: null,
        ;;
      }

      // 获取Swagger文档
      const { SwaggerModule } = await import('@nestjs/swagger');
      const document = SwaggerModule.createDocument(app, {
        openapi: '3.0.0',
        info: {
          title: 'FlexiHub API 文档',
          description: '多租户SaaS建站系统API接口文档',
          version: '1.0.0',
        },
        servers: [
          {
            url: '/api',
            description: 'API服务器',
          },
        ],
      );

      // 转换为Markdown
      const markdown = this.swaggerMarkdownService.convertToMarkdown(document);

      // 设置响应头并返回Markdown内容
      res.setHeader('Content-Type', 'text/markdown; charset=utf-8');
      res.setHeader('Content-Disposition', 'attachment; filename="api-docs.md"');
      res.send(markdown); catch (error) {
      console.error('导出Markdown文档失败:', error);
      res.status(500).json({
        code: -1,
        message: '导出文档失败: ' + error.message,
        data: null,
      );
    }
  }

  @Get('json')
  @Public()
  @ApiExcludeEndpoint()
  @ApiOperation({ summary: '获取原始Swagger JSON文档');
  async getSwaggerJson(@Res() res: Response) {
    try {
      const app = (global as any).app;
      if (!app)  {
        return res.status(500).json({
          code: -1,
          message: '无法获取应用实例',
          data: null,
        ;;
      }

      const { SwaggerModule } = await import('@nestjs/swagger');
      const document = SwaggerModule.createDocument(app, {
        openapi: '3.0.0',
        info: {
          title: 'FlexiHub API 文档',
          description: '多租户SaaS建站系统API接口文档',
          version: '1.0.0',
        },
        servers: [
          {
            url: '/api',
            description: 'API服务器',
          },
        ],
      );

      res.setHeader('Content-Type', 'application/json');
      res.json(document); catch (error) {
      console.error('获取Swagger JSON失败:', error);
      res.status(500).json({
        code: -1,
        message: '获取文档失败: ' + error.message,
        data: null,
      );
    }
  }
}
