import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

/**
 * JWT载荷接口
 */
export interface JwtPayload {
  sub: string;
  username: string;
  userType: 'SYSTEM' | 'TENANT';
  tenantId?: string; // 租户的数字ID
  tenantCode?: string; // 租户代码
}

/**
 * JWT认证策略
 */
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
    });
  }

  /**
   * 验证JWT载荷并返回用户信息
   * @param payload JWT载荷
   * @returns 用户信息
   */
  async validate(payload: JwtPayload) {
    return {
      userId: payload.sub,
      username: payload.username,
      userType: payload.userType,
      tenantId: payload.tenantId, // 租户的数字ID
      tenantCode: payload.tenantCode, // 租户代码
    };
  }
}
