import { LoginDto } from '../dto/login.dto';
import { UserInfoDto } from '../dto/user-info.dto';

/**
 * 认证策略接口
 * 定义认证相关的方法
 */
export interface IAuthStrategy {
  /**
   * 登录方法
   * @param loginDto 登录信息
   * @returns 登录结果，包含访问令牌
   */
  login(loginDto: LoginDto): Promise<{ accessToken: string }>;

  /**
   * 获取用户信息
   * @param userId 用户ID
   * @param tenantId 租户ID（可选）
   * @returns 用户信息
   */
  getUserInfo(userId: string, tenantId?: string): Promise<UserInfoDto>;

  /**
   * 获取权限码
   * @param userId 用户ID
   * @param tenantId 租户ID（可选）
   * @returns 权限码列表
   */
  getPermissionCodes(userId: string, tenantId?: string): Promise<string[]>;

  /**
   * 获取角色编码
   * @param userId 用户ID
   * @param tenantId 租户ID（可选）
   * @returns 角色编码列表
   */
  getRoleCodes(userId: string, tenantId?: string): Promise<string[]>;

  /**
   * 刷新令牌
   * @param userId 用户ID
   * @param tenantId 租户ID（可选）
   * @returns 新的访问令牌
   */
  refreshToken(userId: string, tenantId?: string): Promise<{ accessToken: string }>;
}
