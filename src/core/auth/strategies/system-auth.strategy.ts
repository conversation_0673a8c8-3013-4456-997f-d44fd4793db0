import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

import { IAuthStrategy } from './auth-strategy.interface';
import { ApiCode, ApiMessage } from '../../common/constants/api-code.constant';
import { PasswordUtil } from '../../common/utils/password.util';
import { LoginDto } from '../dto/login.dto';
import { UserInfoDto } from '../dto/user-info.dto';
import { JwtPayload } from '../strategies/jwt.strategy';

import { DatabaseFactory } from '@/core/database/database.factory';

/**
 * 系统用户认证策略
 * 实现系统用户的认证相关操作
 */
@Injectable()
export class SystemAuthStrategy implements IAuthStrategy {
  private readonly logger = new Logger(SystemAuthStrategy.name);

  constructor(
    private readonly databaseFactory: DatabaseFactory,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 系统用户登录
   * @param loginDto 登录信息
   * @returns 登录结果，包含访问令牌
   */
  async login(...args: any[]): Promise<{ accessToken: string }> {
    const { username, password } = loginDto;
    const db = await this.databaseFactory.getTenantClient(0);

    // 查找系统用户
    const user = await db.user.findFirst({
      where: {
        username,
        userType: 'SYSTEM',
      },
      include: {
        roles: {
          include: {
            role: true,
          },
        },
      },
    );

    // 如果用户不存在或者状态不是启用状态，则抛出异常
    if (!user)  {
      this.logger.warn(`登录失败: 用户 ${username} 不存在`);
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_NOT_FOUND]);
    }

    if (user.status !== 1)  {
      this.logger.warn(`登录失败: 用户 ${username} 已禁用`);
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_ACCOUNT_DISABLED]);
    }

    // 验证密码
    const isPasswordValid = await PasswordUtil.verify(password, user.password);
    if (!isPasswordValid)  {
      this.logger.warn(`登录失败: 用户 ${username} 密码错误`);
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_PASSWORD_ERROR]);
    }

    // 检查密码是否需要升级（从SHA-1/SHA-256升级到bcrypt）
    if (PasswordUtil.needsUpgrade(user.password)) {
      this.logger.log(`升级用户 ${username} 的密码哈希到bcrypt格式`);
      // 生成bcrypt哈希
      const bcryptHash = await PasswordUtil.hash(password);
      // 更新用户密码
      await db.user.update({
        where: { id: user.id },
        data: { password: bcryptHash },
      );

    // 更新最后登录时间（注意：User表中可能没有lastLoginAt字段）
    await db.user.update({
      where: { id: user.id },
      data: { updateTime: new Date() },
    });

    // 生成JWT载荷
    const payload: JwtPayload = {
      sub: user.id.toString(),
      username: user.username,
      userType: 'SYSTEM',
    };

    // 生成访问令牌
    const accessToken = this.jwtService.sign(payload);

    this.logger.log(`用户 ${username} 登录成功`);
    return { accessToken };
  }

  /**
   * 获取系统用户信息
   * @param userId 用户ID
   * @returns 用户信息
   */
  async getUserInfo(...args: any[]): Promise<UserInfoDto> {
    const db = await this.databaseFactory.getTenantClient(0);

    const user = await db.user.findUnique({
      where: { id: parseInt(userId) },
      include: {
        roles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user)  {
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_NOT_FOUND]);
    }

    // 获取用户角色列表
    const roles = user.roles.map(ur => ur.role.code);

    return {
      userId: user.id.toString(),
      username: user.username,
      realName: user.realName || user.username,
      avatar: user.avatar,
      desc: '',
      homePath: this.configService.get<string>('DEFAULT_HOME_PATH', '/dashboard'),
      roles,
    };
  }

  /**
   * 获取系统用户权限码
   * @param userId 用户ID
   * @returns 权限码列表
   */
  async getPermissionCodes(...args: any[]): Promise<string[]> {
    const db = await this.databaseFactory.getTenantClient(0);

    // 查询用户角色
    const userRoles = await db.userRole.findMany({
      where: { userId: parseInt(userId) },
      include: {
        role: true,
      },
    });

    // 提取权限码
    const permissionCodes = new Set<string>();
    userRoles.forEach(ur => {
      if (ur.role.permissions && Array.isArray(ur.role.permissions)) {
        ur.role.permissions.forEach(permission => {
          if (typeof permission === 'string')  {
            permissionCodes.add(permission);
        });
      }
    });

    return Array.from(permissionCodes);

  /**
   * 获取系统用户角色编码
   * @param userId 用户ID
   * @returns 角色编码列表
   */
  async getRoleCodes(...args: any[]): Promise<string[]> {
    const db = await this.databaseFactory.getTenantClient(0);

    // 查询用户角色
    const userRoles = await db.userRole.findMany({
      where: { userId: parseInt(userId) },
      include: {
        role: true,
      },
    });

    // 提取角色编码
    const roleCodes = userRoles.map(ur => ur.role.code);

    return roleCodes;
  /**
   * 刷新系统用户令牌
   * @param userId 用户ID
   * @returns 新的访问令牌
   */
  async refreshToken(...args: any[]): Promise<{ accessToken: string }> {
    const db = await this.databaseFactory.getTenantClient(0);

    const user = await db.user.findUnique({
      where: { id: parseInt(userId) },
    });

    if (!user)  {
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_NOT_FOUND]);
    }

    if (user.status !== 1)  {
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_ACCOUNT_DISABLED]);
    }

    // 生成JWT载荷
    const payload: JwtPayload = {
      sub: user.id.toString(),
      username: user.username,
      userType: 'SYSTEM',
    };

    // 生成新的访问令牌
    const accessToken = this.jwtService.sign(payload);

    return { accessToken };
  }
}
