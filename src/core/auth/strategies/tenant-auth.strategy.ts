import {
  Injectable,
  UnauthorizedException,
  Logger,
  BadRequestException,
  Inject,
  Scope,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

import { IAuthStrategy } from './auth-strategy.interface';
import { ApiCode, ApiMessage } from '../../common/constants/api-code.constant';
import { PasswordUtil } from '../../common/utils/password.util';
import { LoginDto } from '../dto/login.dto';
import { UserInfoDto } from '../dto/user-info.dto';
import { JwtPayload } from '../strategies/jwt.strategy';

import { DatabaseFactory } from '@/core/database/database.factory';

/**
 * 租户用户认证策略
 * 实现租户用户的认证相关操作
 */
@Injectable({ scope: Scope.REQUEST })
export class TenantAuthStrategy implements IAuthStrategy {
  private readonly logger = new Logger(TenantAuthStrategy.name);

  constructor(
    private readonly databaseFactory: DatabaseFactory,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 租户用户登录
   * @param loginDto 登录信息
   * @returns 登录结果，包含访问令牌
   */
  async login(...args: any[]): Promise<{ accessToken: string }> {
    const { username, password, tenantCode } = loginDto;

    if (!tenantCode)  {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    // 由于当前Schema中没有tenant表，我们暂时使用tenantId直接访问
    // 这里需要根据实际业务逻辑调整
    const tenantId = parseInt(tenantCode) || 1; // 暂时使用默认租户ID

    try {
      const tenantDb = await this.databaseFactory.getTenantClient(tenantId);

      // 查找租户用户
      const user = await tenantDb.user.findFirst({
        where: {
          username,
          userType: 'TENANT',
        },
        include: {
          roles: {
            include: {
              role: true,
            },
          },
        },
      );

      // 如果用户不存在或者状态不是启用状态，则抛出异常
      if (!user)  {
        this.logger.warn(`登录失败: 用户 ${username} 不存在`);
        throw new UnauthorizedException(ApiMessage[ApiCode.USER_NOT_FOUND]);
      }

      if (user.status !== 1)  {
        this.logger.warn(`登录失败: 用户 ${username} 已禁用`);
        throw new UnauthorizedException(ApiMessage[ApiCode.USER_ACCOUNT_DISABLED]);
      }

      // 验证密码
      const isPasswordValid = await PasswordUtil.verify(password, user.password);
      if (!isPasswordValid)  {
        this.logger.warn(`登录失败: 用户 ${username} 密码错误`);
        throw new UnauthorizedException(ApiMessage[ApiCode.USER_PASSWORD_ERROR]);
      }

      // 检查密码是否需要升级（从SHA-1/SHA-256升级到bcrypt）
      if (PasswordUtil.needsUpgrade(user.password)) {
        this.logger.log(`升级租户 ${tenantCode} 用户 ${username} 的密码哈希到bcrypt格式`);
        // 生成bcrypt哈希
        const bcryptHash = await PasswordUtil.hash(password);
        // 更新用户密码
        await tenantDb.user.update({
          where: { id: user.id },
          data: { password: bcryptHash },
        );

      // 更新最后登录时间
      await tenantDb.user.update({
        where: { id: user.id },
        data: { updateTime: new Date() },
      });

      // 生成JWT载荷
      const payload: JwtPayload = {
        sub: user.id.toString(),
        username: user.username,
        userType: 'TENANT',
        tenantId: tenantId.toString(),
        tenantCode: tenantCode,
      };

      // 生成访问令牌
      const accessToken = this.jwtService.sign(payload);

      this.logger.log(`租户 ${tenantCode} 用户 ${username} 登录成功`);
      return { accessToken };
    } catch (error) {
      this.logger.error(`登录失败: ${error.message}`);
      throw new UnauthorizedException(ApiMessage[ApiCode.ERROR]);
    }
  }

  /**
   * 获取租户用户信息
   * @param userId 用户ID
   * @param tenantId 租户ID
   * @returns 用户信息
   */
  async getUserInfo(...args: any[]): Promise<UserInfoDto> {
    if (!tenantId)  {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    const tenantDb = await this.databaseFactory.getTenantClient(parseInt(tenantId));

    const user = await tenantDb.user.findUnique({
      where: {
        id: parseInt(userId),
      },
      include: {
        roles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user)  {
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_NOT_FOUND]);
    }

    // 获取用户角色列表
    const roles = user.roles.map(ur => ur.role.code);

    return {
      userId: user.id.toString(),
      username: user.username,
      realName: user.realName || user.username,
      avatar: user.avatar,
      desc: '',
      homePath: this.configService.get<string>('DEFAULT_HOME_PATH', '/dashboard'),
      roles,
    };
  }

  /**
   * 获取租户用户权限码
   * @param userId 用户ID
   * @param tenantId 租户ID
   * @returns 权限码列表
   */
  async getPermissionCodes(...args: any[]): Promise<string[]> {
    if (!tenantId)  {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    const tenantDb = await this.databaseFactory.getTenantClient(parseInt(tenantId));

    // 查询用户角色
    const userRoles = await tenantDb.userRole.findMany({
      where: { userId: parseInt(userId) },
      include: {
        role: true,
      },
    });

    // 提取权限码
    const permissionCodes = new Set<string>();
    userRoles.forEach(ur => {
      if (ur.role.permissions && Array.isArray(ur.role.permissions)) {
        ur.role.permissions.forEach(permission => {
          if (typeof permission === 'string')  {
            permissionCodes.add(permission);
        });
      }
    });

    return Array.from(permissionCodes);

  /**
   * 获取租户用户角色编码
   * @param userId 用户ID
   * @param tenantId 租户ID
   * @returns 角色编码列表
   */
  async getRoleCodes(...args: any[]): Promise<string[]> {
    if (!tenantId)  {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    const tenantDb = await this.databaseFactory.getTenantClient(parseInt(tenantId));

    // 查询用户角色
    const userRoles = await tenantDb.userRole.findMany({
      where: { userId: parseInt(userId) },
      include: {
        role: true,
      },
    });

    // 提取角色编码
    const roleCodes = userRoles.map(ur => ur.role.code);

    return roleCodes;
  /**
   * 刷新租户用户令牌
   * @param userId 用户ID
   * @param tenantId 租户ID
   * @returns 新的访问令牌
   */
  async refreshToken(...args: any[]): Promise<{ accessToken: string }> {
    if (!tenantId)  {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    const tenantDb = await this.databaseFactory.getTenantClient(parseInt(tenantId));

    const user = await tenantDb.user.findUnique({
      where: { id: parseInt(userId) },
    });

    if (!user)  {
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_NOT_FOUND]);
    }

    if (user.status !== 1)  {
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_ACCOUNT_DISABLED]);
    }

    // 生成JWT载荷
    const payload: JwtPayload = {
      sub: user.id.toString(),
      username: user.username,
      userType: 'TENANT',
      tenantId: tenantId,
    };

    // 生成新的访问令牌
    const accessToken = this.jwtService.sign(payload);

    return { accessToken };
  }
}
