import {
  Controller,
  Post,
  Body,
  Get,
  UseGuards,
  Req,
  HttpCode,
  Injectable,
  Scope,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { Public } from './decorators/public.decorator';
import { LoginDto, LoginResponseDto } from './dto/login.dto';
import { UserInfoDto } from './dto/user-info.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { AuthService } from './services/auth.service';

/**
 * 认证控制器
 * 处理认证相关的API请求
 */
@ApiTags('认证')
@Controller()
@Injectable({ scope: Scope.REQUEST })
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  /**
   * 登录接口
   * @param loginDto 登录信息
   * @param req 请求对象
   * @returns 登录结果，包含访问令牌
   */
  @Public()
  @Post('auth/login')
  @HttpCode(200)
  @ApiOperation({ summary: '用户登录' })
  @ApiResponse({ status: 200, description: '登录成功', type: LoginResponseDto })
  @ApiResponse({ status: 401, description: '用户名或密码错误' })
  async login(@Body() loginDto: LoginDto, @Req() req: any) {
    // 使用统一的认证服务处理登录，传递请求对象以获取租户信息
    return this.authService.login(loginDto, req);
  }

  /**
   * 获取用户信息接口
   * @param req 请求对象
   * @returns 用户信息
   */
  @Get('user/info')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取用户信息' })
  @ApiResponse({ status: 200, description: '获取成功', type: UserInfoDto })
  async getUserInfo(@Req() req: any) {
    return this.authService.getUserInfo(req.user);
  }

  /**
   * 获取角色编码接口
   * @param req 请求对象
   * @returns 角色编码列表
   */
  @Get('auth/codes')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取角色编码' })
  @ApiResponse({ status: 200, description: '获取成功', type: [String] })
  async getRoleCodes(@Req() req: any) {
    return this.authService.getRoleCodes(req.user);
  }

  /**
   * 刷新令牌接口
   * @param req 请求对象
   * @returns 新的访问令牌
   */
  @Post('auth/refresh')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '刷新令牌' })
  @ApiResponse({ status: 200, description: '刷新成功', type: LoginResponseDto })
  async refreshToken(@Req() req: any) {
    return this.authService.refreshToken(req.user);
  }

  /**
   * 退出登录接口
   * @returns 退出结果
   */
  @Post('auth/logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '退出登录' })
  @ApiResponse({ status: 200, description: '退出成功' })
  async logout() {
    // 客户端只需要清除本地存储的令牌即可
    return { success: true };
  }
}
