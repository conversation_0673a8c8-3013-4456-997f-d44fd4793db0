import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { PERMISSIONS_KEY } from '../decorators/permissions.decorator';

import { PermissionService } from '@/modules/permission/permission.service';

/**
 * 权限守卫
 * 用于验证用户是否具有访问特定资源的权限
 */
@Injectable()
export class PermissionsGuard implements CanActivate {
  private readonly logger = new Logger(PermissionsGuard.name);

  constructor(
    private reflector: Reflector,
    private permissionService: PermissionService,
  ) {}

  /**
   * 检查用户是否具有所需权限
   * @param context 执行上下文
   * @returns 是否具有权限
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 获取方法和类上定义的权限要求
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(PERMISSIONS_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // 如果没有定义权限要求，则允许访问
    if (!requiredPermissions || requiredPermissions.length === 0)  {
      return true;
    // 获取请求对象
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // 如果用户未认证，则拒绝访问
    if (!user)  {
      this.logger.warn('未认证的用户尝试访问需要权限的资源');
      throw new ForbiddenException('未认证的用户');
    }

    try {
      // 获取用户角色的权限
      const userPermissions = await this.getUserPermissions(user);

      // 检查用户是否具有所需的所有权限
      const hasAllPermissions = requiredPermissions.every(permission =>
        this.hasPermission(userPermissions, permission),
      );

      if (!hasAllPermissions)  {
        this.logger.warn(
          `用户 ${user.username} (ID: ${user.sub 权限不足，需要权限: [${requiredPermissions.join(', ')}]`,
        );
        throw new ForbiddenException('权限不足');
      }

      this.logger.debug(
        `用户 ${user.username} (ID: ${user.sub 权限验证通过，权限: [${requiredPermissions.join(', ')}]`,
      );

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException)  {
        throw error;
      }

      this.logger.error(`权限验证过程中发生错误: ${error.message}`, error.stack);
      throw new ForbiddenException('权限验证失败');
    }
  }

  /**
   * 获取用户的所有权限
   * @param user 用户信息
   * @returns 用户权限列表
   */
  private async getUserPermissions(user: any): Promise<string[]> {
    const { userType, tenantId } = user;
    const permissions: string[] = [];

    try {
      // 如果用户有角色信息，直接从角色中获取权限
      if (user.roles && Array.isArray(user.roles)) {
        for (const role of user.roles) {
          try {
            const rolePermissions = await this.permissionService.getRolePermissions(
              role.id || role.code,
              userType,
              tenantId ? parseInt(tenantId) : undefined,
            );
            permissions.push(...rolePermissions.map(p => p.code));
          } catch (error) {
            this.logger.warn(`获取角色 ${role.id || role.code} 权限失败: ${error.message}`);
        }
      }

      // 去重权限列表
      return [...new Set(permissions)];
    } catch (error) {
      this.logger.error(`获取用户权限失败: ${error.message}`, error.stack);
      return [];
  }

  /**
   * 检查用户是否具有指定权限
   * @param userPermissions 用户权限列表
   * @param requiredPermission 需要的权限
   * @returns 是否具有权限
   */
  private hasPermission(userPermissions: string[], requiredPermission: string): boolean {
    // 直接匹配权限码
    if (userPermissions.includes(requiredPermission)) {
      return true;
    // 支持通配符权限匹配
    // 例如：system:user:* 可以匹配 system:user:create, system:user:update 等
    return userPermissions.some(permission => {
      if (permission.endsWith('*')) {
        const permissionPrefix = permission.slice(0, -1);
        return requiredPermission.startsWith(permissionPrefix);
      return false;
    });
  }
}
