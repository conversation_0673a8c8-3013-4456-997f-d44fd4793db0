import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

/**
 * 系统用户守卫
 * 用于验证请求是否来自系统用户
 */
@Injectable()
export class SystemUserGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  /**
   * 判断请求是否可以通过认证
   * @param context 执行上下文
   * @returns 是否通过认证
   */
  canActivate(context: ExecutionContext): boolean {
    // 检查是否跳过系统用户验证
    const skipSystemUserCheck = this.reflector.getAllAndOverride<boolean>('skipSystemUserCheck', [
      context.getHandler(),
      context.getClass(),
    ]);

    // 如果跳过验证，则直接通过
    if (skipSystemUserCheck)  {
      return true;
    // 获取请求对象
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // 检查用户是否存在且是系统用户
    if (!user || user.userType !== 'SYSTEM')  {
      throw new UnauthorizedException('只有系统用户才能访问此资源');
    }

    return true;
}
