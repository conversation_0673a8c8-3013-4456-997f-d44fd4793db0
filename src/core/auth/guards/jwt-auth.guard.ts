import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';

/**
 * JWT认证守卫
 * 用于验证请求中的JWT令牌
 */
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super();
  }

  /**
   * 判断请求是否可以通过认证
   * @param context 执行上下文
   * @returns 是否通过认证
   */
  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    // 检查是否是公共路由
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    // 如果是公共路由，则跳过认证
    if (isPublic) {
      return true;
    }

    // 否则执行JWT认证
    return super.canActivate(context);
  }

  /**
   * 处理认证失败的情况
   * @param err 错误信息
   */
  handleRequest(err: any, user: any) {
    if (err || !user) {
      throw new UnauthorizedException('认证失败或Token无效');
    }
    return user;
  }
}
