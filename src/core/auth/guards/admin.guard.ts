import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { ADMIN_ONLY_KEY } from '../decorators/admin-only.decorator';

/**
 * 管理员守卫
 * 用于验证请求是否来自管理员用户
 */
@Injectable()
export class AdminGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  /**
   * 判断请求是否可以通过管理员验证
   * @param context 执行上下文
   * @returns 是否通过验证
   */
  canActivate(context: ExecutionContext): boolean {
    // 检查是否需要管理员权限
    const requireAdmin = this.reflector.getAllAndOverride<boolean>(ADMIN_ONLY_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // 如果不需要管理员权限，则直接通过
    if (!requireAdmin) {
      return true;
    }

    // 获取请求中的用户信息
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // 检查用户是否存在且是管理员
    if (!user) {
      throw new ForbiddenException('未授权访问');
    }

    // 检查用户类型是否为系统用户或者是否有管理员角色
    const isAdmin =
      user.userType === 'SYSTEM' || (user.roles && user.roles.some(role => role.code === 'ADMIN'));

    if (!isAdmin) {
      throw new ForbiddenException('需要管理员权限');
    }

    return true;
  }
}
