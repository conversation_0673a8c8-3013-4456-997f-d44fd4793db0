import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { ROLES_KEY } from '../decorators/roles.decorator';

/**
 * 角色守卫
 * 用于验证用户是否具有访问特定资源的角色
 */
@Injectable()
export class RolesGuard implements CanActivate {
  private readonly logger = new Logger(RolesGuard.name);

  constructor(private reflector: Reflector) {}

  /**
   * 检查用户是否具有所需角色
   * @param context 执行上下文
   * @returns 是否具有角色
   */
  canActivate(context: ExecutionContext): boolean {
    // 获取方法和类上定义的角色要求
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // 如果没有定义角色要求，则允许访问
    if (!requiredRoles || requiredRoles.length === 0)  {
      return true;
    // 获取请求对象
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // 如果用户未认证，则拒绝访问
    if (!user)  {
      this.logger.warn('未认证的用户尝试访问需要角色的资源');
      throw new ForbiddenException('未认证的用户');
    }

    // 获取用户角色
    const userRoles = this.getUserRoles(user);

    // 检查用户是否具有所需的任一角色
    const hasRequiredRole = requiredRoles.some(role => this.hasRole(userRoles, role));

    if (!hasRequiredRole)  {
      this.logger.warn(
        `用户 ${user.username} (ID: ${user.sub 角色不足，需要角色: [${requiredRoles.join(', ')}]，当前角色: [${userRoles.join(', ')}]`,
      );
      throw new ForbiddenException('角色权限不足');
    }

    this.logger.debug(
      `用户 ${user.username} (ID: ${user.sub 角色验证通过，需要角色: [${requiredRoles.join(', ')}]，当前角色: [${userRoles.join(', ')}]`,
    );

    return true;
  /**
   * 获取用户的所有角色
   * @param user 用户信息
   * @returns 用户角色列表
   */
  private getUserRoles(user: any): string[] {
    const roles: string[] = [];

    // 从用户的角色信息中提取角色代码
    if (user.roles && Array.isArray(user.roles)) {
      roles.push(...user.roles.map(role => role.code || role.id));
    }

    // 如果没有具体的角色信息，尝试从用户类型推断默认角色
    if (roles.length === 0)  {
      switch (user.userType) {
        case 'SYSTEM':
          roles.push('SYSTEM_ADMIN');
          break;
        case 'TENANT':
          roles.push('TENANT_USER');
          break;
        default:
          roles.push('GUEST');
          break;
      }
    }

    return roles;
  /**
   * 检查用户是否具有指定角色
   * @param userRoles 用户角色列表
   * @param requiredRole 需要的角色
   * @returns 是否具有角色
   */
  private hasRole(userRoles: string[], requiredRole: string): boolean {
    return userRoles.includes(requiredRole);