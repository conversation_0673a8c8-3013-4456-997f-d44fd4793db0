import { Injectable, Logger, Scope } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

import { LoginDto } from '../dto/login.dto';
import { AuthStrategyFactory } from '../strategies/auth-strategy.factory';

/**
 * 认证服务
 * 统一处理系统用户和租户用户的认证相关操作
 */
@Injectable({ scope: Scope.REQUEST })
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly authStrategyFactory: AuthStrategyFactory,
  ) {}

  /**
   * 用户登录
   * @param loginDto 登录信息
   * @param req 请求对象（可选）
   * @returns 登录结果，包含访问令牌
   */
  async login(loginDto: LoginDto, req?: any) {
    // 使用策略工厂创建合适的认证策略
    // 如果请求中有租户信息，优先使用租户认证策略
    const hasTenantInRequest = req?.tenant?.tenantCode;

    // 记录租户信息
    if (hasTenantInRequest) {
      this.logger.debug(`从请求中检测到租户信息: ${req.tenant.tenantCode}`);
    } else if ((loginDto as any).tenantCode) {
      this.logger.debug(`从登录DTO中检测到租户信息: ${(loginDto as any).tenantCode}`);
    } else {
      this.logger.debug('未检测到租户信息，将使用系统认证策略');
    }

    // 如果请求中有租户信息，或登录DTO中有租户代码，使用租户认证策略
    const strategy = this.authStrategyFactory.createStrategy(loginDto, hasTenantInRequest);

    // 记录选择的认证策略
    this.logger.debug(`选择的认证策略: ${strategy.constructor.name}`);

    // 如果请求中有租户信息，将其添加到登录DTO中
    if (hasTenantInRequest && !(loginDto as any).tenantCode) {
      this.logger.debug(`将请求中的租户代码 ${req.tenant.tenantCode} 添加到登录DTO中`);
      (loginDto as any).tenantCode = req.tenant.tenantCode;
    }

    return (strategy as any).login(loginDto);
  }

  /**
   * 刷新访问令牌
   * @param user 用户信息
   * @returns 新的访问令牌
   */
  async refreshToken(user: any) {
    // 使用策略工厂创建合适的认证策略
    const strategy = this.authStrategyFactory.createStrategyByUserType(
      user.userType,
      user.tenantId, // tenantId from user
    );
    return (strategy as any).refreshToken(user.userId, user.tenantId);
  }

  /**
   * 获取用户信息
   * @param user 用户信息
   * @returns 用户详细信息
   */
  async getUserInfo(user: any) {
    // 使用策略工厂创建合适的认证策略
    const strategy = this.authStrategyFactory.createStrategyByUserType(
      user.userType,
      user.tenantId, // tenantId from user
    );
    return (strategy as any).getUserInfo(user.userId, user.tenantId);
  }

  /**
   * 获取用户权限码
   * @param user 用户信息
   * @returns 权限码列表
   */
  async getPermissionCodes(user: any) {
    // 使用策略工厂创建合适的认证策略
    const strategy = this.authStrategyFactory.createStrategyByUserType(
      user.userType,
      user.tenantId, // tenantId from user
    );
    return (strategy as any).getPermissionCodes(user.userId, user.tenantId);
  }

  /**
   * 获取用户角色编码
   * @param user 用户信息
   * @returns 角色编码列表
   */
  async getRoleCodes(user: any) {
    // 使用策略工厂创建合适的认证策略
    const strategy = this.authStrategyFactory.createStrategyByUserType(
      user.userType,
      user.tenantId, // tenantId from user
    );
    return (strategy as any).getRoleCodes(user.userId, user.tenantId);
  }
}
