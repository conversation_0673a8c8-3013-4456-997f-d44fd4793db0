import { SetMetadata } from '@nestjs/common';

export const PERMISSIONS_KEY = 'permissions';

/**
 * 权限装饰器
 * 用于标记方法或类需要的权限
 * @param permissions 权限代码列表
 */
export const Permissions = (...permissions: string[]) => SetMetadata(PERMISSIONS_KEY, permissions);

/**
 * 需要任一权限装饰器
 * 用户只需要具有其中一个权限即可访问
 * @param permissions 权限代码列表
 */
export const RequireAnyPermission = (...permissions: string[]) =>
  SetMetadata('require_any_permission', permissions);

/**
 * 需要所有权限装饰器
 * 用户需要具有所有权限才能访问
 * @param permissions 权限代码列表
 */
export const RequireAllPermissions = (...permissions: string[]) =>
  SetMetadata('require_all_permissions', permissions);
