import { ApiProperty } from '@nestjs/swagger';

/**
 * 用户信息响应DTO
 */
export class UserInfoDto {
  @ApiProperty({ description: '用户ID' })
  userId: string;

  @ApiProperty({ description: '用户名' })
  username: string;

  @ApiProperty({ description: '用户真实姓名' })
  realName: string;

  @ApiProperty({ description: '头像URL', required: false })
  avatar?: string;

  @ApiProperty({ description: '用户描述', required: false })
  desc?: string;

  @ApiProperty({ description: '用户首页路径', required: false })
  homePath?: string;

  @ApiProperty({ description: '用户角色列表', type: [String] })
  roles: string[];
}
