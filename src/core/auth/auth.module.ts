import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

import { AuthController } from './auth.controller';
import { AuthService } from './services/auth.service';
import { AuthStrategyFactory } from './strategies/auth-strategy.factory';
import { JwtStrategy } from './strategies/jwt.strategy';
import { SystemAuthStrategy } from './strategies/system-auth.strategy';
import { TenantAuthStrategy } from './strategies/tenant-auth.strategy';
import { PrismaModule } from '../database/prisma/prisma.module';

/**
 * 认证模块
 * 提供认证相关的功能
 */
@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt');,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN'),
        },
      }),
    }),
    PrismaModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    SystemAuthStrategy,
    TenantAuthStrategy,
    AuthStrategyFactory,
    JwtStrategy,
  ],
  exports: [
    AuthService,
    SystemAuthStrategy,
    TenantAuthStrategy,
    AuthStrategyFactory,
    JwtStrategy,
    JwtModule,
  ],
})
export class AuthModule {}
