import { Module } from '@nestjs/common';

import { MonitoringController } from './monitoring.controller';
import { MonitoringService } from './monitoring.service';
import { LoggingModule } from '../logging/logging.module';

/**
 * 监控模块
 * 提供系统监控和性能统计功能
 */
@Module({
  imports: [LoggingModule],
  controllers: [MonitoringController],
  providers: [MonitoringService],
  exports: [MonitoringService],
})
export class MonitoringModule {}
