import { ApiProperty } from '@nestjs/swagger';

/**
 * 通用API响应DTO
 */
export class ApiResponseDto<T = any> {
  @ApiProperty({
    description: '状态码，0表示成功，其他值表示失败',
    example: 0,
    enum: [0, -1, 400, 401, 403, 404, 500],
  )
  code: number;

  @ApiProperty({
    description: '响应消息',
    example: '操作成功',
  )
  message: string;

  @ApiProperty({
    description: '响应数据',
    required: false,
  )
  data?: T;
}

/**
 * 成功响应DTO
 */
export class SuccessResponseDto<T = any> {
  @ApiProperty({
    description: '状态码，固定为0',
    example: 0,
  )
  code: 0;

  @ApiProperty({
    description: '成功消息',
    example: '操作成功',
  )
  message: string;

  @ApiProperty({
    description: '响应数据',
  )
  data: T;
}

/**
 * 分页响应DTO
 */
export class PaginationResponseDto<T = any> {
  @ApiProperty({
    description: '状态码，固定为0',
    example: 0,
  )
  code: 0;

  @ApiProperty({
    description: '成功消息',
    example: '操作成功',
  )
  message: string;

  @ApiProperty({
    description: '分页数据',
  )
  data: {
    items: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

/**
 * 错误响应DTO
 */
export class ErrorResponseDto {
  @ApiProperty({
    description: '错误状态码',
    example: -1,
    enum: [-1, 400, 401, 403, 404, 500],
  )
  code: number;

  @ApiProperty({
    description: '错误消息',
    example: '操作失败',
  )
  message: string;

  @ApiProperty({
    description: '错误详情',
    required: false,
    example: null,
  )
  data?: any;
}

/**
 * 列表响应DTO
 */
export class ListResponseDto<T = any> {
  @ApiProperty({
    description: '状态码，固定为0',
    example: 0,
  )
  code: 0;

  @ApiProperty({
    description: '成功消息',
    example: '操作成功',
  )
  message: string;

  @ApiProperty({
    description: '列表数据',
    type: 'array',
  )
  data: T[];
}
