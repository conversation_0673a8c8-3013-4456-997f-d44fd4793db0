/**
 * 数据模型常量定义
 * 统一管理实体状态、枚举值、默认值等
 */

// ================================
// 通用状态枚举
// ================================

/**
 * 通用启用状态
 */
export enum CommonStatus {
  DISABLED = 0,
  ENABLED = 1,
}

/**
 * 发布状态
 */
export enum PublishStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

/**
 * 网站状态
 */
export enum WebsiteStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  MAINTENANCE = 'maintenance',
  ARCHIVED = 'archived',
}

/**
 * 表单提交状态
 */
export enum FormSubmissionStatus {
  PENDING = 'pending',
  READ = 'read',
  REPLIED = 'replied',
  ARCHIVED = 'archived',
}

/**
 * 通知类型
 */
export enum NotificationType {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  SUCCESS = 'success',
}

/**
 * 用户类型
 */
export enum UserType {
  SYSTEM = 'SYSTEM',
  TENANT = 'TENANT',
}

/**
 * 网站类型
 */
export enum WebsiteType {
  TENANT = 'tenant',
  SYSTEM = 'system',
  REGISTRATION = 'registration',
  LANDING = 'landing',
}

// ================================
// 组件和模板常量
// ================================

/**
 * 组件类型
 */
export enum ComponentType {
  BASIC = 'basic',
  LAYOUT = 'layout',
  FORM = 'form',
  MEDIA = 'media',
  NAVIGATION = 'navigation',
  ECOMMERCE = 'ecommerce',
}

/**
 * 组件分类
 */
export enum ComponentCategory {
  HEADER = 'header',
  FOOTER = 'footer',
  HERO = 'hero',
  CONTENT = 'content',
  SIDEBAR = 'sidebar',
  BUTTON = 'button',
  FORM = 'form',
}

/**
 * 模板分类
 */
export enum TemplateCategory {
  BUSINESS = 'business',
  PORTFOLIO = 'portfolio',
  BLOG = 'blog',
  ECOMMERCE = 'ecommerce',
}

/**
 * 行业分类
 */
export enum IndustryCategory {
  TECH = 'tech',
  RETAIL = 'retail',
  HEALTHCARE = 'healthcare',
  EDUCATION = 'education',
  FINANCE = 'finance',
  REAL_ESTATE = 'real_estate',
}

// ================================
// SEO相关常量
// ================================

/**
 * Open Graph类型
 */
export enum OgType {
  WEBSITE = 'website',
  ARTICLE = 'article',
  PROFILE = 'profile',
  BOOK = 'book',
  VIDEO = 'video',
  MUSIC = 'music',
}

/**
 * Twitter卡片类型
 */
export enum TwitterCardType {
  SUMMARY = 'summary',
  SUMMARY_LARGE_IMAGE = 'summary_large_image',
  APP = 'app',
  PLAYER = 'player',
}

// ================================
// 默认值常量
// ================================

/**
 * 分页默认值
 */
export const PAGINATION_DEFAULTS = {
  PAGE: 1,
  LIMIT: 10,
  MAX_LIMIT: 100,
  SORT_BY: 'createTime',
  SORT_ORDER: 'desc' as const,
};

/**
 * SEO默认值
 */
export const SEO_DEFAULTS = {
  ROBOTS: 'index,follow',
  OG_TYPE: OgType.WEBSITE,
  TWITTER_CARD: TwitterCardType.SUMMARY,
};

/**
 * 组件默认值
 */
export const COMPONENT_DEFAULTS = {
  TYPE: ComponentType.BASIC,
  CATEGORY: ComponentCategory.CONTENT,
  SORT_ORDER: 0,
  VERSION: '1.0.0',
};

/**
 * 模板默认值
 */
export const TEMPLATE_DEFAULTS = {
  IS_PREMIUM: false,
  IS_ACTIVE: true,
  SORT_ORDER: 0,
};

/**
 * 网站默认值
 */
export const WEBSITE_DEFAULTS = {
  STATUS: WebsiteStatus.DRAFT,
  TYPE: WebsiteType.TENANT,
};

// ================================
// 验证常量
// ================================

/**
 * 字段长度限制
 */
export const FIELD_LIMITS = {
  SHORT_TEXT: 50,
  MEDIUM_TEXT: 100,
  LONG_TEXT: 500,
  DESCRIPTION: 1000,
  URL: 2048,
  EMAIL: 254,
  PHONE: 20,
  PASSWORD_MIN: 6,
  PASSWORD_MAX: 128,
};

/**
 * 数值限制
 */
export const NUMERIC_LIMITS = {
  SORT_ORDER_MIN: 0,
  SORT_ORDER_MAX: 9999,
  QUOTA_MIN: 0,
  QUOTA_MAX: 999999,
  PRICE_MIN: 0,
  PRICE_MAX: 999999.99,
};

/**
 * 文件大小限制（字节）
 */
export const FILE_SIZE_LIMITS = {
  IMAGE_MAX: 5 * 1024 * 1024, // 5MB
  DOCUMENT_MAX: 10 * 1024 * 1024, // 10MB
  AVATAR_MAX: 2 * 1024 * 1024, // 2MB
};

/**
 * 支持的文件类型
 */
export const ALLOWED_FILE_TYPES = {
  IMAGES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  DOCUMENTS: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ],
  AVATARS: ['image/jpeg', 'image/png'],
};
