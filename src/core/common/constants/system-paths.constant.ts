/**
 * 系统路径常量
 * 这些路径不需要租户信息
 */
export const SYSTEM_PATHS = [
  // 认证相关
  '/auth/login',
  '/api/auth/login', // 登录
  '/user/info',
  '/api/user/info', // 用户信息
  '/auth/codes',
  '/api/auth/codes', // 权限码
  '/auth/refresh',
  '/api/auth/refresh', // 刷新令牌
  '/auth/logout',
  '/api/auth/logout', // 退出登录

  // 系统管理相关
  '/system/menu',
  '/api/system/menu', // 系统菜单
  '/system/role',
  '/api/system/role', // 系统角色
  '/system/user',
  '/api/system/user', // 系统用户
  '/system/permission',
  '/api/system/permission', // 系统权限

  // 系统菜单相关
  '/system/menu/list',
  '/api/system/menu/list', // 系统菜单列表
  '/system/menu/tree',
  '/api/system/menu/tree', // 系统菜单树

  // 系统角色相关
  '/system/role/list',
  '/api/system/role/list', // 系统角色列表
  '/system/role/assign',
  '/api/system/role/assign', // 分配角色给用户
  '/system/role/users',
  '/api/system/role/users', // 用户角色关联
  '/system/permission/roles',
  '/api/system/permission/roles', // 角色权限

  // 系统用户相关
  '/system/user/list',
  '/api/system/user/list', // 系统用户列表

  // 系统权限相关
  '/system/permission/list',
  '/api/system/permission/list', // 系统权限列表

  // 系统部门相关
  '/system/dept/list',
  '/api/system/dept/list', // 系统部门列表
  '/system/dept/tree',
  '/api/system/dept/tree', // 系统部门树

  // 租户功能相关
  '/api/tenant/features/my-features', // 获取当前租户功能
  '/api/tenant/features/config', // 获取功能配置
];

/**
 * 检查路径是否是系统路径
 * @param url 请求路径
 * @returns 是否是系统路径
 */
export function isSystemPath(url: string): boolean {
  // 更精确的路径匹配
  // 1. 移除查询参数
  const urlWithoutQuery = url.split('?')[0];

  // 2. 检查是否完全匹配或者是子路径
  return SYSTEM_PATHS.some(path => {
    // 完全匹配
    if (urlWithoutQuery === path)  {
      return true;
    // 检查是否是子路径（例如 /api/tenant/features/config/123）
    if (path.endsWith('/config') && urlWithoutQuery.startsWith(path + '/')) {
      return true;
    return false;
  });
}
