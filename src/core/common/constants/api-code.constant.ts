/**
 * API 响应状态码常量
 */
export enum ApiCode {
  /**
   * 成功
   */
  SUCCESS = 0,

  /**
   * 通用错误
   */
  ERROR = -1,

  /**
   * 参数错误
   */
  PARAM_ERROR = 400,

  /**
   * 未授权
   */
  UNAUTHORIZED = 401,

  /**
   * 禁止访问
   */
  FORBIDDEN = 403,

  /**
   * 资源不存在
   */
  NOT_FOUND = 404,

  /**
   * 请求方法不允许
   */
  METHOD_NOT_ALLOWED = 405,

  /**
   * 请求超时
   */
  REQUEST_TIMEOUT = 408,

  /**
   * 服务器内部错误
   */
  INTERNAL_SERVER_ERROR = 500,

  /**
   * 服务不可用
   */
  SERVICE_UNAVAILABLE = 503,

  /**
   * 网关超时
   */
  GATEWAY_TIMEOUT = 504,

  /**
   * 通用业务错误
   */
  BUSINESS_ERROR = 10000,

  /**
   * 资源不存在
   */
  RESOURCE_NOT_FOUND = 10001,

  /**
   * 资源已存在
   */
  RESOURCE_EXISTS = 10002,

  /**
   * 数据验证错误
   */
  VALIDATION_ERROR = 10003,

  /**
   * 业务错误 - 用户相关
   */
  USER_NOT_FOUND = 1001,
  USER_ALREADY_EXISTS = 1002,
  USER_PASSWORD_ERROR = 1003,
  USER_ACCOUNT_DISABLED = 1004,

  /**
   * 业务错误 - 租户相关
   */
  TENANT_NOT_FOUND = 2001,
  TENANT_ALREADY_EXISTS = 2002,
  TENANT_DISABLED = 2003,
  TENANT_CODE_EXISTS = 2004,

  /**
   * 业务错误 - 权限相关
   */
  PERMISSION_DENIED = 3001,
  ROLE_NOT_FOUND = 3002,
  ROLE_ALREADY_EXISTS = 3003,
  ROLE_CODE_EXISTS = 3004,
  USER_ROLE_NOT_FOUND = 3005,

  /**
   * 业务错误 - 菜单相关
   */
  MENU_NOT_FOUND = 4001,
  MENU_ALREADY_EXISTS = 4002,
  MENU_HAS_CHILDREN = 4003,
}

/**
 * API 响应消息常量
 */
export const ApiMessage = {
  [ApiCode.SUCCESS]: '操作成功',
  [ApiCode.ERROR]: '操作失败',
  [ApiCode.PARAM_ERROR]: '参数错误',
  [ApiCode.UNAUTHORIZED]: '未授权',
  [ApiCode.FORBIDDEN]: '禁止访问',
  [ApiCode.NOT_FOUND]: '资源不存在',
  [ApiCode.METHOD_NOT_ALLOWED]: '请求方法不允许',
  [ApiCode.REQUEST_TIMEOUT]: '请求超时',
  [ApiCode.INTERNAL_SERVER_ERROR]: '服务器内部错误',
  [ApiCode.SERVICE_UNAVAILABLE]: '服务不可用',
  [ApiCode.GATEWAY_TIMEOUT]: '网关超时',
  [ApiCode.BUSINESS_ERROR]: '业务处理失败',
  [ApiCode.RESOURCE_NOT_FOUND]: '请求的资源不存在',
  [ApiCode.RESOURCE_EXISTS]: '资源已存在',
  [ApiCode.VALIDATION_ERROR]: '数据验证失败',
  [ApiCode.USER_NOT_FOUND]: '用户不存在',
  [ApiCode.USER_ALREADY_EXISTS]: '用户已存在',
  [ApiCode.USER_PASSWORD_ERROR]: '用户名或密码错误',
  [ApiCode.USER_ACCOUNT_DISABLED]: '用户账号已禁用',
  [ApiCode.TENANT_NOT_FOUND]: '租户不存在',
  [ApiCode.TENANT_ALREADY_EXISTS]: '租户已存在',
  [ApiCode.TENANT_DISABLED]: '租户已禁用',
  [ApiCode.TENANT_CODE_EXISTS]: '租户代码已存在',
  [ApiCode.PERMISSION_DENIED]: '权限不足',
  [ApiCode.ROLE_NOT_FOUND]: '角色不存在',
  [ApiCode.ROLE_ALREADY_EXISTS]: '角色已存在',
  [ApiCode.ROLE_CODE_EXISTS]: '角色编码已存在',
  [ApiCode.USER_ROLE_NOT_FOUND]: '用户角色关联不存在',
  [ApiCode.MENU_NOT_FOUND]: '菜单不存在',
  [ApiCode.MENU_ALREADY_EXISTS]: '菜单已存在',
  [ApiCode.MENU_HAS_CHILDREN]: '菜单含有子菜单，无法删除',
};
