/**
 * 功能代码常量
 *
 * 功能代码采用层级结构：[模块].[子功能]
 * 例如：ai.ppt, payment, tenant-config.email
 */

// AI模块功能
export const AI_FEATURES = {
  PPT: 'ai.ppt',
  DOCUMENT: 'ai.document',
  CHAT: 'ai.chat',
  IMAGE: 'ai.image',
};

// 支付模块功能
export const PAYMENT_FEATURES = {
  BASIC: 'payment',
  REFUND: 'payment.refund',
  SUBSCRIPTION: 'payment.subscription',
};

// 会员模块功能
export const MEMBERSHIP_FEATURES = {
  BASIC: 'membership',
  UPGRADE: 'membership.upgrade',
  POINTS: 'membership.points',
};

// 租户配置模块功能
export const TENANT_CONFIG_FEATURES = {
  EMAIL: 'tenant-config.email',
  SMS: 'tenant-config.sms',
  OSS: 'tenant-config.oss',
  PAYMENT: 'tenant-config.payment',
};

// 所有功能代码的平面列表
export const ALL_FEATURE_CODES = [
  ...Object.values(AI_FEATURES),
  ...Object.values(PAYMENT_FEATURES),
  ...Object.values(MEMBERSHIP_FEATURES),
  ...Object.values(TENANT_CONFIG_FEATURES),
];

// 功能代码描述映射
export const FEATURE_DESCRIPTIONS: Record<string, string> = {
  // AI模块
  [AI_FEATURES.PPT]: 'AI PPT生成功能',
  [AI_FEATURES.DOCUMENT]: 'AI文档生成功能',
  [AI_FEATURES.CHAT]: 'AI聊天功能',
  [AI_FEATURES.IMAGE]: 'AI图像生成功能',

  // 支付模块
  [PAYMENT_FEATURES.BASIC]: '基础支付功能',
  [PAYMENT_FEATURES.REFUND]: '退款功能',
  [PAYMENT_FEATURES.SUBSCRIPTION]: '订阅支付功能',

  // 会员模块
  [MEMBERSHIP_FEATURES.BASIC]: '基础会员功能',
  [MEMBERSHIP_FEATURES.UPGRADE]: '会员升级功能',
  [MEMBERSHIP_FEATURES.POINTS]: '会员积分功能',

  // 租户配置模块
  [TENANT_CONFIG_FEATURES.EMAIL]: '租户邮件配置',
  [TENANT_CONFIG_FEATURES.SMS]: '租户短信配置',
  [TENANT_CONFIG_FEATURES.OSS]: '租户对象存储配置',
  [TENANT_CONFIG_FEATURES.PAYMENT]: '租户支付配置',
};

// 功能代码与模块映射
export const FEATURE_MODULES: Record<string, string> = {
  // AI模块
  [AI_FEATURES.PPT]: 'AI模块',
  [AI_FEATURES.DOCUMENT]: 'AI模块',
  [AI_FEATURES.CHAT]: 'AI模块',
  [AI_FEATURES.IMAGE]: 'AI模块',

  // 支付模块
  [PAYMENT_FEATURES.BASIC]: '支付模块',
  [PAYMENT_FEATURES.REFUND]: '支付模块',
  [PAYMENT_FEATURES.SUBSCRIPTION]: '支付模块',

  // 会员模块
  [MEMBERSHIP_FEATURES.BASIC]: '会员模块',
  [MEMBERSHIP_FEATURES.UPGRADE]: '会员模块',
  [MEMBERSHIP_FEATURES.POINTS]: '会员模块',

  // 租户配置模块
  [TENANT_CONFIG_FEATURES.EMAIL]: '租户配置模块',
  [TENANT_CONFIG_FEATURES.SMS]: '租户配置模块',
  [TENANT_CONFIG_FEATURES.OSS]: '租户配置模块',
  [TENANT_CONFIG_FEATURES.PAYMENT]: '租户配置模块',
};
