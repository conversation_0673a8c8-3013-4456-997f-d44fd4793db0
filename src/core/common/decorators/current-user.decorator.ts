import { createParamDecorator, ExecutionContext } from '@nestjs/common';

import { IRequestWithProps } from '../interfaces/request-with-props.interface';

/**
 * 当前用户装饰器
 * 用于从请求中获取当前用户信息
 *
 * 使用示例：
 * ```typescript
 * @Get('profile')
 * getProfile(@CurrentUser() user: any) {
 *   return user;
 * }
 * ```
 */
export const CurrentUser = createParamDecorator(
  (data: string | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<IRequestWithProps>();
    const user = request.user;

    // 如果指定了数据属性，则返回该属性的值
    return data ? user?.[data] : user;
  },
);
