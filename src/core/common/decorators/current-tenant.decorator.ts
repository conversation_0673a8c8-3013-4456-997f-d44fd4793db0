import { createParamDecorator, ExecutionContext } from '@nestjs/common';

import { IRequestWithProps } from '../interfaces/request-with-props.interface';

/**
 * 当前租户装饰器
 * 用于从请求中获取当前租户信息
 *
 * 使用示例：
 * ```typescript
 * @Get('tenant-info')
 * getTenantInfo(@CurrentTenant() tenant: any) {
 *   return tenant;
 * }
 * ```
 */
export const CurrentTenant = createParamDecorator(
  (data: string | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<IRequestWithProps>();
    const tenant = request.tenant;

    // 如果指定了数据属性，则返回该属性的值
    return data ? tenant?.[data] : tenant;
  },
);
