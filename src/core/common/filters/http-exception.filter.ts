import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';

import { ApiCode, ApiMessage } from '../constants/api-code.constant';
import { BusinessException } from '../exceptions/business.exception';
import { ValidationException } from '../exceptions/validation.exception';

/**
 * 全局HTTP异常过滤器
 * 捕获所有异常并返回统一的错误响应格式
 */
@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // 获取异常状态码和消息
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let apiCode = ApiCode.ERROR;
    let message = '服务器内部错误';
    let errors = null;

    // 处理业务异常
    if (exception instanceof BusinessException)  {
      const businessException = exception as BusinessException;
      status = exception.getStatus();
      apiCode = businessException.getErrorCode();

      // 获取异常响应
      const exceptionResponse = exception.getResponse() as any;
      message = exceptionResponse.message || ApiMessage[apiCode];

      // 处理验证异常
      if (exception instanceof ValidationException)  {
        errors = (exception as ValidationException).getErrors();
      }
    }
    // 处理HTTP异常
    else if (exception instanceof HttpException)  {
      status = exception.getStatus();

      // 获取异常响应
      const exceptionResponse = exception.getResponse() as any;

      // 处理验证管道异常
      if (status === HttpStatus.BAD_REQUEST && Array.isArray(exceptionResponse.message)) {
        apiCode = ApiCode.VALIDATION_ERROR;
        message = '数据验证失败';
        errors = exceptionResponse.message;
      } else {
        message = exceptionResponse.message || exception.message;

        // 根据HTTP状态码映射到API状态码
        switch (status) {
          case HttpStatus.BAD_REQUEST:
            apiCode = ApiCode.PARAM_ERROR;
            break;
          case HttpStatus.UNAUTHORIZED:
            apiCode = ApiCode.UNAUTHORIZED;
            break;
          case HttpStatus.FORBIDDEN:
            apiCode = ApiCode.FORBIDDEN;
            break;
          case HttpStatus.NOT_FOUND:
            apiCode = ApiCode.NOT_FOUND;
            break;
          case HttpStatus.METHOD_NOT_ALLOWED:
            apiCode = ApiCode.METHOD_NOT_ALLOWED;
            break;
          case HttpStatus.REQUEST_TIMEOUT:
            apiCode = ApiCode.REQUEST_TIMEOUT;
            break;
          case HttpStatus.INTERNAL_SERVER_ERROR:
            apiCode = ApiCode.INTERNAL_SERVER_ERROR;
            break;
          case HttpStatus.SERVICE_UNAVAILABLE:
            apiCode = ApiCode.SERVICE_UNAVAILABLE;
            break;
          case HttpStatus.GATEWAY_TIMEOUT:
            apiCode = ApiCode.GATEWAY_TIMEOUT;
            break;
          default:
            apiCode = ApiCode.ERROR;
            break;
        }
      }
    }

    // 记录错误日志
    this.logger.error(`${request.method} ${request.url} - ${status}: ${message}`, exception.stack);

    // 返回统一格式的错误响应
    const responseBody = {
      code: apiCode,
      data: null,
      message: message || ApiMessage[apiCode],
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    // 添加验证错误信息
    if (errors)  {
      responseBody['errors'] = errors;
    }

    response.status(status).json(responseBody);
  }
}
