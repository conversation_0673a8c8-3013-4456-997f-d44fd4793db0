import { Request } from 'express';

/**
 * 扩展的请求接口
 * 包含了额外的属性，如用户信息和租户信息
 */
export interface IRequestWithProps extends Request {
  /**
   * 认证信息
   */
  auth?: any;

  /**
   * 用户信息
   */
  user?: {
    /**
     * 用户ID
     */
    userId?: string;

    /**
     * 用户名
     */
    username?: string;

    /**
     * 用户类型
     * SYSTEM: 系统用户
     * TENANT: 租户用户
     */
    userType?: string;

    /**
     * 租户ID
     * 仅当用户类型为TENANT时有效
     */
    tenantId?: string;

    /**
     * 用户角色
     */
    role?: string;
  };

  /**
   * API密钥
   */
  apiKey?: string;

  /**
   * 租户信息
   */
  tenant?: {
    /**
     * 租户ID - 用于数据库关系和内部操作
     */
    tenantId?: number;

    /**
     * 租户代码 - 用于识别租户和日志
     */
    tenantCode?: string;

    /**
     * 数据源URL
     */
    datasourceUrl?: string;
  };
}
