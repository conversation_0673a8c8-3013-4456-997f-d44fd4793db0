/**
 * API响应接口
 * 定义了API响应的标准格式
 */
export interface ApiResponse<T = any> {
  /**
   * 状态码
   * 0表示成功，其他值表示失败
   */
  code: number;

  /**
   * 响应消息
   */
  message: string;

  /**
   * 响应数据
   */
  data?: T;

  /**
   * 元数据
   * 可以包含分页信息等
   */
  meta?: Record<string, any>;
}

/**
 * 分页元数据接口
 * 定义了分页响应的元数据格式
 */
export interface PaginationMeta {
  /**
   * 当前页码
   */
  page: number;

  /**
   * 每页条数
   */
  pageSize: number;

  /**
   * 总条数
   */
  total: number;

  /**
   * 总页数
   */
  totalPages: number;
}

/**
 * 分页查询参数接口
 * 定义了分页查询的参数格式
 */
export interface PaginationParams {
  /**
   * 当前页码
   */
  page?: number;

  /**
   * 每页条数
   */
  pageSize?: number;

  /**
   * 排序字段
   */
  sortBy?: string;

  /**
   * 排序方向
   * asc: 升序
   * desc: 降序
   */
  sortOrder?: 'asc' | 'desc';
}
