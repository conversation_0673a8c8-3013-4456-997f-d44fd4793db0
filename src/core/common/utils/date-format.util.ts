/**
 * 日期格式化工具类
 */
export class DateFormatUtil {
  /**
   * 格式化日期为年月日时分秒格式
   * @param date 日期对象或日期字符串
   * @returns 格式化后的日期字符串，格式：YYYY-MM-DD HH:mm:ss
   */
  static formatToDateTime(date: Date | string | null | undefined): string {
    if (!date) return '';

    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // 获取年月日时分秒
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    const seconds = String(dateObj.getSeconds()).padStart(2, '0');

    // 格式化为 YYYY-MM-DD HH:mm:ss
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 格式化日期为年月日格式
   * @param date 日期对象或日期字符串
   * @returns 格式化后的日期字符串，格式：YYYY-MM-DD
   */
  static formatToDate(date: Date | string | null | undefined): string {
    if (!date) return '';

    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // 获取年月日
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');

    // 格式化为 YYYY-MM-DD
    return `${year}-${month}-${day}`;
  }

  /**
   * 格式化日期为时分秒格式
   * @param date 日期对象或日期字符串
   * @returns 格式化后的时间字符串，格式：HH:mm:ss
   */
  static formatToTime(date: Date | string | null | undefined): string {
    if (!date) return '';

    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // 获取时分秒
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    const seconds = String(dateObj.getSeconds()).padStart(2, '0');

    // 格式化为 HH:mm:ss
    return `${hours}:${minutes}:${seconds}`;
  }
}
