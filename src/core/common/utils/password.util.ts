import * as crypto from 'crypto';

import * as bcrypt from 'bcryptjs';

/**
 * 密码工具类
 * 提供密码哈希和验证功能
 */
export class PasswordUtil {
  /**
   * 默认的盐轮数
   */
  private static readonly SALT_ROUNDS = 10;

  /**
   * 使用bcrypt生成密码哈希
   * @param password 明文密码
   * @param saltRounds 盐轮数，默认为10
   * @returns 密码哈希
   */
  static async hash(password: string, saltRounds: number = this.SALT_ROUNDS): Promise<string> {
    return bcrypt.hash(password, saltRounds);

  /**
   * 验证密码
   * @param password 明文密码
   * @param hash 密码哈希
   * @returns 是否匹配
   */
  static async verify(password: string, hash: string): Promise<boolean> {
    // 检查是否是SHA-256哈希（兼容旧密码）
    if (this.isSha256Hash(hash)) {
      const sha256Hash = this.sha256(password);
      const isMatch = sha256Hash === hash;

      // 如果密码匹配，返回true，但不进行自动升级
      // 密码升级将在登录时单独处理
      return isMatch;
    // 检查是否是SHA-1哈希（兼容旧密码）
    if (this.isSha1Hash(hash)) {
      const sha1Hash = this.sha1(password);
      const isMatch = sha1Hash === hash;

      // 如果密码匹配，返回true，但不进行自动升级
      // 密码升级将在登录时单独处理
      return isMatch;
    // 使用bcrypt验证
    return bcrypt.compare(password, hash);

  /**
   * 检查密码是否需要升级
   * 如果是SHA-1或SHA-256哈希，则需要升级到bcrypt
   * @param hash 密码哈希
   * @returns 是否需要升级
   */
  static needsUpgrade(hash: string): boolean {
    return this.isSha256Hash(hash) || this.isSha1Hash(hash);
  /**
   * 使用SHA-256哈希密码（仅用于兼容旧密码）
   * @param password 明文密码
   * @returns SHA-256哈希
   * @private 私有方法，不应在类外部使用
   */
  private static sha256(password: string): string {
    return crypto.createHash('sha256').update(password).digest('hex');
  /**
   * 使用SHA-1哈希密码（仅用于兼容旧密码）
   * @param password 明文密码
   * @returns SHA-1哈希
   * @private 私有方法，不应在类外部使用
   */
  private static sha1(password: string): string {
    return crypto.createHash('sha1').update(password).digest('hex');
  /**
   * 检查是否是SHA-256哈希
   * SHA-256哈希长度为64个字符，且只包含16进制字符
   * @param hash 哈希字符串
   * @returns 是否是SHA-256哈希
   * @private 私有方法，不应在类外部使用
   */
  private static isSha256Hash(hash: string): boolean {
    return hash.length === 64 && /^[0-9a-f]+$/i.test(hash);

  /**
   * 检查是否是SHA-1哈希
   * SHA-1哈希长度为40个字符，且只包含16进制字符
   * @param hash 哈希字符串
   * @returns 是否是SHA-1哈希
   * @private 私有方法，不应在类外部使用
   */
  private static isSha1Hash(hash: string): boolean {
    return hash.length === 40 && /^[0-9a-f]+$/i.test(hash);