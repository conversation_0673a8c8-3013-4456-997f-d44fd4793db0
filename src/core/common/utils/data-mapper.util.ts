import { JsonValue } from '@prisma/client/runtime/library';

import { DateFormatUtil } from './date-format.util';

/**
 * 数据映射工具类
 * 统一处理Prisma模型到Entity/DTO的字段映射
 */
export class DataMapperUtil {
  /**
   * 映射基础时间字段
   * 将Prisma的createdAt/updatedAt映射为createTime/updateTime
   */
  static mapBaseTimeFields(dbModel: any): {
    createTime: Date;
    updateTime: Date;
  } {
    return {
      createTime: dbModel.createdAt || dbModel.createTime,
      updateTime: dbModel.updatedAt || dbModel.updateTime,
    };
  }

  /**
   * 映射格式化的时间字段
   * 返回格式化的时间字符串
   */
  static mapFormattedTimeFields(dbModel: any): {
    createTime: string;
    updateTime: string;
  } {
    return {
      createTime: DateFormatUtil.formatToDateTime(dbModel.createdAt || dbModel.createTime),
      updateTime: DateFormatUtil.formatToDateTime(dbModel.updatedAt || dbModel.updateTime),
    };
  }

  /**
   * 映射基础实体字段
   * 包含ID和时间字段
   */
  static mapBaseEntity(dbModel: any): {
    id: number;
    createTime: Date;
    updateTime: Date;
  } {
    return {
      id: dbModel.id,
      ...this.mapBaseTimeFields(dbModel),
    };
  }

  /**
   * 映射租户实体字段
   * 包含基础字段和租户ID
   */
  static mapTenantEntity(dbModel: any): {
    id: number;
    tenantId: number;
    createTime: Date;
    updateTime: Date;
  } {
    return {
      ...this.mapBaseEntity(dbModel),
      tenantId: dbModel.tenantId,
    };
  }

  /**
   * 映射可选租户实体字段
   * 租户ID可能为空（支持独立数据库模式）
   */
  static mapOptionalTenantEntity(dbModel: any): {
    id: number;
    tenantId?: number;
    createTime: Date;
    updateTime: Date;
  } {
    return {
      ...this.mapBaseEntity(dbModel),
      ...(dbModel.tenantId && { tenantId: dbModel.tenantId ;,
    };
  }

  /**
   * 映射JSON字段
   * 安全处理JsonValue类型
   */
  static mapJsonField(jsonValue: JsonValue | null | undefined): any {
    if (jsonValue === null || jsonValue === undefined)  {
      return null;
    if (typeof jsonValue === 'string')  {
      try {
        return JSON.parse(jsonValue); catch {
        return jsonValue;
    }

    return jsonValue;
  /**
   * 映射Decimal字段为number
   * 处理Prisma Decimal类型
   */
  static mapDecimalField(decimal: any): number | null {
    if (decimal === null || decimal === undefined)  {
      return null;
    if (typeof decimal === 'number')  {
      return decimal;
    if (typeof decimal === 'string')  {
      return parseFloat(decimal);
    // Prisma Decimal对象
    if (decimal && typeof decimal.toNumber === 'function')  {
      return decimal.toNumber();

    return null;
  /**
   * 清理时间字段映射
   * 移除Prisma原始时间字段，保留映射后的字段
   */
  static cleanTimeFields(obj: any): any {
    const { createdAt, updatedAt, ...rest } = obj;
    return rest;
  /**
   * 构建分页响应
   */
  static buildPagedResponse<T>(
    items: T[],
    total: number,
    page: number,
    limit: number,
  ): {
    items: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } {
    const totalPages = Math.ceil(total / limit);

    return {
      items,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 安全获取嵌套对象属性
   */
  static safeGet(obj: any, path: string, defaultValue: any = null): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : defaultValue;
    }, obj);
  }

  /**
   * 映射枚举字段
   * 确保枚举值的类型安全
   */
  static mapEnumField<T extends string>(value: any, validValues: readonly T[], defaultValue: T): T {
    if (typeof value === 'string' && validValues.includes(value as T)) {
      return value as T;
    return defaultValue;
}
