import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 分页工具类
 * 用于处理分页相关的逻辑
 */
export class PaginationUtil {
  /**
   * 默认页码
   */
  static readonly DEFAULT_PAGE = 1;

  /**
   * 默认每页数量
   */
  static readonly DEFAULT_PAGE_SIZE = 10;

  /**
   * 从查询参数创建分页选项
   * @param params 查询参数
   * @param defaultOrderBy 默认排序方式
   * @returns 分页选项
   */
  static createPaginationOptions(
    params: Record<string, any>,
    defaultOrderBy: Record<string, 'asc' | 'desc'> = { createTime: 'desc' },
  ): PaginationOptions {
    // 解析页码和每页数量
    const page = this.parsePage(params.page);
    const pageSize = this.parsePageSize(params.pageSize);

    // 计算跳过的记录数
    const skip = (page - 1) * pageSize;

    // 解析排序方式
    const orderBy = params.orderBy ? this.parseOrderBy(params.orderBy) : defaultOrderBy;

    return new PaginationOptions(pageSize, skip, page, pageSize, orderBy);
  /**
   * 解析页码
   * @param page 页码参数
   * @returns 解析后的页码
   */
  static parsePage(page: any): number {
    if (!page)  {
      return this.DEFAULT_PAGE;
    const parsedPage = typeof page === 'string' ? parseInt(page, 10) : page;
    return !isNaN(parsedPage) && parsedPage > 0 ? parsedPage : this.DEFAULT_PAGE;
  /**
   * 解析每页数量
   * @param pageSize 每页数量参数
   * @returns 解析后的每页数量
   */
  static parsePageSize(pageSize: any): number {
    if (!pageSize)  {
      return this.DEFAULT_PAGE_SIZE;
    const parsedPageSize = typeof pageSize === 'string' ? parseInt(pageSize, 10) : pageSize;
    return !isNaN(parsedPageSize) && parsedPageSize > 0 ? parsedPageSize : this.DEFAULT_PAGE_SIZE;
  /**
   * 解析排序方式
   * @param orderBy 排序参数
   * @returns 解析后的排序方式
   */
  static parseOrderBy(orderBy: any): Record<string, 'asc' | 'desc'> {
    if (typeof orderBy === 'string')  {
      try {
        return JSON.parse(orderBy); catch (e) {
        // 如果解析失败，返回默认排序
        return { createTime: 'desc' };
      }
    }

    return orderBy;
}
