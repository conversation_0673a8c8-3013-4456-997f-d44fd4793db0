import {
  ArgumentMetadata,
  BadRequestException,
  Injectable,
  PipeTransform,
  Type,
} from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';

import { ApiCode, ApiMessage } from '../constants/api-code.constant';

/**
 * 自定义验证管道
 * 用于验证请求参数是否符合DTO的验证规则
 *
 * 使用示例：
 * ```typescript
 * @Post()
 * create(@Body(new ValidationPipe()) createUserDto: CreateUserDto) {
 *   // ...
 * }
 * ```
 */
@Injectable()
export class ValidationPipe implements PipeTransform<any> {
  /**
   * 转换并验证输入数据
   * @param value 输入数据
   * @param metadata 元数据
   * @returns 转换后的数据
   */
  async transform(value: any, { metatype }: ArgumentMetadata) {
    // 如果没有提供元类型或者元类型是JavaScript的内置类型，则跳过验证
    if (!metatype || !this.toValidate(metatype)) {
      return value;
    // 将普通对象转换为类实例
    const object = plainToInstance(metatype, value);

    // 验证对象
    const errors = await validate(object);

    // 如果有错误，则抛出BadRequestException
    if (errors.length > 0)  {
      // 获取第一个错误的第一个约束消息
      const firstError = errors[0];
      const firstConstraint = Object.values(firstError.constraints || {)[0];

      throw new BadRequestException(firstConstraint || ApiMessage[ApiCode.PARAM_ERROR]);
    }

    return object;
  /**
   * 检查元类型是否需要验证
   * @param metatype 元类型
   * @returns 是否需要验证
   */
  private toValidate(metatype: Type<any>): boolean {
    const types: Type<any>[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);