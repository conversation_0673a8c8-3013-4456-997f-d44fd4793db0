import { ArgumentMetadata, Injectable, PipeTransform } from '@nestjs/common';

/**
 * 解析整数或返回undefined的管道
 * 如果输入是一个有效的整数字符串，则返回对应的整数
 * 如果输入是undefined或空字符串，则返回undefined
 * 如果输入是其他无效值，则返回undefined
 *
 * 使用示例：
 * ```typescript
 * @Get()
 * findAll(@Query('id', ParseIntOrUndefinedPipe) id?: number) {
 *   // ...
 * }
 * ```
 */
@Injectable()
export class ParseIntOrUndefinedPipe implements PipeTransform<string, number | undefined> {
  transform(value: string, metadata: ArgumentMetadata): number | undefined {
    // 如果值为undefined或空字符串，则返回undefined
    if (value === undefined || value === '')  {
      return undefined;
    // 尝试将值转换为整数
    const val = parseInt(value, 10);

    // 如果转换结果是NaN，则返回undefined
    return isNaN(val) ? undefined : val;
}
