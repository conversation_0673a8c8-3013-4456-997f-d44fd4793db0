import { HttpStatus } from '@nestjs/common';

import { BusinessException } from './business.exception';
import { ApiCode } from '../constants/api-code.constant';

/**
 * 资源未找到异常
 * 用于处理请求的资源不存在的情况
 */
export class ResourceNotFoundException extends BusinessException {
  /**
   * 构造函数
   * @param resourceName 资源名称
   * @param resourceId 资源ID
   */
  constructor(resourceName: string, resourceId?: string | number) {
    const message = resourceId
      ? `${resourceName} 不存在，ID: ${resourceId}`
      : `${resourceName} 不存在`;

    super(ApiCode.RESOURCE_NOT_FOUND, message, HttpStatus.NOT_FOUND);
  }
}
