/*
 * @Author: ya<PERSON><PERSON>qi<PERSON> <EMAIL>
 * @Date: 2025-05-09 13:30:33
 * @LastEditors: yangwenqiang <EMAIL>
 * @LastEditTime: 2025-06-01 00:53:10
 * @FilePath: /multi-tenant-nestjs/src/core/common/exceptions/business.exception.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { HttpException, HttpStatus } from '@nestjs/common';

import { ApiCode } from '../constants/api-code.constant';

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 */
export class BusinessException extends HttpException {
  /**
   * 错误代码
   */
  private readonly errorCode: number;

  /**
   * 构造函数
   * @param code 错误代码
   * @param message 错误消息
   * @param status HTTP状态码
   */
  constructor(
    code: number = ApiCode.ERROR,
    message?: string,
    status: HttpStatus = HttpStatus.BAD_REQUEST,
  ) {
    super(
      {
        code,
        message: message || `业务异常，错误代码：${code}`,
        data: null,
      },
      status,
    );
    this.errorCode = code;
  }

  /**
   * 获取错误代码
   * @returns 错误代码
   */
  getErrorCode(): number {
    return this.errorCode;
  }
}
