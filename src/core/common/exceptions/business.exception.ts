import { HttpException, HttpStatus } from '@nestjs/common';

import { ApiCode } from '../constants/api-code.constant';

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 */
export class BusinessException extends HttpException {
  /**
   * 错误代码
   */
  private readonly errorCode: number;

  /**
   * 构造函数
   * @param code 错误代码
   * @param message 错误消息
   * @param status HTTP状态码
   */
  constructor(
    code: number = ApiCode.ERROR,
    message?: string,
    status: HttpStatus = HttpStatus.BAD_REQUEST,
  ) {
    super(
      {
        code,
        message: message || `业务异常，错误代码：${code}`,
        data: null,
      },
      status,
    );
    this.errorCode = code;
  }

  /**
   * 获取错误代码
   * @returns 错误代码
   */
  getErrorCode(): number {
    return this.errorCode;
}
