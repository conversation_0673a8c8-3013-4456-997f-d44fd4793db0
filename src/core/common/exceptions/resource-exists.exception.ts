import { HttpStatus } from '@nestjs/common';

import { BusinessException } from './business.exception';
import { ApiCode } from '../constants/api-code.constant';

/**
 * 资源已存在异常
 * 用于处理创建的资源已存在的情况
 */
export class ResourceExistsException extends BusinessException {
  /**
   * 构造函数
   * @param resourceName 资源名称
   * @param field 字段名称
   * @param value 字段值
   */
  constructor(resourceName: string, field: string, value: string | number) {
    const message = `${resourceName} 已存在，${field}: ${value}`;

    super(ApiCode.RESOURCE_EXISTS, message, HttpStatus.CONFLICT);
  }
}
