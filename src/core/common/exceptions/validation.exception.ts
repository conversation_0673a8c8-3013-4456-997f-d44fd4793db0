import { HttpStatus } from '@nestjs/common';

import { BusinessException } from './business.exception';
import { ApiCode } from '../constants/api-code.constant';

/**
 * 验证异常
 * 用于处理数据验证失败的情况
 */
export class ValidationException extends BusinessException {
  /**
   * 构造函数
   * @param message 错误消息
   * @param errors 验证错误详情
   */
  constructor(
    message: string = '数据验证失败',
    private readonly errors?: any,
  ) {
    super(ApiCode.VALIDATION_ERROR, message, HttpStatus.BAD_REQUEST);
  }

  /**
   * 获取验证错误详情
   * @returns 验证错误详情
   */
  getErrors(): any {
    return this.errors;
}
