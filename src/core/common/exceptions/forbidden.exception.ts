import { HttpStatus } from '@nestjs/common';

import { BusinessException } from './business.exception';
import { ApiCode } from '../constants/api-code.constant';

/**
 * 禁止访问异常
 * 用于处理用户无权限访问资源的情况
 */
export class ForbiddenException extends BusinessException {
  /**
   * 构造函数
   * @param message 错误消息
   */
  constructor(message: string = '无权限访问') {
    super(ApiCode.FORBIDDEN, message, HttpStatus.FORBIDDEN);
  }
}
