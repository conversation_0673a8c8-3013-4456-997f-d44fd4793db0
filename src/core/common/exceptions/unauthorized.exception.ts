import { HttpStatus } from '@nestjs/common';

import { BusinessException } from './business.exception';
import { ApiCode } from '../constants/api-code.constant';

/**
 * 未授权异常
 * 用于处理用户未授权访问资源的情况
 */
export class UnauthorizedException extends BusinessException {
  /**
   * 构造函数
   * @param message 错误消息
   */
  constructor(message: string = '未授权访问') {
    super(ApiCode.UNAUTHORIZED, message, HttpStatus.UNAUTHORIZED);
  }
}
