/*
 * @Author: ya<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-08 14:16:44
 * @LastEditors: yangwenqiang <EMAIL>
 * @LastEditTime: 2025-06-01 00:47:44
 * @FilePath: /multi-tenant-nestjs/src/core/core.module.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Module } from '@nestjs/common';

import { AuthModule } from './auth/auth.module';
import { CommonModule } from './common/common.module';
import { DatabaseModule } from './database/database.module';
import { PrismaModule } from './database/prisma/prisma.module';
import { HealthModule } from './health/health.module';
import { LoggingModule } from './logging/logging.module';
import { MiddlewareModule } from './middleware/middleware.module';
import { MonitoringModule } from './monitoring/monitoring.module';

/**
 * 核心模块
 * 提供应用程序中使用的所有核心功能
 */
@Module({
  imports: [
    AuthModule,
    CommonModule,
    DatabaseModule,
    PrismaModule,
    MiddlewareModule,
    LoggingModule,
    HealthModule,
    MonitoringModule,
  ],
  exports: [
    AuthModule,
    CommonModule,
    DatabaseModule,
    PrismaModule,
    MiddlewareModule,
    LoggingModule,
    HealthModule,
    MonitoringModule,
  ],
})
export class CoreModule {}
