import { Injectable, Type, Scope } from '@nestjs/common';

import { IBaseStrategy } from './base-strategy.interface';

/**
 * 基础策略工厂
 * 用于创建和管理策略实例
 */
@Injectable({ scope: Scope.DEFAULT 
export class BaseStrategyFactory<T extends IBaseStrategy> {
  private strategies: Map<string, T> = new Map();

  /**
   * 注册策略
   * @param strategy 策略实例
   * @param type 策略类型（可选）
   */
  register(strategy: T, type?: string): void {
    console.log(`BaseStrategyFactory.register() 被调用`);

    if (!strategy)  {
      console.error('尝试注册空策略');
      throw new Error('策略不能为空');
    }

    // 如果提供了类型，使用提供的类型；否则，使用策略的getType方法
    const strategyType = type || strategy.getType();
    console.log(`注册策略，类型: ${strategyType}`);

    // 验证策略是否实现了必要的方法
    if (typeof strategy.getType !== 'function')  {
      console.error(`策略 ${strategyType} 不完整，缺少 getType 方法`);
      throw new Error(`策略 ${strategyType} 不完整，缺少 getType 方法`);
    }

    // 注册策略
    this.strategies.set(strategyType, strategy);
    console.log(`策略 ${strategyType} 注册成功`);

    // 验证注册是否成功
    const registeredStrategy = this.strategies.get(strategyType);
    if (registeredStrategy)  {
      console.log(`验证策略 ${strategyType} 注册成功`); else {
      console.error(`策略 ${strategyType} 注册失败`);
      throw new Error(`策略 ${strategyType} 注册失败`);
    }
  }

  /**
   * 获取策略
   * @param type 策略类型
   * @returns 策略实例
   */
  getStrategy(type: string): T | undefined {
    console.log(`BaseStrategyFactory.getStrategy() 被调用，类型: ${type}`);

    // 获取所有已注册的策略
    const allStrategies = this.getAllStrategies();
    console.log(`已注册的策略数量: ${allStrategies.length}`);

    if (allStrategies.length > 0)  {
      console.log(`已注册的策略类型: ${allStrategies.map(s => s.getType()).join(', ')}`);
    } else {
      console.log('没有注册任何策略');

    // 尝试获取策略
    const strategy = this.strategies.get(type);

    if (strategy)  {
      console.log(`成功获取到策略: ${type}`);
      return strategy;
    } else {
      console.log(`未找到类型为 ${type} 的策略`);
      return undefined;
  }

  /**
   * 获取所有策略
   * @returns 所有策略实例
   */
  getAllStrategies(): T[] {
    return Array.from(this.strategies.values());
}
