module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat', // 新功能
        'fix', // 修复Bug
        'docs', // 文档变更
        'style', // 代码风格变更（不影响功能）
        'refactor', // 代码重构（不新增功能或修复Bug）
        'perf', // 性能优化
        'test', // 添加或修改测试
        'build', // 构建相关变更
        'ci', // CI配置变更
        'chore', // 其他不修改src或test的变更
        'revert', // 回退提交
      ],
    ],
    'type-case': [2, 'always', 'lowercase'],
    'type-empty': [2, 'never'],
    'subject-empty': [2, 'never'],
    'subject-full-stop': [2, 'never', '.'],
    'subject-case': [2, 'never', ['sentence-case', 'start-case', 'pascal-case', 'upper-case']],
    'body-leading-blank': [2, 'always'],
    'footer-leading-blank': [1, 'always'],
    'header-max-length': [2, 'always', 72],
  },
};
