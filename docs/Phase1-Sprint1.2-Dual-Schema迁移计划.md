# Phase 1 Sprint 1.2: Dual-Schema架构迁移计划

## 🎯 迁移目标

**将当前单一Schema架构迁移到Dual-Schema架构，实现平台数据和业务数据的物理分离**

## 📊 数据分层重新设计

### **Public Schema (平台控制层)**

```prisma
// prisma/public-schema.prisma
generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/@prisma-public/client"
}

datasource db {
  provider = "postgresql"
  url      = env("PUBLIC_DATABASE_URL")
}

// 平台基础设施 - 租户管理
model Tenant {
  id                 Int      @id @default(autoincrement())
  code               String   @unique
  name               String
  website            String?
  domain             String?
  status             Int      @default(1)
  createTime         DateTime @default(now()) @map("create_time")
  updateTime         DateTime @updatedAt @map("update_time")
  metadata           Json?    @default("{}")
  registrationSource String?  @default("manual") @map("registration_source")

  // 关联
  datasource         TenantDatasource?
  subscriptions      TenantSubscription[]
  features          TenantFeature[]
  configs           TenantConfig[]

  @@map("tenant")
}

model TenantDatasource {
  id          Int     @id @default(autoincrement())
  tenantId    Int     @unique @map("tenant_id")

  // 支持共享和独立两种模式
  isShared    Boolean @default(true) @map("is_shared")

  // 独立数据库配置
  host        String?
  port        Int?
  username    String?
  password    String? // 加密存储
  database    String?
  ssl         Boolean @default(true)

  createTime  DateTime @default(now()) @map("create_time")
  updateTime  DateTime @updatedAt @map("update_time")

  tenant      Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("tenant_datasource")
}

// 平台订阅系统
model SubscriptionPlan {
  id            Int      @id @default(autoincrement())
  code          String   @unique
  name          String
  description   String?
  price         Decimal  @default(0) @db.Decimal(10,2)
  currency      String   @default("CNY")
  billingCycle  String   @default("monthly") @map("billing_cycle")

  // 支持的部署模式
  deploymentMode String[] @default(["shared"]) @map("deployment_mode") // shared, dedicated

  features      Json     @default("[]")
  limits        Json     @default("{}")
  databases     Json?    @default("{}") // 数据库资源配置
  isActive      Boolean  @default(true) @map("is_active")
  sortOrder     Int      @default(0) @map("sort_order")
  metadata      Json?    @default("{}")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  subscriptions TenantSubscription[]

  @@map("subscription_plans")
}

model TenantSubscription {
  id           Int      @id @default(autoincrement())
  tenantId     Int      @map("tenant_id")
  planId       Int      @map("plan_id")
  duration     Int      @default(1)
  billingCycle String   @default("monthly") @map("billing_cycle")
  startDate    DateTime @map("start_date")
  endDate      DateTime @map("end_date")
  status       String   @default("active")
  autoRenew    Boolean  @default(false) @map("auto_renew")
  paymentInfo  Json?    @default("{}") @map("payment_info")
  metadata     Json     @default("{}")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  tenant       Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  plan         SubscriptionPlan @relation(fields: [planId], references: [id])

  @@map("tenant_subscriptions")
}

// 租户功能权限
model TenantFeature {
  id          Int      @id @default(autoincrement())
  tenantId    Int      @map("tenant_id")
  featureCode String   @map("feature_code")
  enabled     Boolean  @default(true)
  quota       Int?
  usedQuota   Int      @default(0) @map("used_quota")
  resetAt     DateTime? @map("reset_at")
  metadata    Json?    @default("{}")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, featureCode])
  @@map("tenant_features")
}

// 租户配置
model TenantConfig {
  id        Int      @id @default(autoincrement())
  tenantId  Int      @map("tenant_id")
  category  String
  key       String
  value     String
  dataType  String   @default("string") @map("data_type")
  encrypted Boolean  @default(false)
  description String?
  metadata  Json?    @default("{}")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, category, key])
  @@map("tenant_config")
}

// 系统配置
model SystemConfig {
  id          Int      @id @default(autoincrement())
  category    String
  key         String
  value       String
  dataType    String   @default("string") @map("data_type")
  description String?
  isPublic    Boolean  @default(false) @map("is_public")
  metadata    Json?    @default("{}")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@unique([category, key])
  @@map("system_config")
}

// 平台级审计日志
model AuditLog {
  id         Int      @id @default(autoincrement())
  tenantId   Int?     @map("tenant_id")
  userId     Int?     @map("user_id")
  action     String
  resource   String
  resourceId String?  @map("resource_id")
  ipAddress  String?  @map("ip_address")
  userAgent  String?  @map("user_agent")
  details    Json?    @default("{}")
  createdAt  DateTime @default(now()) @map("created_at")

  @@index([tenantId])
  @@index([userId])
  @@index([action])
  @@index([createdAt])
  @@map("audit_logs")
}
```

### **Tenant Schema (业务数据层)**

```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户管理（支持共享和独立两种模式）
model User {
  id          Int      @id @default(autoincrement())
  tenantId    Int?     @map("tenant_id") // 共享模式需要，独立模式可选
  username    String
  password    String
  email       String?
  realName    String?  @map("real_name")
  phoneNumber String?  @map("phone_number")
  avatar      String?
  status      Int      @default(1)
  userType    String   @default("TENANT") @map("user_type") // SYSTEM, TENANT
  metadata    Json?    @default("{}")
  createTime  DateTime @default(now()) @map("create_time")
  updateTime  DateTime @updatedAt @map("update_time")

  roles       UserRole[]

  // 共享模式的约束
  @@unique([tenantId, username], name: "user_tenant_username_key")
  @@unique([tenantId, email], name: "user_tenant_email_key")

  // 独立模式的约束（当tenantId为null时）
  @@unique([username], name: "user_username_key")
  @@unique([email], name: "user_email_key")

  @@map("user")
}

model Role {
  id          Int      @id @default(autoincrement())
  tenantId    Int?     @map("tenant_id")
  code        String
  name        String
  description String?
  permissions Json     @default("[]")
  isSystem    Boolean  @default(false) @map("is_system")
  status      Int      @default(1)
  createTime  DateTime @default(now()) @map("create_time")
  updateTime  DateTime @updatedAt @map("update_time")

  userRoles   UserRole[]

  @@unique([tenantId, code])
  @@map("role")
}

model UserRole {
  id         Int      @id @default(autoincrement())
  userId     Int      @map("user_id")
  roleId     Int      @map("role_id")
  createTime DateTime @default(now()) @map("create_time")

  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  role       Role     @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_role")
}

// 网站管理
model Website {
  id           Int      @id @default(autoincrement())
  tenantId     Int?     @map("tenant_id") // 共享模式需要，独立模式可选
  name         String
  domain       String?
  websiteType  String   @default("tenant") @map("website_type")
  status       String   @default("draft")
  config       Json     @default("{}")
  content      Json     @default("{}")
  seoConfig    Json?    @default("{}") @map("seo_config")
  analytics    Json?    @default("{}")
  metadata     Json?    @default("{}")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  publishedAt  DateTime? @map("published_at")

  pages        WebsitePage[]

  @@map("websites")
}

model WebsitePage {
  id         Int      @id @default(autoincrement())
  websiteId  Int      @map("website_id")
  path       String
  title      String
  content    Json     @default("{}")
  seoMeta    Json?    @default("{}") @map("seo_meta")
  status     String   @default("draft")
  metadata   Json?    @default("{}")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  website    Website  @relation(fields: [websiteId], references: [id], onDelete: Cascade)

  @@unique([websiteId, path])
  @@map("website_pages")
}

// 通知系统
model Notification {
  id        Int      @id @default(autoincrement())
  tenantId  Int?     @map("tenant_id")
  userId    Int?     @map("user_id")
  type      String
  title     String
  content   String
  isRead    Boolean  @default(false) @map("is_read")
  metadata  Json?    @default("{}")
  createdAt DateTime @default(now()) @map("created_at")
  readAt    DateTime? @map("read_at")

  @@index([tenantId, userId])
  @@index([createdAt])
  @@map("notifications")
}

// 使用统计
model UsageStats {
  id         Int      @id @default(autoincrement())
  tenantId   Int?     @map("tenant_id") // 独立模式下可能不需要
  date       DateTime @db.Date
  category   String
  value      Int      @default(0)
  unit       String   @default("count")
  metadata   Json?    @default("{}")
  createdAt  DateTime @default(now()) @map("created_at")

  @@unique([tenantId, date, category])
  @@index([tenantId, date])
  @@map("usage_stats")
}

// 支付记录
model Payment {
  id               Int      @id @default(autoincrement())
  tenantId         Int?     @map("tenant_id")
  subscriptionId   Int?     @map("subscription_id")
  amount           Decimal  @db.Decimal(10,2)
  currency         String   @default("CNY")
  paymentMethod    String   @map("payment_method")
  paymentProvider  String   @map("payment_provider")
  transactionId    String?  @map("transaction_id")
  status           String   @default("pending")
  paymentData      Json?    @default("{}") @map("payment_data")
  metadata         Json?    @default("{}")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")
  paidAt          DateTime? @map("paid_at")

  @@index([tenantId])
  @@index([status])
  @@map("payments")
}
```

## 🔄 迁移步骤

### **阶段1: 准备工作（1天）**

1. **备份当前数据库**

```bash
# 创建完整备份
pg_dump -h localhost -U postgres -d flexihub > backup_before_migration.sql
```

2. **更新环境变量**

```bash
# .env
DATABASE_URL="postgresql://user:pass@localhost:5432/flexihub_tenant"
PUBLIC_DATABASE_URL="postgresql://user:pass@localhost:5432/flexihub_public"
```

3. **调整现有public-schema.prisma**

```bash
# 基于现有public-schema.prisma进行调整
cp prisma/public-schema.prisma prisma/public-schema-new.prisma
```

### **阶段2: 创建Public数据库（1天）**

```bash
# 1. 创建公共数据库
createdb flexihub_public

# 2. 生成并推送public schema
npm run db:generate:public
npm run db:push:public

# 3. 迁移平台数据
node scripts/migrate-to-public-schema.js
```

### **阶段3: 调整Tenant Schema（1天）**

```bash
# 1. 调整主schema，移除平台表
# 2. 重新生成客户端
npm run db:generate:tenant

# 3. 清理已迁移的表
node scripts/cleanup-migrated-tables.js
```

### **阶段4: 更新服务层（2天）**

```typescript
// 1. 创建数据库工厂服务
src/core/database/
├── public.client.ts      # 公共数据库客户端
├── tenant.client.ts      # 租户数据库客户端
├── database.factory.ts   # 数据库工厂
└── data-access.service.ts # 统一数据访问

// 2. 更新现有服务以使用新的双客户端架构
```

### **阶段5: 测试和验证（1天）**

```bash
# 1. 运行测试套件
npm test

# 2. 验证数据完整性
node scripts/verify-migration.js

# 3. 性能测试
npm run test:performance
```

## 🛠️ 核心迁移脚本

### **数据迁移脚本**

```javascript
// scripts/migrate-to-public-schema.js
const { PrismaClient } = require('@prisma/client');
const { PrismaClient: PublicPrismaClient } = require('@prisma-public/client');

const prisma = new PrismaClient();
const publicPrisma = new PublicPrismaClient();

async function migrateToPublicSchema() {
  console.log('🚀 开始迁移到Public Schema...');

  // 1. 迁移租户数据
  await migrateTenants();

  // 2. 迁移订阅计划
  await migrateSubscriptionPlans();

  // 3. 迁移租户订阅
  await migrateTenantSubscriptions();

  // 4. 迁移租户功能
  await migrateTenantFeatures();

  // 5. 迁移租户配置
  await migrateTenantConfigs();

  // 6. 迁移系统配置
  await migrateSystemConfigs();

  // 7. 迁移审计日志
  await migrateAuditLogs();

  console.log('✅ 迁移完成！');
}

async function migrateTenants() {
  const tenants = await prisma.tenant.findMany({
    include: { datasource: true },
  });

  for (const tenant of tenants) {
    await publicPrisma.tenant.create({
      data: {
        id: tenant.id,
        code: tenant.code,
        name: tenant.name,
        website: tenant.website,
        domain: tenant.domain,
        status: tenant.status,
        createTime: tenant.createTime,
        updateTime: tenant.updateTime,
        metadata: tenant.metadata,
        registrationSource: tenant.registrationSource,
        datasource: tenant.datasource
          ? {
              create: {
                isShared: true, // 默认为共享模式
                host: tenant.datasource.host,
                port: tenant.datasource.port,
                username: tenant.datasource.username,
                password: tenant.datasource.password,
                database: tenant.datasource.database,
                ssl: tenant.datasource.ssl,
                createTime: tenant.datasource.createTime,
                updateTime: tenant.datasource.updateTime,
              },
            }
          : undefined,
      },
    });
  }
}

// ... 其他迁移函数
```

## 📊 迁移验证

### **数据完整性验证**

```javascript
// scripts/verify-migration.js
async function verifyMigration() {
  const originalCounts = {
    tenants: await prisma.tenant.count(),
    subscriptions: await prisma.tenantSubscription.count(),
    features: await prisma.tenantFeature.count(),
    configs: await prisma.tenantConfig.count(),
  };

  const newCounts = {
    tenants: await publicPrisma.tenant.count(),
    subscriptions: await publicPrisma.tenantSubscription.count(),
    features: await publicPrisma.tenantFeature.count(),
    configs: await publicPrisma.tenantConfig.count(),
  };

  console.log('📊 数据迁移验证:');
  Object.keys(originalCounts).forEach(key => {
    const match = originalCounts[key] === newCounts[key];
    console.log(`${key}: ${originalCounts[key]} → ${newCounts[key]} ${match ? '✅' : '❌'}`);
  });
}
```

## 🎯 迁移后的架构优势

### **1. 清晰的职责分离**

- **Public Schema**: 平台管理和配置
- **Tenant Schema**: 业务数据和功能

### **2. 支持多种部署模式**

- **共享模式**: 成本低，适合小客户
- **独立模式**: 安全性高，适合企业客户

### **3. 灵活的扩展能力**

- 独立数据库可独立扩容
- 支持跨区域部署
- 满足合规要求

## ⚠️ 风险和缓解措施

### **风险1: 数据迁移失败**

- **缓解**: 完整备份 + 分阶段迁移 + 回滚计划

### **风险2: 服务中断**

- **缓解**: 维护窗口 + 灰度发布 + 监控告警

### **风险3: 性能影响**

- **缓解**: 性能测试 + 连接池优化 + 缓存策略

## 🎯 总结

这个迁移计划将FlexiHub从单一Schema升级到企业级Dual-Schema架构，实现：

1. **平台数据**和**业务数据**的物理分离
2. 支持**共享**和**独立**两种数据库模式
3. 为未来的企业级功能奠定基础
4. 保持100%的功能兼容性

预计总迁移时间：**5-6天**，其中数据迁移1天，代码调整3天，测试验证1-2天。
