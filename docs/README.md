# FlexiHub 文档中心

## 📚 文档索引

### 🎯 核心规划文档

- **[开发路线图与计划](./FlexiHub开发路线图与计划.md)** - 项目整体规划和Sprint计划
- **[Phase1完成报告](./Phase1-数据库架构升级完成报告.md)** - 数据库架构升级的详细报告

### 🏗️ 架构设计文档

- **[企业级多租户架构](./企业级多租户架构-独立数据库模式.md)** - 支持独立数据库的企业级架构设计

### 📋 功能设计文档

- **[系统官网建站化方案](./系统官网建站化方案.md)** - 官网使用自身建站系统的方案
- **[系统管理员角色设计](./系统管理员角色设计.md)** - 系统管理角色和权限设计
- **[租户自助注册指南](./租户自助注册使用指南.md)** - 租户注册流程和使用指南

### 🔧 技术文档

- **[项目改动影响分析](./项目改动影响分析与整合方案.md)** - 架构变更的影响分析
- **[前端项目整合指南](./前端项目整合指南.md)** - 前端项目集成方案
- **[Swagger改进建议](./swagger-improvements.md)** - API文档优化建议

### 📁 子目录

- **[项目管理](./project_management/)** - 项目管理相关文档
- **[开发指引](./instructions/)** - 开发规范和指引

---

## 🎯 当前状态

### ✅ 已完成

- **Phase 1 Sprint 1.1**: 数据库架构升级完成
- **企业级功能**: 订阅管理、权限控制、配置管理等11个核心模块

### 🔄 进行中

- **Phase 1 Sprint 1.2**: 核心服务重构
  - 服务层重构
  - 统一数据访问层
  - API架构优化

### 🎯 下一步

- **Phase 1 Sprint 1.3**: API开发与优化
- **Phase 2**: Dual-Schema架构实现
- **Phase 3**: 独立数据库模式支持

---

## 📖 快速导航

### 👥 角色导航

#### 🏗️ 架构师

- [企业级多租户架构设计](./企业级多租户架构-独立数据库模式.md)
- [开发路线图与计划](./FlexiHub开发路线图与计划.md)

#### 💻 开发工程师

- [前端项目整合指南](./前端项目整合指南.md)
- [Swagger改进建议](./swagger-improvements.md)
- [项目改动影响分析](./项目改动影响分析与整合方案.md)

#### 📋 产品经理

- [租户自助注册指南](./租户自助注册使用指南.md)
- [系统官网建站化方案](./系统官网建站化方案.md)

#### 🎯 项目经理

- [开发路线图与计划](./FlexiHub开发路线图与计划.md)
- [Phase1完成报告](./Phase1-数据库架构升级完成报告.md)

### 📂 功能导航

#### 🔑 核心架构

- 多租户架构设计
- 数据库分层策略
- 服务层重构方案

#### 🏢 租户管理

- 租户注册流程
- 订阅计划管理
- 权限控制系统

#### 🌐 网站建设

- 官网建站化
- 租户网站管理
- 内容管理系统

#### 🔧 技术集成

- 前端项目整合
- API文档规范
- 数据库迁移

---

## 🎯 项目愿景

**"一键注册，即刻建站"**

FlexiHub致力于构建企业级多租户SaaS建站平台，通过分阶段的架构演进，最终实现：

1. **灵活架构**: 支持共享和独立数据库双模式
2. **企业级功能**: 订阅管理、权限控制、配置管理
3. **优秀体验**: 从注册到建站的极致用户体验
4. **商业价值**: 多层次订阅模式，满足不同客户需求

---

_最后更新: 2025-05-30_  
_文档版本: v2.0_
