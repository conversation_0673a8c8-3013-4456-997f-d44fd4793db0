# Phase 1 Sprint 2: 依赖修复与系统稳定化

## 🎯 Sprint 目标

**主要目标：** 修复所有依赖引用错误，确保系统可正常编译和运行  
**次要目标：** 完善类型定义，提升代码质量  
**时间估计：** 1-2个工作日

## 📋 任务清单

### 🔥 P0 - 关键问题修复 (当日必完成)

#### 1. 批量修复Prisma服务引用 (预计3小时)

**影响文件数量：** 约40个文件  
**错误类型：** 引用不存在的服务路径

```typescript
// 需要替换的模式
❌ import { PublicPrismaService } from '@/core/database/prisma/public-prisma.service';
✅ import { DatabaseFactory } from '@/core/database/database.factory';

❌ import { TenantPrismaService } from '@/core/database/prisma/tenant-prisma.service';
✅ import { DatabaseFactory } from '@/core/database/database.factory';
```

**批量修复策略：**

- 使用正则表达式批量替换import语句
- 更新constructor注入方式
- 修改调用方法

#### 2. Schema表名和字段修复 (预计2小时)

**问题文件：**

- `prisma/seed.ts` - 主要种子文件
- `prisma/seeds/component-library.seed.ts` - 组件库种子

**需要修复的表名：**

- ❌ `subscriptionPlan` → ✅ `plan` 或对应的正确表名
- ❌ `systemConfig` → ✅ `config` 或对应的正确表名
- ❌ `tenantSubscription` → ✅ `subscription` 或对应的正确表名

**需要修复的字段名：**

- ❌ `emailAddress` → ✅ `email`
- ❌ `remark` → ✅ `description` 或移除
- ❌ `username_tenantId` → ✅ 使用正确的复合索引名

### 🔧 P1 - 类型兼容性修复 (次日完成)

#### 3. DTO类型兼容性修复 (预计2小时)

**主要问题：**

- `TenantResponseDto` 类型不匹配
- `Decimal` 类型需要转换为 `number`
- `CommonStatus` 枚举值映射

**修复策略：**

- 创建类型转换工具函数
- 更新DTO接口定义
- 添加类型守卫

#### 4. Entity类型清理 (预计1小时)

**需要处理的Entity：**

- `UserExtension` - 检查是否存在于Schema
- `UserRemark` - 检查是否存在于Schema
- `UserTag` - 检查是否存在于Schema

**处理方案：**

- 如果Schema中存在，更新类型定义
- 如果不存在，移除相关引用或创建Schema

### 🧪 P2 - 测试与验证 (第二日完成)

#### 5. 编译验证 (预计30分钟)

**验证步骤：**

```bash
npm run build
npm run lint
npm run format:check
```

#### 6. 运行时验证 (预计1小时)

**验证内容：**

- 数据库连接正常
- API端点可访问
- 租户隔离功能正常
- 缓存功能正常

## 🛠️ 技术方案

### 1. 批量文件修复脚本

创建自动化脚本处理import替换：

```javascript
// scripts/fix-prisma-imports.js
const fs = require('fs');
const path = require('path');

const replacements = [
  {
    from: /import\s*{\s*PublicPrismaService\s*}\s*from\s*['"][^'"]*public-prisma\.service['"];?/g,
    to: "import { DatabaseFactory } from '@/core/database/database.factory';",
  },
  {
    from: /import\s*{\s*TenantPrismaService\s*}\s*from\s*['"][^'"]*tenant-prisma\.service['"];?/g,
    to: "import { DatabaseFactory } from '@/core/database/database.factory';",
  },
];
```

### 2. 数据库连接重构模式

**旧模式：**

```typescript
constructor(
  private readonly publicPrisma: PublicPrismaService,
  @Inject(TENANT_PRISMA_SERVICE) private readonly tenantPrisma: TenantPrismaService,
) {}

async findUser(id: number) {
  return this.tenantPrisma.user.findUnique({ where: { id } });
}
```

**新模式：**

```typescript
constructor(
  private readonly databaseFactory: DatabaseFactory,
) {}

async findUser(id: number, tenantId: number) {
  const tenantDb = this.databaseFactory.getTenantClient(tenantId);
  return tenantDb.user.findUnique({ where: { id } });
}
```

### 3. 类型转换工具

```typescript
// src/core/common/utils/type-converter.util.ts
export class TypeConverterUtil {
  static decimalToNumber(decimal: any): number {
    return decimal ? Number(decimal.toString()) : 0;
  }

  static mapPrismaResponse<T>(data: any): T {
    // 递归处理Decimal类型转换
    // 处理日期格式化
    // 处理枚举值映射
  }
}
```

## 📊 质量标准

### 编译成功率目标

- **Day 1 结束：** 80% (修复主要依赖问题)
- **Day 2 结束：** 100% (完全编译成功)

### 功能完整性目标

- **核心API：** 100% 可用
- **数据库连接：** 100% 正常
- **租户隔离：** 100% 有效
- **缓存功能：** 100% 正常

### 代码质量目标

- **ESLint错误：** 0个
- **TypeScript错误：** 0个
- **单元测试：** 通过率 > 95%

## 🚀 完成标准

### 编译验证

```bash
✅ npm run build        # 编译成功
✅ npm run lint         # 无ESLint错误
✅ npm run test         # 测试通过
✅ npm run dev          # 服务启动成功
```

### 功能验证

```bash
✅ curl http://localhost:3000/health           # 健康检查
✅ curl http://localhost:3000/platform/tenants # 租户列表API
✅ curl http://localhost:3000/api/users        # 用户API (需要租户头部)
```

### 性能验证

- 🎯 API响应时间 < 200ms
- 🎯 数据库连接建立时间 < 50ms
- 🎯 缓存命中率 > 90%

## 📋 风险控制

### 主要风险

1. **Schema不匹配风险**

   - **影响：** 种子文件无法执行
   - **缓解：** 优先验证Schema结构

2. **类型不兼容风险**

   - **影响：** 编译失败
   - **缓解：** 创建类型转换层

3. **功能回归风险**
   - **影响：** 现有功能受损
   - **缓解：** 分步测试验证

### 回滚方案

如果修复遇到阻塞问题：

1. **Step 1：** 回滚到上一个commit
2. **Step 2：** 创建兼容层而非直接替换
3. **Step 3：** 渐进式迁移而非批量修改

## 🎯 成功指标

- ✅ **编译成功率：** 100%
- ✅ **运行时稳定性：** 无崩溃
- ✅ **API可用性：** 100%
- ✅ **测试通过率：** > 95%
- ✅ **性能指标：** 满足目标
- ✅ **代码质量：** 无警告和错误

---

**Next Action:** 开始执行P0任务 - 批量修复Prisma服务引用
