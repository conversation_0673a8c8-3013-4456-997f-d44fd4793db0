# Swagger文档改进说明

## 概述

本次改进主要解决了两个问题：

1. **Swagger导出Markdown格式**：提供API文档的Markdown格式导出功能
2. **统一返回格式显示**：在Swagger文档中正确显示系统的统一返回格式

## 新增功能

### 1. Swagger转Markdown服务

创建了 `SwaggerMarkdownService`，提供以下功能：

- 将Swagger JSON文档转换为Markdown格式
- 自动添加统一返回格式说明
- 包含状态码说明和认证方式
- 按API标签分组展示
- 生成完整的请求/响应示例

#### 使用方式

访问以下API端点：

- `GET /api/system/swagger/markdown` - 导出Markdown格式文档
- `GET /api/system/swagger/json` - 获取原始Swagger JSON

### 2. 统一返回格式DTO

创建了完整的API响应DTO集合：

- `ApiResponseDto` - 通用响应格式
- `SuccessResponseDto` - 成功响应格式
- `PaginationResponseDto` - 分页响应格式
- `ErrorResponseDto` - 错误响应格式
- `ListResponseDto` - 列表响应格式

### 3. API响应装饰器

提供了便捷的装饰器简化Swagger注解：

```typescript
// 成功响应
@ApiCompleteResponse(UserDto, '获取用户成功')

// 分页响应
@ApiCompletePaginationResponse(UserDto, '获取用户列表成功')

// 列表响应
@ApiListResponse(UserDto, '获取用户列表成功')

// 错误响应
@ApiErrorResponses()
```

## 文件结构

```
src/
├── core/
│   ├── swagger/
│   │   ├── swagger.module.ts          # Swagger模块
│   │   ├── swagger.controller.ts      # Swagger控制器
│   │   └── swagger-markdown.service.ts # Markdown转换服务
│   └── common/
│       ├── dto/
│       │   └── api-response.dto.ts    # 统一响应DTO
│       └── decorators/
│           └── api-response.decorator.ts # API响应装饰器
└── main.ts                            # 更新的Swagger配置
```

## 配置更新

### main.ts

- 添加了统一返回格式说明到Swagger描述中
- 配置了额外的Schema模型
- 优化了Swagger UI设置
- 添加了全局应用实例存储

### app.module.ts

- 导入了新的 `SwaggerModule`

## 使用示例

### 控制器中使用新装饰器

```typescript
@Controller('users')
@ApiTags('用户管理')
export class UserController {
  @Get()
  @ApiOperation({ summary: '获取用户列表' })
  @ApiCompletePaginationResponse(UserDto, '获取用户列表成功')
  async findAll(@Query() query: QueryUserDto) {
    return this.userService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取用户详情' })
  @ApiCompleteResponse(UserDto, '获取用户成功')
  async findOne(@Param('id') id: string) {
    return this.userService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: '创建用户' })
  @ApiCompleteResponse(UserDto, '创建用户成功')
  async create(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }
}
```

### 响应格式示例

所有API现在都会在Swagger中显示统一的返回格式：

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    // 具体的响应数据
  }
}
```

分页响应：

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "items": [...],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  }
}
```

## 访问方式

1. **Swagger UI**: `http://localhost:3000/api/docs`
2. **Markdown导出**: `http://localhost:3000/api/system/swagger/markdown`
3. **JSON导出**: `http://localhost:3000/api/system/swagger/json`

## 优势

1. **前端开发友好**：清晰显示完整的返回格式，包括统一的外层结构
2. **文档生成**：可以导出Markdown格式的API文档用于项目文档
3. **开发效率**：使用装饰器简化了Swagger注解的编写
4. **一致性**：确保所有API接口都遵循统一的返回格式
5. **可维护性**：集中管理API响应格式，便于后续调整

## 注意事项

1. 所有新的控制器都应该使用新的API响应装饰器
2. 现有控制器可以逐步迁移到新的装饰器
3. `TransformInterceptor` 会自动处理统一返回格式的包装
4. 导出功能使用了`@Public()`装饰器，无需认证即可访问

## 下一步优化

1. 可以考虑添加PDF导出功能
2. 支持更多自定义的文档模板
3. 添加API变更历史记录
4. 集成API测试功能
