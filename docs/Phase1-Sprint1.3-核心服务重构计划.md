# Phase 1 Sprint 1.3: 核心服务重构与API优化

## 🎯 Sprint目标

**基于Dual-Schema架构，重构核心服务层，实现平台管理和业务功能的清晰分离，优化API结构和性能**

## 📋 主要任务

### **任务1: 数据库客户端工厂 (1天)**

- 创建双客户端管理系统
- 实现动态租户数据库连接
- 建立统一数据访问接口

### **任务2: 服务层重构 (2天)**

- 平台服务层：租户管理、订阅计划、系统配置
- 业务服务层：用户管理、网站管理、内容管理
- 统一服务接口和错误处理

### **任务3: API结构优化 (2天)**

- 平台管理API (`/platform/*`)
- 业务功能API (`/api/*`)
- API版本控制和文档更新

### **任务4: 中间件增强 (1天)**

- 租户识别中间件优化
- 权限验证中间件
- 审计日志中间件

### **任务5: 缓存策略实现 (1天)**

- 分层缓存设计
- 租户配置缓存
- 数据库查询缓存

## 🏗️ 架构设计

### **数据访问层架构**

```
src/core/database/
├── clients/
│   ├── public.client.ts       # Public Schema客户端
│   ├── tenant.client.ts       # Tenant Schema客户端
│   └── database.factory.ts    # 数据库工厂
├── services/
│   ├── data-access.service.ts # 统一数据访问服务
│   └── connection.service.ts  # 连接管理服务
└── interfaces/
    ├── database.interface.ts  # 数据库接口定义
    └── tenant-context.interface.ts # 租户上下文接口
```

### **服务层架构**

```
src/modules/
├── platform/                 # 平台管理服务（Public Schema）
│   ├── tenant-management/
│   │   ├── tenant.service.ts
│   │   ├── tenant.controller.ts
│   │   └── dto/
│   ├── subscription/
│   │   ├── subscription.service.ts
│   │   ├── subscription.controller.ts
│   │   └── dto/
│   └── system-config/
│       ├── system-config.service.ts
│       ├── system-config.controller.ts
│       └── dto/
└── business/                 # 业务功能服务（Tenant Schema）
    ├── user-management/
    │   ├── user.service.ts
    │   ├── user.controller.ts
    │   └── dto/
    ├── website-management/
    │   ├── website.service.ts
    │   ├── website.controller.ts
    │   └── dto/
    └── content-management/
        ├── content.service.ts
        ├── content.controller.ts
        └── dto/
```

### **API路由设计**

```
# 平台管理API（系统管理员）
/platform/tenants              # 租户管理
/platform/subscriptions        # 订阅管理
/platform/configs              # 系统配置
/platform/analytics            # 平台分析

# 业务功能API（租户用户）
/api/users                     # 用户管理
/api/websites                  # 网站管理
/api/pages                     # 页面管理
/api/media                     # 媒体管理
/api/forms                     # 表单管理
```

## 🔧 实现计划

### **第1天: 数据库客户端工厂**

#### **1.1 创建Public客户端**

```typescript
// src/core/database/clients/public.client.ts
import { Injectable } from '@nestjs/common';
import { PrismaClient as PublicPrismaClient } from '@prisma-public/prisma/client';

@Injectable()
export class PublicDatabaseClient {
  private client: PublicPrismaClient;

  constructor() {
    this.client = new PublicPrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });
  }

  async onModuleInit() {
    await this.client.$connect();
  }

  async onModuleDestroy() {
    await this.client.$disconnect();
  }

  get tenant() {
    return this.client.tenant;
  }
  get subscriptionPlan() {
    return this.client.subscriptionPlan;
  }
  get tenantSubscription() {
    return this.client.tenantSubscription;
  }
  get systemConfig() {
    return this.client.systemConfig;
  }
  get auditLog() {
    return this.client.auditLog;
  }
}
```

#### **1.2 创建Tenant客户端**

```typescript
// src/core/database/clients/tenant.client.ts
import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class TenantDatabaseClient {
  private client: PrismaClient;

  constructor() {
    this.client = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });
  }

  async onModuleInit() {
    await this.client.$connect();
  }

  async onModuleDestroy() {
    await this.client.$disconnect();
  }

  get user() {
    return this.client.user;
  }
  get role() {
    return this.client.role;
  }
  get website() {
    return this.client.website;
  }
  get websitePage() {
    return this.client.websitePage;
  }
  get notification() {
    return this.client.notification;
  }
  get payment() {
    return this.client.payment;
  }
}
```

#### **1.3 数据库工厂**

```typescript
// src/core/database/database.factory.ts
import { Injectable } from '@nestjs/common';
import { PublicDatabaseClient } from './clients/public.client';
import { TenantDatabaseClient } from './clients/tenant.client';

@Injectable()
export class DatabaseFactory {
  constructor(
    private readonly publicClient: PublicDatabaseClient,
    private readonly tenantClient: TenantDatabaseClient,
  ) {}

  getPublicClient(): PublicDatabaseClient {
    return this.publicClient;
  }

  async getTenantClient(tenantId?: number): Promise<TenantDatabaseClient> {
    // 目前返回共享客户端，后续支持独立数据库时扩展
    return this.tenantClient;
  }

  async getDynamicTenantClient(tenantId: number) {
    // 查询租户配置，决定使用共享还是独立数据库
    const tenantConfig = await this.publicClient.tenant.findUnique({
      where: { id: tenantId },
      include: { datasource: true },
    });

    if (tenantConfig?.datasource?.isShared === false) {
      // 创建独立数据库连接（Phase 2实现）
      throw new Error('独立数据库模式将在Phase 2实现');
    }

    return this.tenantClient;
  }
}
```

### **第2-3天: 服务层重构**

#### **2.1 平台服务基类**

```typescript
// src/modules/platform/base/platform.service.ts
import { Injectable } from '@nestjs/common';
import { DatabaseFactory } from '@core/database/database.factory';
import { PublicDatabaseClient } from '@core/database/clients/public.client';

@Injectable()
export abstract class BasePlatformService {
  protected publicDb: PublicDatabaseClient;

  constructor(private readonly databaseFactory: DatabaseFactory) {
    this.publicDb = this.databaseFactory.getPublicClient();
  }
}
```

#### **2.2 业务服务基类**

```typescript
// src/modules/business/base/business.service.ts
import { Injectable } from '@nestjs/common';
import { DatabaseFactory } from '@core/database/database.factory';
import { TenantDatabaseClient } from '@core/database/clients/tenant.client';

@Injectable()
export abstract class BaseBusinessService {
  protected tenantDb: TenantDatabaseClient;

  constructor(private readonly databaseFactory: DatabaseFactory) {}

  async initTenantContext(tenantId?: number) {
    this.tenantDb = await this.databaseFactory.getTenantClient(tenantId);
  }
}
```

### **第4-5天: API结构优化**

#### **4.1 平台管理控制器**

```typescript
// src/modules/platform/tenant-management/tenant.controller.ts
import { Controller, Get, Post, Put, Delete, Param, Body } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { TenantManagementService } from './tenant.service';

@ApiTags('平台管理 - 租户')
@Controller('platform/tenants')
export class PlatformTenantController {
  constructor(private readonly tenantService: TenantManagementService) {}

  @Get()
  @ApiOperation({ summary: '获取租户列表' })
  async findAll() {
    return this.tenantService.findAll();
  }

  @Post()
  @ApiOperation({ summary: '创建租户' })
  async create(@Body() createTenantDto: CreateTenantDto) {
    return this.tenantService.create(createTenantDto);
  }
}
```

#### **4.2 业务功能控制器**

```typescript
// src/modules/business/user-management/user.controller.ts
import { Controller, Get, Post, Put, Delete, Param, Body } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { UserManagementService } from './user.service';
import { TenantContext } from '@core/decorators/tenant-context.decorator';

@ApiTags('业务功能 - 用户管理')
@Controller('api/users')
export class BusinessUserController {
  constructor(private readonly userService: UserManagementService) {}

  @Get()
  @ApiOperation({ summary: '获取用户列表' })
  async findAll(@TenantContext() tenantId: number) {
    return this.userService.findAllByTenant(tenantId);
  }

  @Post()
  @ApiOperation({ summary: '创建用户' })
  async create(@TenantContext() tenantId: number, @Body() createUserDto: CreateUserDto) {
    return this.userService.create(tenantId, createUserDto);
  }
}
```

### **第6天: 中间件增强**

#### **6.1 租户识别中间件**

```typescript
// src/core/middleware/tenant-resolution.middleware.ts
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { DatabaseFactory } from '@core/database/database.factory';

@Injectable()
export class TenantResolutionMiddleware implements NestMiddleware {
  constructor(private readonly databaseFactory: DatabaseFactory) {}

  async use(req: Request, res: Response, next: NextFunction) {
    const publicDb = this.databaseFactory.getPublicClient();

    // 从域名解析租户
    const host = req.get('host');
    const tenant = await publicDb.tenant.findFirst({
      where: { domain: host },
    });

    if (tenant) {
      req['tenantId'] = tenant.id;
      req['tenantCode'] = tenant.code;
    }

    next();
  }
}
```

### **第7天: 缓存策略实现**

#### **7.1 租户配置缓存**

```typescript
// src/core/cache/tenant-config.cache.ts
import { Injectable } from '@nestjs/common';
import { CacheService } from '@core/cache/cache.service';
import { DatabaseFactory } from '@core/database/database.factory';

@Injectable()
export class TenantConfigCache {
  constructor(
    private readonly cache: CacheService,
    private readonly databaseFactory: DatabaseFactory,
  ) {}

  async getTenantConfig(tenantId: number) {
    const cacheKey = `tenant:${tenantId}:config`;

    let config = await this.cache.get(cacheKey);
    if (!config) {
      const publicDb = this.databaseFactory.getPublicClient();
      config = await publicDb.tenant.findUnique({
        where: { id: tenantId },
        include: {
          datasource: true,
          subscriptions: { include: { plan: true } },
          features: true,
          configs: true,
        },
      });

      // 缓存1小时
      await this.cache.set(cacheKey, config, 3600);
    }

    return config;
  }
}
```

## 📊 预期成果

### **技术成果**

- ✅ 清晰的平台/业务服务分离
- ✅ 统一的数据访问接口
- ✅ 标准化的API结构
- ✅ 性能优化的缓存策略
- ✅ 增强的中间件系统

### **业务价值**

- 🚀 更快的API响应速度 (<200ms)
- 🔧 更清晰的代码维护性
- 📈 更好的系统可扩展性
- 🛡️ 更强的数据安全性

### **开发效率**

- 🏗️ 标准化的开发模式
- 🧪 更容易的单元测试
- 📚 完善的API文档
- 🔄 支持热重载的开发环境

## 🎯 验收标准

1. **服务分离**: 平台服务和业务服务完全分离，职责清晰
2. **API标准**: 所有API遵循RESTful设计，文档完整
3. **性能指标**: API响应时间<200ms，缓存命中率>80%
4. **代码质量**: 测试覆盖率>80%，ESLint通过
5. **向后兼容**: 现有功能100%保持兼容

## 🚀 后续规划

Sprint 1.3完成后，为Phase 2的独立数据库模式奠定基础：

- 动态数据库连接已预留接口
- 租户配置缓存支持模式切换
- 统一服务接口支持多数据源

---

**时间安排**: 7个工作日  
**责任人**: 开发团队  
**优先级**: 高  
**风险评估**: 中等（主要是重构风险，需要充分测试）
