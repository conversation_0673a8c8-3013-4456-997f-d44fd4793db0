# 企业级多租户架构：独立数据库模式

## 🎯 核心洞察

**用户提问**：如果给租户单独配置数据库呢？不使用公共的数据库而是租户自己的数据库？

**这是真正的企业级SaaS架构！**

## 📊 重新定义架构模式

### **模式对比**

| 模式           | 数据隔离 | 安全性 | 扩展性 | 复杂度 | 适用场景   |
| -------------- | -------- | ------ | ------ | ------ | ---------- |
| **共享数据库** | 逻辑隔离 | 中等   | 中等   | 低     | 中小型SaaS |
| **独立数据库** | 物理隔离 | 高     | 高     | 高     | 企业级SaaS |

### **FlexiHub应该支持两种模式！**

## 🏗️ 双模式架构设计

### **Public Schema (平台控制层)**

```prisma
// prisma/public-schema.prisma
// 在平台主数据库中

model Tenant {
  id              Int      @id @default(autoincrement())
  code            String   @unique
  name            String
  status          Int      @default(1)
  deploymentMode  String   @default("shared")  // shared | dedicated

  // 关联
  datasource      TenantDatasource?
  subscription    TenantSubscription[]
}

model TenantDatasource {
  id          Int     @id @default(autoincrement())
  tenantId    Int     @unique

  // 共享模式（使用主数据库）
  isShared    Boolean @default(true)

  // 独立模式（独立数据库）
  host        String?
  port        Int?
  database    String?
  username    String?
  password    String?  // 加密存储
  ssl         Boolean @default(true)

  // 连接池配置
  maxConnections Int?  @default(10)

  tenant      Tenant  @relation(fields: [tenantId], references: [id])
}

model SubscriptionPlan {
  // 平台级的订阅计划
  code            String   @unique
  deploymentMode  String[] // ["shared", "dedicated"] 支持的部署模式
  databases       Json?    // 数据库资源配置
}
```

### **Tenant Schema (租户业务层)**

```prisma
// prisma/schema.prisma
// 在租户自己的数据库中（独立模式）
// 或在主数据库中带tenantId（共享模式）

model User {
  id          Int      @id @default(autoincrement())
  // 共享模式需要tenantId，独立模式不需要
  tenantId    Int?     // 仅共享模式使用
  username    String
  email       String?
  // ... 用户数据
}

model Website {
  id          Int      @id @default(autoincrement())
  tenantId    Int?     // 仅共享模式使用
  name        String
  domain      String?
  // ... 网站数据
}

// 所有业务数据都在这里
```

## 🔄 动态数据库连接架构

### **数据库工厂服务**

```typescript
@Injectable()
export class DatabaseFactory {
  private tenantClients = new Map<number, PrismaClient>();

  constructor(
    private publicPrisma: PublicPrismaClient, // 平台数据库
    private sharedPrisma: PrismaClient, // 共享租户数据库
  ) {}

  async getTenantClient(tenantId: number): Promise<PrismaClient> {
    // 获取租户数据源配置
    const tenant = await this.publicPrisma.tenant.findUnique({
      where: { id: tenantId },
      include: { datasource: true },
    });

    if (!tenant.datasource) {
      throw new Error(`Tenant ${tenantId} has no datasource configured`);
    }

    // 共享模式：使用主数据库
    if (tenant.datasource.isShared) {
      return this.sharedPrisma;
    }

    // 独立模式：创建专用连接
    if (!this.tenantClients.has(tenantId)) {
      const client = new PrismaClient({
        datasources: {
          db: {
            url: this.buildConnectionString(tenant.datasource),
          },
        },
      });

      this.tenantClients.set(tenantId, client);
    }

    return this.tenantClients.get(tenantId);
  }

  private buildConnectionString(datasource: TenantDatasource): string {
    return `postgresql://${datasource.username}:${datasource.password}@${datasource.host}:${datasource.port}/${datasource.database}?ssl=true`;
  }
}
```

### **统一数据访问服务**

```typescript
@Injectable()
export class DataAccessService {
  constructor(
    private databaseFactory: DatabaseFactory,
    private publicPrisma: PublicPrismaClient,
  ) {}

  // 平台管理数据
  get platform() {
    return this.publicPrisma;
  }

  // 租户业务数据
  async tenant(tenantId: number) {
    const client = await this.databaseFactory.getTenantClient(tenantId);

    return {
      user: client.user,
      website: client.website,
      // 注意：共享模式需要自动添加tenantId过滤
      ...(await this.addTenantFilter(client, tenantId)),
    };
  }

  private async addTenantFilter(client: PrismaClient, tenantId: number) {
    // 检查是否为共享模式
    const tenant = await this.publicPrisma.tenant.findUnique({
      where: { id: tenantId },
      include: { datasource: true },
    });

    if (tenant.datasource?.isShared) {
      // 共享模式：自动添加tenantId过滤
      return this.createSharedModeProxy(client, tenantId);
    }

    // 独立模式：直接返回
    return {};
  }

  private createSharedModeProxy(client: PrismaClient, tenantId: number) {
    // 为共享模式创建自动添加tenantId过滤的代理
    return new Proxy(client, {
      get(target, prop) {
        if (typeof target[prop]?.findMany === 'function') {
          return {
            ...target[prop],
            findMany: (args = {}) =>
              target[prop].findMany({
                ...args,
                where: { ...args.where, tenantId },
              }),
            create: args =>
              target[prop].create({
                ...args,
                data: { ...args.data, tenantId },
              }),
            // ... 其他方法也类似处理
          };
        }
        return target[prop];
      },
    });
  }
}
```

## 🚀 部署和扩展策略

### **租户升级路径**

```typescript
@Injectable()
export class TenantUpgradeService {
  // 从共享模式升级到独立模式
  async upgradeToDeadicated(tenantId: number, targetDbConfig: DatabaseConfig) {
    // 1. 创建新的独立数据库
    await this.createDedicatedDatabase(targetDbConfig);

    // 2. 数据迁移
    await this.migrateSharedToDeadicated(tenantId, targetDbConfig);

    // 3. 更新租户配置
    await this.publicPrisma.tenantDatasource.update({
      where: { tenantId },
      data: {
        isShared: false,
        host: targetDbConfig.host,
        port: targetDbConfig.port,
        database: targetDbConfig.database,
        username: targetDbConfig.username,
        password: await this.encrypt(targetDbConfig.password),
      },
    });

    // 4. 清理共享数据库中的租户数据
    await this.cleanupSharedData(tenantId);
  }
}
```

### **自动化数据库供应**

```typescript
@Injectable()
export class DatabaseProvisioningService {
  // 为新租户自动创建独立数据库
  async provisionDatabase(tenantId: number, plan: SubscriptionPlan) {
    if (plan.deploymentMode.includes('dedicated')) {
      // 1. 在云服务商创建数据库实例
      const dbInstance = await this.cloudProvider.createDatabase({
        name: `flexihub-tenant-${tenantId}`,
        tier: plan.databases.tier,
        storage: plan.databases.storage,
        region: plan.databases.region,
      });

      // 2. 运行数据库迁移
      await this.runMigrations(dbInstance.connectionString);

      // 3. 更新租户配置
      await this.publicPrisma.tenantDatasource.create({
        data: {
          tenantId,
          isShared: false,
          host: dbInstance.host,
          port: dbInstance.port,
          database: dbInstance.database,
          username: dbInstance.username,
          password: await this.encrypt(dbInstance.password),
        },
      });
    }
  }
}
```

## 💡 最终架构优势

### **灵活性**

- ✅ 支持共享和独立两种模式
- ✅ 租户可以根据需求升级
- ✅ 满足不同安全和性能要求

### **安全性**

- ✅ 独立模式物理隔离
- ✅ 共享模式逻辑隔离
- ✅ 数据泄露风险最小化

### **扩展性**

- ✅ 独立数据库可独立扩容
- ✅ 支持跨区域部署
- ✅ 满足企业级性能要求

### **商业价值**

- ✅ 多层次订阅定价
- ✅ 满足不同客户需求
- ✅ 支持企业客户私有化部署

## 🎯 结论

**这才是真正的企业级多租户SaaS架构！**

现在我完全理解了你原始dual-schema设计的智慧：

- **Public Schema**: 平台控制层，管理租户和数据源
- **Tenant Schema**: 业务数据层，可以在共享DB或独立DB中

这种设计既支持经济的共享模式，又支持企业级的独立模式，这是真正的SaaS产品应该有的架构！
