# 系统官网建站化方案

## 🎯 方案概述

将FlexiHub的官网通过自己的建站系统来构建和管理，实现"自举式"产品展示，既是最好的产品演示，也是成本最低的官网解决方案。

## 🏗️ 架构设计

### 1. 特殊租户模式

```typescript
// 创建系统官网专用租户
const SYSTEM_WEBSITE_TENANT = {
  id: 0, // 特殊ID，表示系统租户
  code: 'flexihub-official',
  name: 'FlexiHub官网',
  type: 'SYSTEM', // 特殊类型
  domain: 'www.flexihub.com',
  status: 1,
  privileges: {
    unlimited: true, // 无限制使用
    priority: 'highest', // 最高优先级
    customFeatures: ['seo_optimization', 'performance_optimization'],
  },
};
```

### 2. 域名路由策略

```typescript
// src/core/routing/domain-router.service.ts
@Injectable()
export class DomainRouterService {
  async resolveTenant(domain: string): Promise<TenantInfo> {
    // 系统官网域名特殊处理
    if (this.isOfficialDomain(domain)) {
      return {
        tenantId: 0,
        type: 'SYSTEM',
        website: await this.getOfficialWebsite(),
      };
    }

    // 租户注册门户
    if (this.isRegistrationDomain(domain)) {
      return {
        tenantId: -1,
        type: 'REGISTRATION',
        website: await this.getRegistrationPortal(),
      };
    }

    // 普通租户网站
    return this.resolveTenantWebsite(domain);
  }

  private isOfficialDomain(domain: string): boolean {
    const officialDomains = ['flexihub.com', 'www.flexihub.com', 'official.flexihub.com'];
    return officialDomains.includes(domain);
  }
}
```

## 📊 数据结构设计

### 1. 网站类型区分

```sql
-- 扩展网站表，支持系统网站
ALTER TABLE websites ADD COLUMN website_type VARCHAR(20) DEFAULT 'tenant';

-- 网站类型枚举
-- 'tenant': 普通租户网站
-- 'system': 系统官网
-- 'registration': 注册门户
-- 'landing': 产品落地页

-- 为系统官网创建专用记录
INSERT INTO websites (
  id,
  tenant_id,
  name,
  domain,
  website_type,
  status,
  config
) VALUES (
  0,
  0,
  'FlexiHub官网',
  'www.flexihub.com',
  'system',
  'published',
  '{
    "seo": {
      "title": "FlexiHub - 企业级多租户建站平台",
      "description": "专业的SaaS建站解决方案",
      "keywords": "建站,SaaS,多租户,企业级"
    },
    "performance": {
      "cache": true,
      "cdn": true,
      "compression": true
    }
  }'
);
```

### 2. 官网专用组件库

```typescript
// src/modules/website/components/official/
export const OfficialComponents = {
  // 首页英雄区
  HeroSection: {
    component: 'HeroSection',
    props: {
      title: 'FlexiHub企业级建站平台',
      subtitle: '一键注册，即刻建站，让每个企业都有自己的官网',
      ctaButton: {
        text: '立即注册',
        link: '/register',
        style: 'primary',
      },
      backgroundImage: '/assets/hero-bg.jpg',
    },
  },

  // 功能特性
  FeaturesSection: {
    component: 'FeaturesGrid',
    props: {
      title: '为什么选择FlexiHub？',
      features: [
        {
          icon: 'rocket',
          title: '快速部署',
          description: '5分钟完成注册，立即拥有专业网站',
        },
        {
          icon: 'shield',
          title: '企业级安全',
          description: '银行级数据加密，多重安全防护',
        },
        {
          icon: 'chart',
          title: '智能分析',
          description: '实时数据分析，助力业务增长',
        },
      ],
    },
  },

  // 价格套餐
  PricingSection: {
    component: 'PricingTable',
    props: {
      title: '选择适合您的套餐',
      plans: [
        {
          name: '免费版',
          price: 0,
          features: ['1个网站', '基础模板', '社区支持'],
          cta: '免费开始',
        },
        {
          name: '专业版',
          price: 299,
          features: ['10个网站', '高级模板', '优先支持'],
          cta: '立即购买',
          recommended: true,
        },
      ],
    },
  },

  // 客户案例
  TestimonialsSection: {
    component: 'TestimonialCarousel',
    props: {
      title: '客户成功案例',
      testimonials: [
        {
          customer: '张总 - 科技公司CEO',
          content: 'FlexiHub让我们快速搭建了官网，节省了大量开发成本',
          rating: 5,
        },
      ],
    },
  },
};
```

## 🎨 官网页面结构

### 1. 首页设计

```vue
<!-- public-portal/src/views/OfficialHome.vue -->
<template>
  <div class="official-home">
    <!-- 导航栏 -->
    <OfficialNavbar />

    <!-- 英雄区域 -->
    <HeroSection
      title="FlexiHub企业级建站平台"
      subtitle="一键注册，即刻建站，专为中国企业设计"
      :cta-buttons="[
        { text: '免费注册', type: 'primary', action: 'register' },
        { text: '产品演示', type: 'secondary', action: 'demo' },
      ]"
    />

    <!-- 核心功能 -->
    <FeaturesSection :features="coreFeatures" />

    <!-- 产品优势 -->
    <AdvantagesSection />

    <!-- 价格套餐 -->
    <PricingSection />

    <!-- 成功案例 -->
    <CaseStudiesSection />

    <!-- 注册转化 -->
    <RegistrationCTASection />

    <!-- 页脚 -->
    <OfficialFooter />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const coreFeatures = ref([
  {
    icon: '🚀',
    title: '5分钟极速建站',
    description: '无需技术背景，选择模板即可快速搭建专业网站',
    demo: 'https://demo.flexihub.com/quick-build',
  },
  {
    icon: '🛡️',
    title: '企业级安全',
    description: '银行级加密，数据隔离，符合等保要求',
    demo: 'https://demo.flexihub.com/security',
  },
  {
    icon: '📊',
    title: '智能数据分析',
    description: '实时访问统计，用户行为分析，助力业务决策',
    demo: 'https://demo.flexihub.com/analytics',
  },
  {
    icon: '🎨',
    title: '拖拽式编辑器',
    description: '可视化编辑，所见即所得，轻松定制网站风格',
    demo: 'https://demo.flexihub.com/editor',
  },
]);
</script>
```

### 2. 产品页面

```vue
<!-- public-portal/src/views/ProductFeatures.vue -->
<template>
  <div class="product-features">
    <ProductHero />

    <!-- 功能详情 -->
    <div class="features-detail">
      <FeatureCard
        v-for="feature in productFeatures"
        :key="feature.id"
        :feature="feature"
        :show-demo="true"
      />
    </div>

    <!-- 技术架构 -->
    <TechnicalArchitecture />

    <!-- API文档 -->
    <ApiDocumentation />
  </div>
</template>
```

## 🔧 技术实现

### 1. 官网内容管理

```typescript
// src/modules/official/official-content.service.ts
@Injectable()
export class OfficialContentService {
  /**
   * 获取官网页面内容
   */
  async getPageContent(pageType: string): Promise<PageContent> {
    const content = await this.contentRepository.findOne({
      where: {
        websiteId: 0, // 系统官网ID
        pageType,
        status: 'published',
      },
    });

    return this.processContent(content);
  }

  /**
   * 更新官网内容（仅系统管理员）
   */
  async updateOfficialContent(pageType: string, content: any, adminId: number): Promise<void> {
    // 验证系统管理员权限
    await this.validateSystemAdmin(adminId);

    await this.contentRepository.upsert({
      websiteId: 0,
      pageType,
      content,
      updatedBy: adminId,
      updatedAt: new Date(),
    });

    // 清理CDN缓存
    await this.cdnService.purgeCache(`/official/${pageType}`);
  }
}
```

### 2. SEO优化

```typescript
// src/modules/website/seo/official-seo.service.ts
@Injectable()
export class OfficialSeoService {
  async generateSeoMeta(page: string): Promise<SeoMeta> {
    const seoConfig = {
      home: {
        title: 'FlexiHub - 企业级多租户建站平台 | 专业SaaS解决方案',
        description:
          '专为中国企业设计的建站平台，5分钟快速搭建专业网站，企业级安全保障，支持自定义域名。立即注册免费试用！',
        keywords: '企业建站,SaaS建站,多租户建站,网站制作,企业官网',
        canonical: 'https://www.flexihub.com',
      },
      features: {
        title: '产品功能 - FlexiHub建站平台',
        description:
          '了解FlexiHub的强大功能：拖拽式编辑器、智能SEO、数据分析、多语言支持等企业级特性',
        keywords: '建站功能,网站编辑器,SEO优化,数据分析',
      },
      pricing: {
        title: '价格套餐 - FlexiHub建站平台',
        description: '查看FlexiHub灵活的价格方案，从免费版到企业版，总有适合您的建站解决方案',
        keywords: '建站价格,套餐选择,企业版价格',
      },
    };

    return seoConfig[page] || seoConfig.home;
  }
}
```

## 📱 移动端适配

### 响应式设计

```vue
<!-- 移动端优化的官网组件 -->
<template>
  <div class="mobile-hero">
    <!-- 移动端专用布局 -->
    <div class="mobile-content">
      <h1 class="mobile-title">FlexiHub建站平台</h1>
      <p class="mobile-subtitle">让每个企业都有专业官网</p>

      <!-- 移动端CTA -->
      <div class="mobile-cta">
        <button @click="showRegistrationModal" class="cta-primary">立即注册</button>
        <button @click="watchDemo" class="cta-secondary">观看演示</button>
      </div>
    </div>

    <!-- 手机预览 -->
    <div class="phone-preview">
      <PhoneMockup>
        <WebsitePreview :demo-site="demoSite" />
      </PhoneMockup>
    </div>
  </div>
</template>
```

## 🚀 部署和CDN优化

### 1. 静态资源优化

```typescript
// 官网静态资源配置
export const OfficialAssetConfig = {
  // 图片压缩和格式优化
  images: {
    hero: {
      webp: '/assets/hero-bg.webp',
      jpg: '/assets/hero-bg.jpg',
      sizes: ['1920x1080', '1366x768', '768x432'],
    },
    features: {
      format: 'svg', // 优先使用SVG图标
      fallback: 'png',
    },
  },

  // CDN配置
  cdn: {
    domain: 'cdn.flexihub.com',
    regions: ['cn-beijing', 'cn-shanghai', 'cn-guangzhou'],
    cache: {
      images: '30d',
      css: '7d',
      js: '7d',
    },
  },
};
```

### 2. 性能监控

```typescript
// 官网性能监控
export class OfficialPerformanceMonitor {
  async trackPageLoad(page: string): Promise<void> {
    const metrics = {
      page,
      timestamp: Date.now(),
      loadTime: performance.now(),
      resourceTiming: performance.getEntriesByType('resource'),
      userAgent: navigator.userAgent,
    };

    await this.analyticsService.track('official_page_load', metrics);
  }

  async trackConversion(action: string): Promise<void> {
    await this.analyticsService.track('official_conversion', {
      action,
      timestamp: Date.now(),
      referrer: document.referrer,
    });
  }
}
```

## 🎯 营销集成

### 1. 注册转化漏斗

```typescript
// 官网到注册的转化追踪
export class ConversionTracker {
  trackVisitor(source: string): void {
    // 设置访客标识
    const visitorId = this.generateVisitorId();
    localStorage.setItem('flexihub_visitor', visitorId);

    // 记录来源
    this.analyticsService.track('visitor_arrival', {
      visitorId,
      source,
      page: window.location.pathname,
    });
  }

  trackRegistrationIntent(): void {
    // 用户点击注册按钮
    const visitorId = localStorage.getItem('flexihub_visitor');
    this.analyticsService.track('registration_intent', {
      visitorId,
      timestamp: Date.now(),
    });
  }

  trackRegistrationComplete(tenantId: number): void {
    // 注册完成
    const visitorId = localStorage.getItem('flexihub_visitor');
    this.analyticsService.track('registration_complete', {
      visitorId,
      tenantId,
      timestamp: Date.now(),
    });
  }
}
```

## 📊 优势分析

### 1. 产品展示优势

- ✅ **真实产品演示** - 官网本身就是产品能力的最佳展示
- ✅ **成本效益** - 无需额外开发和维护官网技术栈
- ✅ **一致性体验** - 用户从官网到产品使用的无缝体验
- ✅ **快速迭代** - 产品功能更新自动同步到官网

### 2. 技术优势

- ✅ **SEO友好** - 内置SEO优化功能直接应用到官网
- ✅ **性能优化** - 享受建站平台的所有性能优化
- ✅ **安全保障** - 与产品相同的安全级别
- ✅ **移动适配** - 自动适配各种设备

### 3. 业务优势

- ✅ **降低成本** - 减少官网开发和维护成本
- ✅ **提升转化** - 更直观的产品体验提升注册转化率
- ✅ **品牌一致** - 官网和产品体验完全一致
- ✅ **快速响应** - 市场变化可以快速反映到官网

这个方案将FlexiHub官网变成产品能力的最佳展示窗口，既降低了维护成本，又提供了最直观的产品体验！
