# SaaS建站系统开发指导

## 项目概述

本项目是一个企业级多租户SaaS系统的后端基础架构，采用NestJS框架开发。当前已实现完整的多租户基础设施、用户权限管理、支付系统等核心功能，为后续的网站建设功能提供了坚实的技术基础。

## 技术架构

### 核心技术栈

- **框架**: NestJS (Node.js)
- **数据库**: PostgreSQL
- **ORM**: Prisma
- **认证**: JWT
- **缓存**: Redis
- **API文档**: Swagger

### 多租户架构设计

#### 数据库分离策略

```typescript
// 公共数据库：存储租户元数据
// prisma/public-schema.prisma
model Tenant {
  id           Int         @id @default(autoincrement())
  code         String      @unique
  name         String
  domain       String?     @unique
  datasource   Datasource? @relation(fields: [datasourceId], references: [id])
}

// 租户数据库：存储业务数据
// prisma/schema.prisma
model User {
  id           Int      @id @default(autoincrement())
  username     String
  tenantId     Int      // 租户隔离字段
}
```

#### 数据隔离机制

```typescript
// 自动添加租户过滤条件
withQueryExtensions(tenantId: number) {
  return this.$extends({
    query: {
      $allOperations({ query }) {
        return query({ where: { tenantId } });
      },
    },
  });
}
```

## 已实现功能模块

### 1. 认证与授权系统

- **JWT认证**: 支持系统用户和租户用户统一认证
- **权限控制**: 基于角色的权限管理(RBAC)
- **多租户识别**: 通过域名或请求头识别租户

### 2. 用户管理系统

- **用户CRUD**: 完整的用户生命周期管理
- **角色分配**: 灵活的角色权限分配
- **密码管理**: 安全的密码重置和修改

### 3. 菜单权限系统

- **动态菜单**: 基于权限的动态菜单生成
- **菜单管理**: 支持多层级菜单结构
- **权限控制**: 细粒度的功能权限控制

### 4. 租户管理系统

- **租户CRUD**: 租户的创建、更新、删除
- **功能模板**: 预定义的功能权限模板
- **配置管理**: 租户级别的配置管理

### 5. 支付系统

- **订单管理**: 完整的支付订单流程
- **多支付方式**: 支持支付宝、微信等
- **退款处理**: 完整的退款申请和处理流程

### 6. 会员系统

- **会员计划**: 多层级会员计划管理
- **虚拟币**: 虚拟货币系统
- **权益管理**: 会员权益和配额管理

## 开发规范

### 代码组织结构

```
src/
├── core/                   # 核心基础设施
│   ├── auth/              # 认证相关
│   ├── common/            # 通用组件
│   ├── decorators/        # 装饰器
│   └── guards/            # 守卫
├── modules/               # 业务模块
│   ├── user/             # 用户模块
│   ├── tenant/           # 租户模块
│   ├── payment/          # 支付模块
│   └── membership/       # 会员模块
└── config/               # 配置文件
```

### 编码规范

1. **遵循SOLID原则**: 单一职责、开闭原则等
2. **DRY原则**: 避免代码重复
3. **KISS原则**: 保持简单直接
4. **统一命名**: 使用清晰的命名约定

### 数据库设计规范

1. **租户隔离**: 所有业务表必须包含tenantId字段
2. **索引优化**: 为查询字段添加适当索引
3. **数据完整性**: 使用外键约束保证数据一致性

## 网站建设功能开发指导

基于当前架构，网站建设功能应该按以下方式开发：

### 1. 网站模板系统

```typescript
// 建议的数据模型
model WebsiteTemplate {
  id           Int      @id @default(autoincrement())
  name         String   // 模板名称
  category     String   // 行业分类
  preview      String   // 预览图
  config       Json     // 模板配置
  components   Json     // 组件配置
  tenantId     Int      // 租户隔离
}
```

### 2. 页面编辑器

```typescript
// 页面数据模型
model WebsitePage {
  id           Int      @id @default(autoincrement())
  title        String   // 页面标题
  path         String   // 页面路径
  content      Json     // 页面内容(组件树)
  seoConfig    Json?    // SEO配置
  status       String   // 发布状态
  websiteId    Int      // 所属网站
  tenantId     Int      // 租户隔离
}
```

### 3. 组件系统

```typescript
// 组件库模型
model ComponentLibrary {
  id           Int      @id @default(autoincrement())
  name         String   // 组件名称
  type         String   // 组件类型
  config       Json     // 组件配置
  template     String   // 组件模板
  category     String   // 组件分类
  tenantId     Int      // 租户隔离
}
```

### 4. 内容管理

```typescript
// 内容管理模型
model WebsiteContent {
  id           Int      @id @default(autoincrement())
  title        String   // 内容标题
  type         String   // 内容类型(文章、产品等)
  content      Json     // 内容数据
  metadata     Json?    // 元数据
  status       String   // 发布状态
  websiteId    Int      // 所属网站
  tenantId     Int      // 租户隔离
}
```

## 开发流程

### 1. 功能开发流程

1. **需求分析**: 明确功能需求和技术要求
2. **数据建模**: 设计数据库模型和关系
3. **API设计**: 定义RESTful API接口
4. **业务逻辑**: 实现核心业务逻辑
5. **测试验证**: 编写单元测试和集成测试
6. **文档更新**: 同步更新API文档

### 2. 代码提交规范

```bash
# 提交格式
<type>(<scope>): <subject>

# 示例
feat(website): add template management module
fix(payment): resolve order status update issue
docs(api): update website builder API documentation
```

### 3. 分支管理

- **main**: 主分支，稳定版本
- **develop**: 开发分支，功能集成
- **feature/\***: 功能分支，新功能开发
- **hotfix/\***: 热修复分支，紧急修复

## 性能优化指导

### 1. 数据库优化

- 合理使用索引
- 避免N+1查询问题
- 使用数据库连接池

### 2. 缓存策略

```typescript
// Redis缓存示例
@Injectable()
export class CacheService {
  async getOrSet<T>(key: string, factory: () => Promise<T>, ttl = 3600): Promise<T> {
    const cached = await this.redis.get(key);
    if (cached) return JSON.parse(cached);

    const data = await factory();
    await this.redis.setex(key, ttl, JSON.stringify(data));
    return data;
  }
}
```

### 3. API优化

- 实现分页查询
- 使用字段选择减少数据传输
- 合理使用HTTP缓存头

## 安全考虑

### 1. 数据安全

- 敏感数据加密存储
- SQL注入防护
- XSS攻击防护

### 2. 访问控制

- JWT令牌安全
- 权限验证
- 租户数据隔离

### 3. API安全

- 请求频率限制
- 输入数据验证
- 错误信息脱敏

## 测试策略

### 1. 单元测试

```typescript
describe('UserService', () => {
  it('should create user successfully', async () => {
    const userData = { username: 'test', email: '<EMAIL>' };
    const result = await userService.create(userData, tenantId);
    expect(result).toBeDefined();
    expect(result.username).toBe('test');
  });
});
```

### 2. 集成测试

- API接口测试
- 数据库集成测试
- 第三方服务集成测试

### 3. E2E测试

- 完整业务流程测试
- 用户场景测试
- 性能测试

## 部署指导

### 1. 环境配置

```bash
# 环境变量示例
DATABASE_URL="postgresql://user:password@localhost:5432/saas_db"
PUBLIC_DATABASE_URL="postgresql://user:password@localhost:5432/public_db"
JWT_SECRET="your-jwt-secret"
REDIS_URL="redis://localhost:6379"
```

### 2. Docker部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "start:prod"]
```

### 3. 数据库迁移

```bash
# 生成迁移文件
npx prisma migrate dev --name init

# 应用迁移
npx prisma migrate deploy

# 数据初始化
npm run db:seed
```

## 监控与维护

### 1. 日志管理

- 结构化日志记录
- 错误日志监控
- 性能日志分析

### 2. 健康检查

```typescript
@Controller('health')
export class HealthController {
  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.db.pingCheck('database'),
      () => this.redis.pingCheck('redis'),
    ]);
  }
}
```

### 3. 性能监控

- API响应时间监控
- 数据库查询性能
- 内存使用情况

## 扩展指导

### 1. 微服务拆分

当系统规模增长时，可以考虑按业务域拆分微服务：

- 用户服务
- 网站建设服务
- 支付服务
- 内容管理服务

### 2. 消息队列

对于异步处理需求，可以引入消息队列：

```typescript
// 事件发布示例
this.eventEmitter.emit('website.published', {
  websiteId,
  tenantId,
  publishTime: new Date(),
});
```

### 3. 第三方集成

- CDN服务集成
- 邮件服务集成
- 短信服务集成
- 支付网关集成

## 注意事项

1. **租户隔离**: 所有业务逻辑必须考虑租户隔离
2. **性能优化**: 大量数据处理时注意性能优化
3. **安全防护**: 始终考虑安全威胁和防护措施
4. **向后兼容**: API变更时保持向后兼容性
5. **文档同步**: 代码变更时及时更新文档

## 技术债务管理

1. **定期重构**: 识别和重构技术债务
2. **代码审查**: 通过代码审查保证代码质量
3. **性能测试**: 定期进行性能测试和优化
4. **依赖更新**: 及时更新依赖包和安全补丁
