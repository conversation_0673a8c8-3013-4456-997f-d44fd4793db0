# 系统管理员角色设计

## 🎯 角色定位：系统管理员比以往更重要

在新的多租户SaaS架构中，系统管理员不仅继续存在，而且扮演着**更加关键的角色**。他们是整个平台的守护者和运营者。

## 👥 用户角色层级

### 三层用户架构

```
🏛️ 系统层
├── 超级管理员 (Super Admin)     # 平台所有者
├── 系统管理员 (System Admin)    # 平台运营者
└── 系统运维员 (System Operator) # 技术支持

🏢 租户层
├── 租户管理员 (Tenant Admin)    # 租户所有者
├── 租户用户 (Tenant User)       # 普通用户
└── 租户访客 (Tenant Guest)      # 临时访问

🌐 公开层
└── 匿名访客 (Anonymous)         # 未注册用户
```

## 🔐 系统管理员权限体系

### 1. 核心权限设计

```typescript
// src/core/auth/roles/system-admin.role.ts
export const SystemAdminPermissions = {
  // 平台管理
  PLATFORM: {
    VIEW_DASHBOARD: 'platform:dashboard:view',
    MANAGE_SETTINGS: 'platform:settings:manage',
    VIEW_ANALYTICS: 'platform:analytics:view',
    MANAGE_ANNOUNCEMENTS: 'platform:announcements:manage',
  },

  // 租户管理
  TENANTS: {
    VIEW_ALL: 'tenants:view:all',
    CREATE: 'tenants:create',
    UPDATE: 'tenants:update',
    DELETE: 'tenants:delete',
    SUSPEND: 'tenants:suspend',
    ACTIVATE: 'tenants:activate',
    VIEW_DETAILS: 'tenants:details:view',
  },

  // 订阅计划管理
  SUBSCRIPTIONS: {
    MANAGE_PLANS: 'subscriptions:plans:manage',
    UPDATE_TENANT_PLAN: 'subscriptions:tenant:update',
    VIEW_BILLING: 'subscriptions:billing:view',
    PROCESS_REFUNDS: 'subscriptions:refunds:process',
  },

  // 用户管理
  USERS: {
    VIEW_ALL_TENANTS: 'users:view:all-tenants',
    IMPERSONATE: 'users:impersonate',
    RESET_PASSWORD: 'users:password:reset',
    MANAGE_ROLES: 'users:roles:manage',
  },

  // 系统监控
  MONITORING: {
    VIEW_SYSTEM_METRICS: 'monitoring:metrics:view',
    VIEW_PERFORMANCE: 'monitoring:performance:view',
    MANAGE_ALERTS: 'monitoring:alerts:manage',
    ACCESS_LOGS: 'monitoring:logs:access',
  },

  // 安全管理
  SECURITY: {
    VIEW_AUDIT_LOGS: 'security:audit:view',
    MANAGE_IP_WHITELIST: 'security:ip:manage',
    MANAGE_ENCRYPTION: 'security:encryption:manage',
    EMERGENCY_ACCESS: 'security:emergency:access',
  },
};
```

### 2. 权限验证装饰器

```typescript
// src/core/auth/decorators/system-admin.decorator.ts
export function RequireSystemAdmin(permission?: string) {
  return applyDecorators(
    UseGuards(JwtAuthGuard, SystemAdminGuard),
    Permissions(permission || 'system:admin'),
    ApiOperation({ security: [{ 'system-admin': [] }] }),
  );
}

// 使用示例
@Controller('system/tenants')
export class SystemTenantController {
  @Get()
  @RequireSystemAdmin(SystemAdminPermissions.TENANTS.VIEW_ALL)
  async getAllTenants() {
    // 只有系统管理员可以查看所有租户
  }

  @Post(':id/suspend')
  @RequireSystemAdmin(SystemAdminPermissions.TENANTS.SUSPEND)
  async suspendTenant(@Param('id') id: number) {
    // 只有系统管理员可以暂停租户
  }
}
```

## 🏢 系统管理后台设计

### 1. 管理后台架构

```typescript
// 系统管理后台独立架构
interface SystemAdminDashboard {
  // 平台概览
  overview: {
    totalTenants: number;
    activeTenants: number;
    revenue: number;
    systemHealth: 'healthy' | 'warning' | 'critical';
  };

  // 租户管理
  tenantManagement: {
    tenantList: TenantWithMetrics[];
    registrationQueue: PendingRegistration[];
    suspendedTenants: SuspendedTenant[];
  };

  // 订阅管理
  subscriptionManagement: {
    planUsage: PlanUsageStats[];
    revenueAnalytics: RevenueData[];
    billingIssues: BillingIssue[];
  };

  // 系统监控
  systemMonitoring: {
    performance: PerformanceMetrics;
    alerts: SystemAlert[];
    logs: SystemLog[];
  };
}
```

### 2. 租户管理功能

```vue
<!-- system-admin/src/views/TenantManagement.vue -->
<template>
  <div class="tenant-management">
    <!-- 租户总览 -->
    <div class="tenant-overview">
      <StatCard title="总租户数" :value="stats.total" />
      <StatCard title="活跃租户" :value="stats.active" />
      <StatCard title="待审核" :value="stats.pending" />
      <StatCard title="已暂停" :value="stats.suspended" />
    </div>

    <!-- 租户列表 -->
    <div class="tenant-list">
      <el-table :data="tenants" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="code" label="租户代码" />
        <el-table-column prop="name" label="企业名称" />
        <el-table-column prop="plan" label="订阅计划">
          <template #default="scope">
            <el-tag :type="getPlanType(scope.row.plan)">
              {{ scope.row.plan }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="registrationSource" label="注册来源">
          <template #default="scope">
            <el-tag v-if="scope.row.registrationSource === 'self-service'" type="success">
              自助注册
            </el-tag>
            <el-tag v-else type="info">手动创建</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="usage" label="使用情况">
          <template #default="scope">
            <UsageIndicator :usage="scope.row.usage" :limits="scope.row.limits" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300">
          <template #default="scope">
            <el-button size="small" @click="viewTenantDetails(scope.row)"> 详情 </el-button>
            <el-button size="small" @click="impersonateTenant(scope.row)"> 登录 </el-button>
            <el-button size="small" type="warning" @click="updateSubscription(scope.row)">
              变更套餐
            </el-button>
            <el-button
              size="small"
              :type="scope.row.status === 1 ? 'danger' : 'success'"
              @click="toggleTenantStatus(scope.row)"
            >
              {{ scope.row.status === 1 ? '暂停' : '激活' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
```

## 📊 系统管理功能

### 1. 平台监控面板

```typescript
// src/modules/system-admin/dashboard/dashboard.service.ts
@Injectable()
export class SystemDashboardService {
  /**
   * 获取平台总览数据
   */
  async getPlatformOverview(): Promise<PlatformOverview> {
    const [tenantStats, revenueStats, performanceStats, systemHealth] = await Promise.all([
      this.getTenantStatistics(),
      this.getRevenueStatistics(),
      this.getPerformanceStatistics(),
      this.getSystemHealth(),
    ]);

    return {
      tenants: tenantStats,
      revenue: revenueStats,
      performance: performanceStats,
      health: systemHealth,
      alerts: await this.getActiveAlerts(),
    };
  }

  /**
   * 获取租户统计信息
   */
  private async getTenantStatistics(): Promise<TenantStats> {
    const today = new Date();
    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    return {
      total: await this.tenantRepository.count(),
      active: await this.tenantRepository.count({ where: { status: 1 } }),
      newThisMonth: await this.tenantRepository.count({
        where: { createdAt: { gte: lastMonth } },
      }),
      byPlan: await this.getSubscriptionStats(),
      bySource: await this.getRegistrationSourceStats(),
    };
  }
}
```

### 2. 订阅计划管理

```typescript
// src/modules/system-admin/subscription/plan-management.service.ts
@Injectable()
export class PlanManagementService {
  /**
   * 创建新的订阅计划
   */
  async createSubscriptionPlan(
    planData: CreateSubscriptionPlanDto,
    adminId: number,
  ): Promise<SubscriptionPlan> {
    // 验证系统管理员权限
    await this.validateSystemAdminPermission(adminId, 'subscriptions:plans:create');

    const plan = await this.subscriptionPlanRepository.create({
      ...planData,
      createdBy: adminId,
      isActive: true,
    });

    // 记录操作日志
    await this.auditService.log({
      adminId,
      action: 'CREATE_SUBSCRIPTION_PLAN',
      resource: 'SubscriptionPlan',
      resourceId: plan.id,
      details: planData,
    });

    return plan;
  }

  /**
   * 批量更新租户订阅计划
   */
  async batchUpdateTenantPlans(
    tenantIds: number[],
    newPlanId: number,
    adminId: number,
  ): Promise<BatchUpdateResult> {
    const results = [];

    for (const tenantId of tenantIds) {
      try {
        await this.updateTenantSubscription(tenantId, newPlanId, adminId);
        results.push({ tenantId, success: true });
      } catch (error) {
        results.push({ tenantId, success: false, error: error.message });
      }
    }

    return {
      total: tenantIds.length,
      success: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      details: results,
    };
  }
}
```

### 3. 安全管理

```typescript
// src/modules/system-admin/security/security-management.service.ts
@Injectable()
export class SecurityManagementService {
  /**
   * 查看系统审计日志
   */
  async getAuditLogs(query: AuditLogQuery, adminId: number): Promise<PaginatedAuditLogs> {
    await this.validateSystemAdminPermission(adminId, 'security:audit:view');

    const filters = this.buildAuditLogFilters(query);

    return this.auditLogRepository.findWithPagination(filters, {
      page: query.page || 1,
      pageSize: query.pageSize || 50,
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * 紧急访问（用于处理紧急情况）
   */
  async emergencyAccess(
    tenantId: number,
    reason: string,
    adminId: number,
  ): Promise<EmergencyAccessToken> {
    await this.validateSystemAdminPermission(adminId, 'security:emergency:access');

    // 创建紧急访问令牌
    const token = this.jwtService.sign({
      adminId,
      tenantId,
      type: 'emergency_access',
      reason,
      expiresAt: Date.now() + 60 * 60 * 1000, // 1小时
    });

    // 记录紧急访问日志
    await this.auditService.log({
      adminId,
      action: 'EMERGENCY_ACCESS',
      resource: 'Tenant',
      resourceId: tenantId,
      details: { reason, tokenId: token.substring(0, 8) },
    });

    // 发送告警通知
    await this.alertService.sendEmergencyAccessAlert({
      adminId,
      tenantId,
      reason,
      timestamp: new Date(),
    });

    return { token, expiresAt: new Date(Date.now() + 60 * 60 * 1000) };
  }
}
```

## 🔄 租户代理登录

### 技术支持功能

```typescript
// src/modules/system-admin/impersonation/impersonation.service.ts
@Injectable()
export class TenantImpersonationService {
  /**
   * 系统管理员代理登录租户
   */
  async impersonateTenant(
    tenantId: number,
    adminId: number,
    reason: string,
  ): Promise<ImpersonationToken> {
    // 验证权限
    await this.validateSystemAdminPermission(adminId, 'users:impersonate');

    // 获取租户信息
    const tenant = await this.tenantService.findById(tenantId);
    if (!tenant) {
      throw new NotFoundException('Tenant not found');
    }

    // 获取租户管理员
    const tenantAdmin = await this.userService.getTenantAdmin(tenantId);

    // 创建代理访问令牌
    const impersonationToken = this.jwtService.sign(
      {
        sub: tenantAdmin.id,
        username: tenantAdmin.username,
        userType: 'TENANT',
        tenantId: tenantId,
        impersonatedBy: adminId,
        impersonationReason: reason,
        isImpersonation: true,
      },
      { expiresIn: '2h' },
    ); // 2小时有效期

    // 记录代理登录日志
    await this.auditService.log({
      adminId,
      action: 'IMPERSONATE_TENANT',
      resource: 'Tenant',
      resourceId: tenantId,
      details: {
        tenantCode: tenant.code,
        tenantAdminId: tenantAdmin.id,
        reason,
      },
    });

    return {
      token: impersonationToken,
      tenantInfo: {
        id: tenant.id,
        code: tenant.code,
        name: tenant.name,
      },
      adminInfo: {
        id: tenantAdmin.id,
        username: tenantAdmin.username,
        realName: tenantAdmin.realName,
      },
      expiresAt: new Date(Date.now() + 2 * 60 * 60 * 1000),
    };
  }
}
```

## 🚨 系统监控和告警

### 智能监控系统

```typescript
// src/modules/system-admin/monitoring/system-monitor.service.ts
@Injectable()
export class SystemMonitorService {
  @Cron('*/5 * * * *') // 每5分钟执行
  async monitorSystemHealth(): Promise<void> {
    const metrics = await this.collectSystemMetrics();

    // 检查各项指标
    await Promise.all([
      this.checkDatabaseHealth(metrics.database),
      this.checkMemoryUsage(metrics.memory),
      this.checkDiskSpace(metrics.disk),
      this.checkResponseTime(metrics.performance),
      this.checkErrorRates(metrics.errors),
    ]);
  }

  private async checkDatabaseHealth(dbMetrics: DatabaseMetrics): Promise<void> {
    if (dbMetrics.connectionPool.active > dbMetrics.connectionPool.max * 0.9) {
      await this.sendAlert({
        type: 'DATABASE_CONNECTIONS_HIGH',
        severity: 'warning',
        message: '数据库连接池使用率过高',
        metrics: dbMetrics,
      });
    }
  }

  private async sendAlert(alert: SystemAlert): Promise<void> {
    // 发送给系统管理员
    const systemAdmins = await this.userService.getSystemAdmins();

    for (const admin of systemAdmins) {
      // 邮件通知
      await this.emailService.sendAlert(admin.email, alert);

      // 短信通知（紧急情况）
      if (alert.severity === 'critical') {
        await this.smsService.sendAlert(admin.phoneNumber, alert);
      }

      // 推送到管理后台
      await this.websocketService.sendToAdmin(admin.id, {
        type: 'system_alert',
        data: alert,
      });
    }
  }
}
```

## 📈 数据分析和报告

### 运营数据分析

```typescript
// src/modules/system-admin/analytics/platform-analytics.service.ts
@Injectable()
export class PlatformAnalyticsService {
  /**
   * 生成平台运营报告
   */
  async generateOperationalReport(
    period: 'daily' | 'weekly' | 'monthly',
    adminId: number,
  ): Promise<OperationalReport> {
    await this.validateSystemAdminPermission(adminId, 'platform:analytics:view');

    const dateRange = this.getDateRange(period);

    const [tenantGrowth, revenueAnalysis, usageStatistics, performanceMetrics] = await Promise.all([
      this.analyzeTenantGrowth(dateRange),
      this.analyzeRevenue(dateRange),
      this.analyzeUsage(dateRange),
      this.analyzePerformance(dateRange),
    ]);

    const report = {
      period,
      dateRange,
      summary: {
        newTenants: tenantGrowth.newCount,
        totalRevenue: revenueAnalysis.total,
        avgResponseTime: performanceMetrics.avgResponseTime,
        uptime: performanceMetrics.uptime,
      },
      tenantGrowth,
      revenueAnalysis,
      usageStatistics,
      performanceMetrics,
      recommendations: await this.generateRecommendations({
        tenantGrowth,
        revenueAnalysis,
        usageStatistics,
      }),
    };

    // 缓存报告
    await this.cacheService.set(`report:${period}:${dateRange.start}`, report, 3600);

    return report;
  }
}
```

## 🎯 总结

### 系统管理员的核心价值

1. **平台守护者** - 确保整个SaaS平台的稳定运行
2. **业务运营者** - 管理租户、订阅、收入等核心业务
3. **安全管理者** - 维护平台安全，处理安全事件
4. **技术支持者** - 为租户提供技术支持和问题解决
5. **数据分析师** - 分析平台数据，优化运营策略

### 与租户管理员的区别

| 维度         | 系统管理员 | 租户管理员 |
| ------------ | ---------- | ---------- |
| **权限范围** | 整个平台   | 单个租户   |
| **管理对象** | 所有租户   | 租户内用户 |
| **访问数据** | 全平台数据 | 租户数据   |
| **安全级别** | 最高权限   | 租户权限   |
| **责任范围** | 平台运营   | 租户业务   |

**系统管理员在新架构中不仅继续存在，而且变得更加重要！**他们是SaaS平台成功运营的关键角色，承担着平台的稳定性、安全性和盈利性的重要责任。
