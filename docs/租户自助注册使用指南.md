# 租户自助注册使用指南

## 📋 概述

本指南介绍如何使用FlexiHub多租户SaaS建站系统的全自动化租户注册功能。通过这个功能，用户可以完全自助完成租户注册、系统初始化和管理员账户创建。

## 🚀 核心功能

### 1. 全自动化注册流程

- ✅ 租户代码和域名可用性检查
- ✅ 自动创建租户数据库Schema
- ✅ 自动创建管理员账户和默认角色权限
- ✅ 自动分配订阅计划和功能权限
- ✅ 自动发送欢迎邮件和验证链接

### 2. 多层级安全保障

- ✅ 数据逻辑隔离
- ✅ 租户专属加密密钥
- ✅ 行级安全策略
- ✅ 操作审计日志

### 3. 订阅计划自动管理

- ✅ 免费版、基础版、专业版、企业版
- ✅ 功能权限自动分配
- ✅ 使用配额自动管理
- ✅ 自动续费和升级支持

## 🔧 API接口说明

### 1. 检查可用性

```http
GET /api/public/tenant/check-availability?code=my-company&domain=my-company.com
```

**响应示例：**

```json
{
  "codeAvailable": true,
  "domainAvailable": true,
  "suggestions": []
}
```

### 2. 获取订阅计划

```http
GET /api/public/tenant/subscription-plans
```

**响应示例：**

```json
{
  "plans": [
    {
      "code": "free",
      "name": "免费版",
      "description": "适合个人用户",
      "price": 0,
      "features": ["1个网站", "基础模板", "社区支持"],
      "limits": {
        "websites": 1,
        "storage": 100,
        "bandwidth": 1000,
        "users": 1,
        "domains": 0
      }
    }
  ]
}
```

### 3. 注册租户

```http
POST /api/public/tenant/register
Content-Type: application/json

{
  "tenantCode": "my-company",
  "tenantName": "我的公司",
  "customDomain": "my-company.com",
  "companyInfo": {
    "companyName": "我的公司有限公司",
    "companySize": "10-50人",
    "industry": "软件开发",
    "website": "https://my-company.com"
  },
  "adminUser": {
    "username": "admin",
    "password": "SecurePassword123",
    "email": "<EMAIL>",
    "realName": "管理员",
    "phoneNumber": "13800138000"
  },
  "subscriptionPlan": "basic",
  "agreeToTerms": true
}
```

**响应示例：**

```json
{
  "success": true,
  "tenantId": 123,
  "tenantCode": "my-company",
  "accessUrl": "https://my-company.com",
  "adminToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "nextSteps": ["使用管理员账号登录系统", "完善公司信息和系统配置", "创建您的第一个网站"]
}
```

### 4. 验证邮箱

```http
POST /api/public/tenant/verify
Content-Type: application/json

{
  "tenantCode": "my-company",
  "verificationCode": "ABC123"
}
```

### 5. 查询注册状态

```http
GET /api/public/tenant/registration-status?code=my-company
```

**响应示例：**

```json
{
  "status": "active",
  "progress": 100,
  "nextStep": null
}
```

## 💻 前端集成示例

### React示例

```tsx
import React, { useState } from 'react';
import axios from 'axios';

const TenantRegistration: React.FC = () => {
  const [formData, setFormData] = useState({
    tenantCode: '',
    tenantName: '',
    customDomain: '',
    companyInfo: {
      companyName: '',
      companySize: '',
      industry: '',
      website: '',
    },
    adminUser: {
      username: '',
      password: '',
      email: '',
      realName: '',
      phoneNumber: '',
    },
    subscriptionPlan: 'free',
  });

  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);

  const checkAvailability = async (code: string) => {
    try {
      const response = await axios.get('/api/public/tenant/check-availability', {
        params: { code },
      });
      return response.data;
    } catch (error) {
      console.error('检查可用性失败:', error);
      return null;
    }
  };

  const submitRegistration = async () => {
    setLoading(true);
    try {
      const response = await axios.post('/api/public/tenant/register', formData);

      if (response.data.success) {
        // 注册成功，跳转到成功页面或显示令牌
        window.location.href = response.data.accessUrl;
      }
    } catch (error) {
      console.error('注册失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="registration-wizard">
      {step === 1 && (
        <BasicInfoStep
          data={formData}
          onChange={setFormData}
          onNext={() => setStep(2)}
          checkAvailability={checkAvailability}
        />
      )}

      {step === 2 && (
        <PlanSelectionStep
          selectedPlan={formData.subscriptionPlan}
          onSelect={plan => {
            setFormData(prev => ({ ...prev, subscriptionPlan: plan }));
            setStep(3);
          }}
        />
      )}

      {step === 3 && (
        <ConfirmationStep data={formData} onSubmit={submitRegistration} loading={loading} />
      )}
    </div>
  );
};
```

### Vue示例

```vue
<template>
  <div class="tenant-registration">
    <el-steps :active="currentStep" align-center>
      <el-step title="基本信息"></el-step>
      <el-step title="选择套餐"></el-step>
      <el-step title="完成注册"></el-step>
    </el-steps>

    <el-form v-if="currentStep === 0" :model="formData" ref="basicForm" :rules="basicRules">
      <el-form-item label="租户代码" prop="tenantCode">
        <el-input
          v-model="formData.tenantCode"
          @blur="checkAvailability"
          placeholder="请输入租户代码"
        />
        <div v-if="!codeAvailable" class="error-text">
          租户代码不可用，建议使用: {{ suggestions.join(', ') }}
        </div>
      </el-form-item>

      <el-form-item label="公司名称" prop="tenantName">
        <el-input v-model="formData.tenantName" />
      </el-form-item>

      <!-- 更多表单字段... -->

      <el-button type="primary" @click="nextStep">下一步</el-button>
    </el-form>

    <!-- 其他步骤的表单... -->
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive } from 'vue';
import axios from 'axios';

export default defineComponent({
  name: 'TenantRegistration',
  setup() {
    const currentStep = ref(0);
    const loading = ref(false);
    const codeAvailable = ref(true);
    const suggestions = ref<string[]>([]);

    const formData = reactive({
      tenantCode: '',
      tenantName: '',
      // ... 其他字段
    });

    const checkAvailability = async () => {
      if (!formData.tenantCode) return;

      try {
        const response = await axios.get('/api/public/tenant/check-availability', {
          params: { code: formData.tenantCode },
        });

        codeAvailable.value = response.data.codeAvailable;
        suggestions.value = response.data.suggestions || [];
      } catch (error) {
        console.error('检查失败:', error);
      }
    };

    const submitRegistration = async () => {
      loading.value = true;
      try {
        const response = await axios.post('/api/public/tenant/register', formData);

        if (response.data.success) {
          // 处理注册成功
          window.location.href = response.data.accessUrl;
        }
      } catch (error) {
        console.error('注册失败:', error);
      } finally {
        loading.value = false;
      }
    };

    return {
      currentStep,
      formData,
      loading,
      codeAvailable,
      suggestions,
      checkAvailability,
      submitRegistration,
    };
  },
});
</script>
```

## 🧪 测试指南

### 1. 运行测试脚本

```bash
# 基础功能测试
node scripts/test-tenant-registration.js basic

# 并发注册测试
node scripts/test-tenant-registration.js concurrent

# 错误处理测试
node scripts/test-tenant-registration.js error

# 完整测试
node scripts/test-tenant-registration.js all
```

### 2. 使用Postman测试

导入API文档到Postman，按照以下顺序测试：

1. **检查可用性** → `GET /api/public/tenant/check-availability`
2. **获取计划** → `GET /api/public/tenant/subscription-plans`
3. **注册租户** → `POST /api/public/tenant/register`
4. **查询状态** → `GET /api/public/tenant/registration-status`
5. **邮箱验证** → `POST /api/public/tenant/verify`

### 3. 手动测试流程

1. 访问注册页面
2. 填写基本信息（确保租户代码唯一）
3. 选择订阅计划
4. 提交注册表单
5. 检查邮箱验证邮件
6. 使用管理员账户登录系统

## 🔧 部署配置

### 环境变量

```env
# 数据库配置
DATABASE_URL="postgresql://user:password@localhost:5432/flexihub"

# JWT配置
JWT_SECRET="your-jwt-secret-key"
JWT_EXPIRES_IN="24h"

# 应用配置
BASE_URL="https://app.flexihub.com"
NODE_ENV="production"

# 邮件配置
EMAIL_PROVIDER="smtp"
EMAIL_HOST="smtp.example.com"
EMAIL_PORT="587"
EMAIL_USER="<EMAIL>"
EMAIL_PASS="email-password"

# 存储配置
STORAGE_PROVIDER="local"
STORAGE_PATH="./uploads"
```

### Docker配置

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "run", "start:prod"]
```

### Nginx配置

```nginx
server {
    listen 80;
    server_name app.flexihub.com;

    location /api/public/tenant/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
}
```

## 🛠️ 故障排除

### 常见问题

1. **租户注册失败**

   - 检查数据库连接
   - 验证环境变量配置
   - 查看应用日志

2. **邮箱验证不工作**

   - 检查邮件服务配置
   - 确认SMTP设置正确
   - 查看邮件发送日志

3. **管理员登录失败**
   - 确认租户已激活
   - 检查JWT配置
   - 验证密码加密

### 调试命令

```bash
# 查看应用日志
docker logs flexihub-app

# 检查数据库连接
npm run db:check

# 验证配置
npm run config:validate

# 重置租户数据
npm run tenant:reset <tenant-code>
```

## 📚 相关文档

- [API文档](./API文档.md)
- [数据库设计](./数据库设计.md)
- [架构设计](./架构设计.md)
- [安全指南](./安全指南.md)
- [部署指南](./部署指南.md)

## 🤝 支持与反馈

如有问题或建议，请：

1. 查看[常见问题](./FAQ.md)
2. 提交[Issue](https://github.com/flexihub/issues)
3. 联系技术支持：<EMAIL>
4. 参与[社区讨论](https://community.flexihub.com)
