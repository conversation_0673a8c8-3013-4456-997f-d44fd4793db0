// 清除菜单缓存
const Redis = require('ioredis');
require('dotenv').config();

// 从.env文件获取Redis配置
const REDIS_ENABLED = process.env.REDIS_ENABLED === 'true';
const REDIS_HOST = process.env.REDIS_HOST || 'localhost';
const REDIS_PORT = parseInt(process.env.REDIS_PORT || '6379');
const REDIS_PASSWORD = process.env.REDIS_PASSWORD || '';
const REDIS_DB = parseInt(process.env.REDIS_DB || '0');

// 创建Redis客户端
async function createRedisClient() {
  if (!REDIS_ENABLED) {
    console.error('Redis未启用，请检查.env文件中的REDIS_ENABLED配置');
    return null;
  }

  try {
    const redis = new Redis({
      host: REDIS_HOST,
      port: REDIS_PORT,
      password: REDIS_PASSWORD || undefined,
      db: REDIS_DB,
    });

    // 测试连接
    await redis.ping();
    console.log('Redis连接成功');
    return redis;
  } catch (error) {
    console.error('Redis连接失败:', error.message);
    return null;
  }
}

// 清除菜单缓存
async function clearMenuCache() {
  const redis = await createRedisClient();
  if (!redis) return;

  try {
    // 获取所有菜单缓存键
    const keys = await redis.keys('menu:*');
    console.log(`找到 ${keys.length} 个菜单缓存键`);

    if (keys.length > 0) {
      // 删除所有菜单缓存
      const result = await redis.del(...keys);
      console.log(`成功删除 ${result} 个菜单缓存`);
    } else {
      console.log('没有找到菜单缓存');
    }
  } catch (error) {
    console.error('清除菜单缓存失败:', error.message);
  } finally {
    // 关闭Redis连接
    redis.quit();
  }
}

// 执行清除缓存
clearMenuCache();
