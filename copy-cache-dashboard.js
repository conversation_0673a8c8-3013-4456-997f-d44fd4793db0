/**
 * 复制缓存监控面板HTML文件到dist目录
 * 在构建后运行此脚本，确保HTML文件在正确的位置
 */
const fs = require('fs');
const path = require('path');

// 源文件和目标目录
const sourceFile = path.join(__dirname, 'src/core/cache/cache-metrics.html');
const targetDir = path.join(__dirname, 'dist/core/cache');
const targetFile = path.join(targetDir, 'cache-metrics.html');

// 确保目标目录存在
if (!fs.existsSync(targetDir)) {
  console.log(`创建目录: ${targetDir}`);
  fs.mkdirSync(targetDir, { recursive: true });
}

// 复制文件
try {
  fs.copyFileSync(sourceFile, targetFile);
  console.log(`成功复制文件: ${sourceFile} -> ${targetFile}`);
} catch (error) {
  console.error(`复制文件失败: ${error.message}`);
  process.exit(1);
}
