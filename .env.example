
NODE_ENV="development"
PORT=4000
CORS_ORIGINS="*"
PUBLIC_DATABASE_URL="********************************************************/database?schema=public"
DATABASE_URL="********************************************************/database?schema=tenant"
JWT_SECRET="clCx72LmRKKj0poJwj06apkzvnY64QYlN7IGO4OuVWg="
JWT_EXPIRES_IN="1d"
DEFAULT_ROLE_CODE="USER_100001"
DEFAULT_HOME_PATH="/dashboard"

# Redis配置
REDIS_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=12345678
REDIS_USER=
REDIS_DB=0
CACHE_TTL=7200000 # 2小时，单位毫秒


# 日志配置
LOG_LEVEL="debug" # 可选值: debug, info, warn, error
LOG_MAX_FILES="14d" # 保留日志文件的天数
LOG_MAX_SIZE="20m" # 单个日志文件的最大大小

# 跨域设置
CORS_ORIGINS=*