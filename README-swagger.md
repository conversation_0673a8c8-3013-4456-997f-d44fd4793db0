# Swagger增强功能使用指南

## 🎯 功能概述

本项目已完成Swagger文档系统的重大升级，主要包括：

1. **📄 Markdown导出** - 将API文档导出为Markdown格式
2. **🔧 统一返回格式显示** - Swagger正确显示系统的统一返回格式
3. **⚡ 装饰器简化** - 提供便捷的API响应装饰器

## 🚀 快速开始

### 1. 启动服务

```bash
npm run start:dev
```

### 2. 访问Swagger UI

打开浏览器访问：`http://localhost:3000/api/docs`

现在所有API都会显示完整的统一返回格式！

### 3. 导出API文档

#### Markdown格式

```bash
curl http://localhost:3000/api/system/swagger/markdown -o api-docs.md
```

#### JSON格式

```bash
curl http://localhost:3000/api/system/swagger/json -o api-docs.json
```

## 📝 开发者使用

### 在控制器中使用新装饰器

```typescript
import {
  ApiCompleteResponse,
  ApiCompletePaginationResponse,
  ApiListResponse,
} from '@/core/common/decorators/api-response.decorator';

@Controller('users')
@ApiTags('用户管理')
export class UserController {
  // 单个数据响应
  @Get(':id')
  @ApiOperation({ summary: '获取用户详情' })
  @ApiCompleteResponse(UserEntity, '获取用户成功')
  async findOne(@Param('id') id: string) {
    return this.userService.findOne(id);
  }

  // 分页数据响应
  @Get()
  @ApiOperation({ summary: '获取用户列表' })
  @ApiCompletePaginationResponse(UserEntity, '获取用户列表成功')
  async findAll(@Query() query: QueryUserDto) {
    return this.userService.findAll(query);
  }

  // 创建数据响应
  @Post()
  @ApiOperation({ summary: '创建用户' })
  @ApiCompleteResponse(UserEntity, '创建用户成功')
  async create(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }

  // 删除操作响应（无返回数据）
  @Delete(':id')
  @ApiOperation({ summary: '删除用户' })
  @ApiCompleteResponse(undefined, '删除用户成功')
  async remove(@Param('id') id: string) {
    await this.userService.remove(id);
    return { message: '删除成功' };
  }
}
```

## 📋 返回格式示例

### 成功响应（单个数据）

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "用户名",
    "email": "<EMAIL>"
  }
}
```

### 分页响应

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "items": [
      { "id": 1, "name": "用户1" },
      { "id": 2, "name": "用户2" }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  }
}
```

### 错误响应

```json
{
  "code": 404,
  "message": "用户不存在",
  "data": null
}
```

## 🔧 可用装饰器

| 装饰器                             | 用途                    | 示例         |
| ---------------------------------- | ----------------------- | ------------ |
| `@ApiCompleteResponse()`           | 标准成功响应 + 错误响应 | 获取单个资源 |
| `@ApiCompletePaginationResponse()` | 分页响应 + 错误响应     | 获取分页列表 |
| `@ApiListResponse()`               | 数组响应                | 获取普通列表 |
| `@ApiSuccessResponse()`            | 仅成功响应              | 特殊场景     |
| `@ApiErrorResponses()`             | 仅错误响应              | 特殊场景     |

## 📊 状态码说明

| 状态码 | 说明           |
| ------ | -------------- |
| 0      | 操作成功       |
| -1     | 操作失败       |
| 400    | 参数错误       |
| 401    | 未授权         |
| 403    | 禁止访问       |
| 404    | 资源不存在     |
| 500    | 服务器内部错误 |

## 🧪 测试功能

运行测试脚本：

```bash
./scripts/test-swagger-features.sh
```

该脚本会：

- 检查服务状态
- 测试Swagger UI访问
- 测试Markdown导出
- 测试JSON导出

## 🔗 相关链接

- **Swagger UI**: http://localhost:3000/api/docs
- **Markdown导出**: http://localhost:3000/api/system/swagger/markdown
- **JSON导出**: http://localhost:3000/api/system/swagger/json
- **详细说明**: [docs/swagger-improvements.md](docs/swagger-improvements.md)

## ❓ 常见问题

### Q: 为什么我的API没有显示统一返回格式？

A: 确保使用了新的API响应装饰器，如 `@ApiCompleteResponse()`

### Q: 如何自定义响应消息？

A: 在装饰器的第二个参数中指定，如 `@ApiCompleteResponse(UserDto, '自定义成功消息')`

### Q: 能否导出PDF格式的文档？

A: 目前只支持Markdown和JSON格式，PDF导出在后续版本中考虑添加

### Q: 导出的文档包含认证信息吗？

A: 导出的文档包含认证方式说明，但不包含具体的token信息

## 📞 支持

如有问题，请查看：

1. [详细说明文档](docs/swagger-improvements.md)
2. 项目Issue页面
3. 联系开发团队
